package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.DTO.inventory.InventoryMaxPrice;
import net.summerfarm.model.domain.PriceAdjustment;
import net.summerfarm.model.domain.ProductPriceAdjustment;
import net.summerfarm.model.vo.PriceAdjustRuleVo;
import net.summerfarm.model.vo.PriceAdjustVO;
import net.summerfarm.model.vo.price.PriceAdjustDetailVO;
import net.summerfarm.service.PriceAdjustService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Package: net.summerfarm.controller
 * @Description:　价格调整
 * @author: <EMAIL>
 * @Date: 2017/11/8
 */
@Api(tags = "价格调整")
@RestController
@RequestMapping("/price-adjust")
public class PriceAdjustController {
    private static final Logger logger = LoggerFactory.getLogger(PriceAdjustController.class);

    @Resource
    private PriceAdjustService priceAdjustService;

    @Resource
    private DynamicConfig dynamicConfig;


    /**
     * 分页查看
     *
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    @ApiOperation(value = "分页查看", httpMethod = "GET", tags = "价格调整")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true)
    })
    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sku/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectSku(@PathVariable int pageIndex, @PathVariable int pageSize, PriceAdjustVO selectKeys) {
        return priceAdjustService.selectSku(pageIndex, pageSize, selectKeys);
    }

    /**
     * 价格调整记录分页查看
     *
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    @ApiOperation(value = "价格调整记录分页查看", httpMethod = "GET", tags = "价格调整")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true)
    })
    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, PriceAdjustVO selectKeys) {
        return priceAdjustService.select(pageIndex, pageSize, selectKeys);
    }

    /**
     * 查看页面
     *
     * @param sku
     * @return
     */
    @ApiOperation(value = "查看页面", httpMethod = "GET", tags = "价格调整")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态，0、待处理，2待审批，3待执行，4已执行，5不调整", paramType = "query"),
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query"),
            @ApiImplicitParam(name = "poolId", value = "价格调整id", paramType = "query")
    })
    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(method = RequestMethod.GET)
    public AjaxResult select(String sku, String status, Integer areaNo, @RequestParam(required = false, name = "id") Integer poolId) {
        logger.info("状态为：{}", status);
        return priceAdjustService.selectSimple(sku, Integer.valueOf(status), areaNo, poolId);
    }



    /**
     * 查看页面(新模型)
     *
     * @param sku
     * @return
     */
    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/detail", method = RequestMethod.GET)
    public AjaxResult<PriceAdjustDetailVO> selectDetail(String sku, String status, Integer areaNo, @RequestParam(required = false, name = "id") Integer poolId) {
        return priceAdjustService.selectDetail(sku, Integer.valueOf(status), areaNo, poolId);
    }


    /**
     * 查看当前成本价是否走新模型
     *
     * @return
     */
    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/isNewModel", method = RequestMethod.GET)
    public CommonResult<Boolean> isNewCostPriceModel() {
        return CommonResult.ok(dynamicConfig.getMallNewCostPriceSwitch());
    }


    /**
     * 运营价格调整
     *
     * @param priceAdjustment
     * @return
     */
    @ApiOperation(value = "运营价格调整", httpMethod = "POST", tags = "价格调整")
    @RequiresPermissions(value = {"price-adjust:operation-save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/operation", method = RequestMethod.POST)
    public AjaxResult operationAdjustSave(PriceAdjustment priceAdjustment) {
        return priceAdjustService.operationAdjustSave(priceAdjustment);
    }

    /**
     * 采购调整
     *
     * @param priceAdjustment
     * @return
     */
    @ApiOperation(value = "采购调整", httpMethod = "POST", tags = "价格调整")
    @RequiresPermissions(value = {"price-adjust:purchase-save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchase", method = RequestMethod.POST)
    public AjaxResult purchaseAdjust(PriceAdjustment priceAdjustment) {
        return priceAdjustService.operationAdjustSave(priceAdjustment);
    }

    /**
     * 审核
     *
     * @return
     */
    @ApiOperation(value = "审核", httpMethod = "POST", tags = "价格调整")
    @RequiresPermissions(value = {"price-adjust:purchase-adjust", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public AjaxResult auditAdjust(PriceAdjustment priceAdjustment) {
        return priceAdjustService.auditAdjust(priceAdjustment);
    }

    /**
     * 取消调整
     *
     * @return
     */
    @ApiOperation(value = "取消调整", httpMethod = "POST", tags = "价格调整")
    @ApiImplicitParam(name = "id", value = "价格调整id", paramType = "query", required = true)
    @RequiresPermissions(value = {"price-adjust:cancel-adjust", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/cancel-adjust", method = RequestMethod.POST)
    public AjaxResult cancelAdjust(int id) {
        return priceAdjustService.cancelAdjust(id);
    }

    @ApiOperation(value = "查询入库剩余信息", httpMethod = "GET", tags = "价格调整")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query"),
            @ApiImplicitParam(name = "storeNo", value = "仓库编号", paramType = "query")
    })
    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/left-stock", method = RequestMethod.GET)
    public AjaxResult selectLeftInStock(Integer areaNo, String sku) {
        return priceAdjustService.selectLeftInStock(areaNo, sku);
    }

    @ApiOperation(value = "查看毛利率规则")
//    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectRule", method = RequestMethod.GET)
    public AjaxResult selectRule() {
        return priceAdjustService.selectRule();
    }


    @ApiOperation(value = "查看毛利率规则")
//    @RequiresPermissions(value = {"price-adjust:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/saveRule", method = RequestMethod.POST)
    public AjaxResult saveRule(@RequestBody PriceAdjustRuleVo priceAdjustRuleVo) {
        return priceAdjustService.saveRule(priceAdjustRuleVo);
    }


    @ApiOperation(value = "初始化周期库存成本数据")
    @RequestMapping(value = "/initialization")
    public AjaxResult initialization(){
        return priceAdjustService.initialization();
    }


    @ApiOperation(value = "定时任务")
    @RequestMapping(value = "/ruleTimedTask")
    public AjaxResult ruleTimedTask(){
        return priceAdjustService.ruleTimedTask();
    }

    /**
     * 查询某个sku在所有区域中的最高价格
     * @param sku 商品编号
     * @return sku最高价格
     */
    @PostMapping("/skuMaxPriceInAllArea/{sku}")
    public AjaxResult skuMaxPriceInAllArea(@PathVariable("sku") String sku){
        if(StringUtils.isEmpty(sku)){
            return AjaxResult.getErrorWithMsg("错误的sku");
        }
        BigDecimal skuMaxPrice = priceAdjustService.findSkuMaxPriceInAllArea(sku);
        if(skuMaxPrice == null){
            return AjaxResult.getErrorWithMsg(String.format("sku[%s]当前无最高售价", sku));
        }
        InventoryMaxPrice maxPrice = new InventoryMaxPrice();
        maxPrice.setMaxPrice(skuMaxPrice.toString());
        return AjaxResult.getOK(maxPrice);
    }


    @ApiOperation(value = "保存调价单")
    @RequiresPermissions(value = {"price-adjust:saveAdjustSheet", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/saveAdjustSheet",method = RequestMethod.POST)
    public AjaxResult saveAdjustSheet(@RequestBody List<ProductPriceAdjustment> productPriceAdjustmentList){

        return priceAdjustService.saveAdjustSheet(productPriceAdjustmentList);
    }
}
