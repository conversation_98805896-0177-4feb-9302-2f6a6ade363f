package net.summerfarm.controller;

import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.RedissonLockUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.FinancePurchaseInvoiceWallets;
import net.summerfarm.model.input.FinanceAccountStatementQuery;
import net.summerfarm.model.input.FinancePurchaseInvoiceWalletsInput;
import net.summerfarm.model.input.PurchaseInvoiceQuery;
import net.summerfarm.model.input.StockTaskWalletsInput;
import net.summerfarm.model.vo.InvoiceSupplierVO;
import net.summerfarm.model.vo.PurchaseInvoiceVO;
import net.summerfarm.service.PurchaseInvoiceService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 采购发票相关控制类
 *
 * <AUTHOR> 2021/08/04
 */
@RestController
@RequestMapping("/invoiceManagement")
public class InvoiceManagementController extends BaseService{

    @Resource
    private PurchaseInvoiceService purchaseInvoiceService;

    @Resource
    private RedisTemplate redisTemplate;

    public static final String KEY_ID = "lock:LockId:purchaseInvoiceService";

    @ApiOperation(value = "采购发票分页查询（待提交）", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:toBeSubmitted", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectToBeSubmitted/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectToBeSubmitted(@PathVariable int pageIndex, @PathVariable int pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery) {
        return purchaseInvoiceService.selectToBeSubmitted(pageIndex, pageSize, purchaseInvoiceQuery);
    }

    @ApiOperation(value = "采购发票分页查询（待匹配）", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:canMatch", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectCanMatch/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectCanMatch(@PathVariable int pageIndex, @PathVariable int pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery) {
        return purchaseInvoiceService.selectCanMatch(pageIndex, pageSize, purchaseInvoiceQuery);
    }

    @ApiOperation(value = "采购发票分页查询（票夹待复核）", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:toBeFiled", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectToBeFiled/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectToBeFiled(@PathVariable int pageIndex, @PathVariable int pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.selectToBeFiled(pageIndex, pageSize, financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "采购发票分页查询（票夹待归档）", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:toBeFiled", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectWaitingArchiving/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectWaitingArchiving(@PathVariable int pageIndex, @PathVariable int pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.selectWaitingArchiving(pageIndex, pageSize, financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "采购发票分页查询（票夹已归档）", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:archived", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectArchived/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectArchived(@PathVariable int pageIndex, @PathVariable int pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.selectArchived(pageIndex, pageSize, financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "采购发票信息导出", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/upload", method = RequestMethod.GET)
    public void upload(PurchaseInvoiceQuery purchaseInvoiceQuery) {
        purchaseInvoiceService.upload(purchaseInvoiceQuery);
    }

    @ApiOperation(value = "采购发票列表页含税金额总计(待提交、待匹配)", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/includedTaxAmount", method = RequestMethod.GET)
    public AjaxResult includedTaxAmount(PurchaseInvoiceQuery purchaseInvoiceQuery) {
        return purchaseInvoiceService.includedTaxAmount(purchaseInvoiceQuery);
    }

    @ApiOperation(value = "采购发票新增发票", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/save", method = RequestMethod.POST)
    public AjaxResult save(@RequestBody PurchaseInvoiceVO purchaseInvoiceVO) {
        return purchaseInvoiceService.save(purchaseInvoiceVO);
    }

    @ApiOperation(value = "检验发票编码", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/checkInvoice", method = RequestMethod.GET)
    public AjaxResult checkInvoice(String invoiceCode, String invoiceNumber) {
        return purchaseInvoiceService.checkInvoice(invoiceCode, invoiceNumber);
    }

    @ApiOperation(value = "采购发票新增发票模板下载", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/download", method = RequestMethod.GET)
    public void download(HttpServletResponse response) throws IOException {
        purchaseInvoiceService.download(response);
    }

    @ApiOperation(value = "【待匹配】采购发票批量导入模板下载", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchase-invoice/select-can-match/download", method = RequestMethod.POST)
    public AjaxResult selectCanMatchDownload()  {
        return purchaseInvoiceService.selectCanMatchDownload();
    }

    /**
     * 待提交批量导入发票
     * TODO zjx 和前端核实是否完全没有此处代码,核实范围,manage&srm及crm相关系统, 如果没有直接删除
     * @deprecated
     */
    @Deprecated
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/import", method = RequestMethod.POST)
    public AjaxResult importFile(@RequestParam("file") MultipartFile file) {
        Integer adminId = getAdminId();
        Long lockId = redisTemplate.opsForValue().increment(KEY_ID, 1L);
        String key = "importPurchaseInvoiceService" + adminId;
        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(key, String.valueOf(lockId), 5, TimeUnit.SECONDS);
        if (!tryLock) {
            return AjaxResult.getError("请先等待第一次操作导入数据完成，否则将清空第一次导入数据");
        }
        return purchaseInvoiceService.importFile(file);
    }

    @ApiOperation(value = "【待匹配】采购发票批量导入", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchase-invoice/select-can-match/batch-import", method = RequestMethod.POST)
    public AjaxResult batchImport(@RequestParam("file") MultipartFile file, Integer invoiceType, Integer invoiceForm) {
        Integer adminId = getAdminId();
        Long lockId = redisTemplate.opsForValue().increment(KEY_ID, 1L);
        String key = "batchImport" + adminId;
        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(key, String.valueOf(lockId), 5, TimeUnit.SECONDS);
        if (!tryLock) {
            return AjaxResult.getError("请先等待第一次操作导入数据完成，否则将清空第一次导入数据");
        }
        return purchaseInvoiceService.batchImport(file, invoiceType, invoiceForm);
    }


    @ApiOperation(value = "供应商列表", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectSupplier", method = RequestMethod.GET)
    public AjaxResult selectSupplier() {
        return purchaseInvoiceService.selectSupplier();
    }

    /**
     * 批量导入解析回查接口
     *
     *  TODO zjx 和前端核实是否完全没有此处代码,核实范围,manage&srm及crm相关系统, 如果没有直接删除
     * @deprecated
     * @return
     */
    @Deprecated
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/analysis/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult analysis(@PathVariable int pageIndex, @PathVariable int pageSize, Integer analysisType) {
        return purchaseInvoiceService.analysis(pageIndex, pageSize, analysisType);
    }

    @ApiOperation(value = "导入excel解析含税金额总计", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/analysis/includedTaxSum", method = RequestMethod.GET)
    public AjaxResult includedTaxSum(Integer analysisType) {
        return purchaseInvoiceService.includedTaxSum(analysisType);
    }

    @ApiOperation(value = "新增excel导入成功数据（待提交）", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/analysis/save", method = RequestMethod.GET)
    public AjaxResult analysisSave() {
        return purchaseInvoiceService.analysisSave();
    }

    @ApiOperation(value = "新增excel导入成功数据(待匹配)", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/newSave/save", method = RequestMethod.GET)
    public AjaxResult newSave() {
        return purchaseInvoiceService.newSave();
    }

    @ApiOperation(value = "采购发票新增发票批量导出解析失败excel", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/analysisFail", method = RequestMethod.GET)
    public void analysisFail() {
        purchaseInvoiceService.analysisFail();
    }

    @ApiOperation(value = "发票详情", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/check", method = RequestMethod.GET)
    public AjaxResult check(Integer purchaseInvoiceId, Integer status) {
        return purchaseInvoiceService.check(purchaseInvoiceId, status);
    }

    @ApiOperation(value = "采购发票修改发票", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/update", method = RequestMethod.POST)
    public AjaxResult update(@RequestBody PurchaseInvoiceVO purchaseInvoiceVO) {
        return purchaseInvoiceService.update(purchaseInvoiceVO);
    }

    @ApiOperation(value = "复核通过（归档修改）", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/reviewPassed", method = RequestMethod.POST)
    public AjaxResult reviewPassed(@RequestBody FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        RLock lock = RedissonLockUtil.tryLock("repass"+financePurchaseInvoiceWalletsInput.getId(), 0);
        try {
            return purchaseInvoiceService.reviewPassed(financePurchaseInvoiceWalletsInput);
        } finally {
            if(lock!=null && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    @ApiOperation(value = "回溯(发票从待匹配回到待提交)", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/backTracking", method = RequestMethod.GET)
    public AjaxResult backTracking(PurchaseInvoiceQuery purchaseInvoiceQuery) {
        return purchaseInvoiceService.backTracking(purchaseInvoiceQuery);
    }

    @ApiOperation(value = "回溯(票夹从待复核失效)", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/backTrackingWallets", method = RequestMethod.GET)
    public AjaxResult backTrackingWallets(Long id, Integer expenseType,String remark,String supplierId) {
        RLock lock =   null;
        if(Objects.nonNull(supplierId)){
             lock  = RedissonLockUtil.tryLock(String.format(WALLETS_UPSERT_LOCK, supplierId), -1);
        }else{
             lock = RedissonLockUtil.tryLock(String.format(WALLETS_UPSERT_LOCK, id), -1);
        }

        try {
            return purchaseInvoiceService.backTrackingWallets(id, expenseType,remark);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation(value = "供应商信息和其待匹配发票还有待匹配对账单信息", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectBySupplier", method = RequestMethod.GET)
    public AjaxResult selectBySupplier(Integer supplierId,String supplierName) {
        return purchaseInvoiceService.selectBySupplier(supplierId,supplierName);
    }

    @ApiOperation(value = "可匹配的采购发票", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectByInvoice/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectByInvoice(@PathVariable int pageIndex, @PathVariable int pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery) {
        return purchaseInvoiceService.selectByInvoice(pageIndex, pageSize, purchaseInvoiceQuery);
    }

    @ApiOperation(value = "可匹配的对账单", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/selectByBill/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectByBill(@PathVariable int pageIndex, @PathVariable int pageSize, FinanceAccountStatementQuery financeAccountStatementQuery) {
        return purchaseInvoiceService.selectByBill(pageIndex, pageSize, financeAccountStatementQuery);
    }

    @ApiOperation(value = "将采购发票与对账单关联", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/saveWalletsWithBill", method = RequestMethod.POST)
    public AjaxResult saveWalletsWithBill(@RequestBody FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        RLock lock = RedissonLockUtil.tryLock(String.format(WALLETS_UPSERT_LOCK, financePurchaseInvoiceWalletsInput.getSupplierId()), -1);
        try {
            return purchaseInvoiceService.saveWalletsWithBill(financePurchaseInvoiceWalletsInput);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    private static final String WALLETS_UPSERT_LOCK = "wallets_upsert_lock_%s";


    @ApiOperation(value = "展示票夹的设置内容", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/showWallets", method = RequestMethod.GET)
    public AjaxResult showWallets() {
        return purchaseInvoiceService.showWallets();
    }

    @ApiOperation(value = "修改票夹的设置内容", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/updateWallets", method = RequestMethod.POST)
    public AjaxResult updateWallets(@RequestBody FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.updateWallets(financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "检验发票的匹配情况", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/checkInvoiceWallets", method = RequestMethod.POST)
    public AjaxResult checkInvoiceWallets(@RequestBody FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.checkInvoiceWallets(financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "票夹详情", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/checkWallets", method = RequestMethod.GET)
    public AjaxResult checkWallets(Long id, Integer status, Integer expenseType) {
        return purchaseInvoiceService.checkWallets(id, status, expenseType);
    }

    @ApiOperation(value = "票夹入库单展示", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/showWarehousingOrder/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult showWarehousingOrder(@PathVariable int pageIndex, @PathVariable int pageSize, StockTaskWalletsInput stockTaskWalletsInput) {
        return purchaseInvoiceService.showWarehousingOrder(pageIndex, pageSize, stockTaskWalletsInput);
    }

    @ApiOperation(value = "票夹入库单对账单数据", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/show", method = RequestMethod.GET)
    public AjaxResult show(StockTaskWalletsInput stockTaskWalletsInput) {
        return purchaseInvoiceService.show(stockTaskWalletsInput);
    }

    @ApiOperation(value = "待复核票夹发票数据", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/showInvoice/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult showInvoice(@PathVariable int pageIndex, @PathVariable int pageSize, Long id) {
        return purchaseInvoiceService.showInvoice(pageIndex, pageSize, id);
    }

    @ApiOperation(value = "票夹含税金额总计", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/statisticsWallets", method = RequestMethod.GET)
    public AjaxResult statisticsWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.statisticsWallets(financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "已归档票夹导出", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/downloadWallets", method = RequestMethod.GET)
    public AjaxResult downloadWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.downloadWallets(financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "票夹匹配明细下载", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/downloadShow", method = RequestMethod.GET)
    public AjaxResult downloadShow(FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets) {
        return purchaseInvoiceService.downloadShow(financePurchaseInvoiceWallets);
    }

    /**
     * 已废弃，迁移值bms walletsQueryController
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    @ApiOperation(value = "票夹明细批量导出", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/allWalletsDetailedDownload", method = RequestMethod.GET)
    @Deprecated
    public AjaxResult allWalletsDetailedDownload(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return purchaseInvoiceService.allWalletsDetailedDownload(financePurchaseInvoiceWalletsInput);
    }

    @ApiOperation(value = "待匹配发票自动匹配", httpMethod = "GET", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaseInvoice/autoMatchInvoice", method = RequestMethod.GET)
    public AjaxResult autoMatchInvoice(Integer purchaseInvoiceId) {
        return purchaseInvoiceService.autoMatchInvoice(purchaseInvoiceId);
    }

    @ApiOperation(value = "发票销售方信息", httpMethod = "POST", tags = "采购发票")
    @RequiresPermissions(value = {"purchaseInvoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchase-invoice/invoice-seller-message", method = RequestMethod.POST)
    public AjaxResult<List<InvoiceSupplierVO>> invoiceSellerMessage(@RequestBody PurchaseInvoiceQuery purchaseInvoiceQuery) {
        return purchaseInvoiceService.invoiceSellerMessage(purchaseInvoiceQuery);
    }
}
