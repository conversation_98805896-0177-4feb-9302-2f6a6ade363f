package net.summerfarm.controller.srm;

import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.srm.SrmSupplierFinanceAccountStatement;
import net.summerfarm.model.input.FinanceAccountStatementInput;
import net.summerfarm.model.input.srm.SrmSupplierFinanceAccountStatementQuery;
import net.summerfarm.service.srm.SrmSupplierFinanceAccountStatementService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: SrmSupplierFinanceAccountStatementController
 * srm对账单
 * @date 2022/9/13 10:42
 */
@RestController
@RequestMapping("/srm-supplier-finance-account-statement")
public class SrmSupplierFinanceAccountStatementController {

    @Resource
    private SrmSupplierFinanceAccountStatementService srmSupplierFinanceAccountStatementService;

    @ApiOperation(value = "srm对账单列表", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/query-list", method = RequestMethod.POST)
    public AjaxResult queryList(@RequestBody SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        return srmSupplierFinanceAccountStatementService.queryList(srmSupplierFinanceAccountStatementQuery);
    }

    @ApiOperation(value = "SRM对账单详情", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/query-detail", method = RequestMethod.POST)
    public AjaxResult<SrmSupplierFinanceAccountStatement> queryDetail(@RequestBody SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement) {
        return srmSupplierFinanceAccountStatementService.queryDetail(srmSupplierFinanceAccountStatement);
    }

    @ApiOperation(value = "SRM对账单详情（出入库单详情）", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/purchase/query-detail", method = RequestMethod.POST)
    public AjaxResult purchaseQueryDetail(@RequestBody SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        return srmSupplierFinanceAccountStatementService.purchaseQueryDetail(srmSupplierFinanceAccountStatementQuery);
    }

    @ApiOperation(value = "SRM对账单详情(出入库单详情汇总)", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/purchase/query-details", method = RequestMethod.POST)
    public AjaxResult purchaseQueryDetails(@RequestBody SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        return srmSupplierFinanceAccountStatementService.purchaseQueryDetails(srmSupplierFinanceAccountStatementQuery);
    }

    @ApiOperation(value = "SRM供应商待匹配的发票信息", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/query-invoice-list", method = RequestMethod.POST)
    public AjaxResult queryInvoiceList(@RequestBody SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        return srmSupplierFinanceAccountStatementService.queryInvoiceList(srmSupplierFinanceAccountStatementQuery);
    }

    @ApiOperation(value = "SRM对账单匹配的发票信息", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/query-detail-invoice", method = RequestMethod.POST)
    public AjaxResult queryDetailInvoice(@RequestBody SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        return srmSupplierFinanceAccountStatementService.queryDetailInvoice(srmSupplierFinanceAccountStatementQuery);
    }

    @ApiOperation(value = "SRM对账单和发票匹配", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/match-account", method = RequestMethod.POST)
    public AjaxResult matchAccount(@RequestBody SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        return srmSupplierFinanceAccountStatementService.matchAccount(srmSupplierFinanceAccountStatementQuery);
    }

    @ApiOperation(value = "SRM供应商确认对账单", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/supplier/confirm", method = RequestMethod.POST)
    public AjaxResult supplierConfirm(@RequestBody SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        return srmSupplierFinanceAccountStatementService.supplierConfirm(srmSupplierFinanceAccountStatementQuery);
    }

}
