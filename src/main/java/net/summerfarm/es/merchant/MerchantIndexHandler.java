package net.summerfarm.es.merchant;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.es.EsIndexContext;
import net.summerfarm.common.util.es.dto.*;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.enums.MerchantEnum;
import net.summerfarm.es.EsClientPoolUtil;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.offline.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.OrderVO;
import net.summerfarm.model.vo.PoiVO;
import net.summerfarm.mq.DtsModel;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/23 15:16
 */
@Component
@Slf4j
public class MerchantIndexHandler implements MerchantIndex{

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantIndexHandler.class);

    public static final String REGISTER_NO_ORDER_30_DAY = "30天注册未下单";
    public static final String REGISTER_FIRST_ORDER_30_DAY = "30天注册首单";
    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private ContactMapper contactMapper;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Resource
    private FollowWhiteListMapper followWhiteListMapper;

    @Resource
    private CrmMerchantDayAttributeMapper crmMerchantDayAttributeMapper;

    @Resource
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MerchantLabelMapper merchantLabelMapper;
    @Resource
    private CustAfterDlvProfitLabelMapper custAfterDlvProfitLabelMapper;
    @Resource
    private FruitPopCustValueLableMapper fruitPopCustValueLableMapper;
    @Resource
    private CustMtdPerformanceMapper custMtdPerformanceMapper;

    @Override
    public void updateEsMerchantIndexBaseInfo(DtsModel dtsModel) {
        this.updateEsMerchantIndex(dtsModel, true);
    }

    // binlog要求有顺序性，异步线程池会导致消息顺序被破坏
    // @Async("asycExecutor")
    @Override
    public void updateEsMerchantIndex(DtsModel dtsModel) {
        this.updateEsMerchantIndex(dtsModel, false);
    }

    private void updateEsMerchantIndex(DtsModel dtsModel, boolean onlyBaseInfo) {
        List<Map<String, String>> data = dtsModel.getData();

        // 判断是否需要更新数据至ES,并执行更新
        if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), dtsModel.getType())) {
            this.checkHasValueAndUpdate(dtsModel, onlyBaseInfo);
            return;
        }
        log.info("更新商户索引数据信息:{}", FastJsonConverter.convert(dtsModel));
        if (Objects.equals(DtsModelTypeEnum.DELETE.name(), dtsModel.getType())) {
            List<Map<String, String>> oldData = dtsModel.getOld();
            for (Map<String, String> datum : oldData) {
                String mId = datum.get("m_id");
                LOGGER.info("商户:{}被删除,es商户索引同步删除;", mId);
                this.delDoc(mId);
            }
            return;
        }
        for (Map<String, String> datum : data) {
            datum = Optional.ofNullable(datum).orElse(new HashMap<>(0));
            String mId = datum.get("m_id");
            if (Objects.isNull(mId)) {
                continue;
            }
            // INSERT || UPDATE
            this.insertOrUpdateByMid(mId, datum, onlyBaseInfo);
        }
    }

    /**
     * 判断一个对象的属性是否有值
     * @param dtsModel 变更数据
     * @param onlyBaseInfo 是否只更新基础信息，true：只更新基础信息，false：更新全部信息
     */
    private void checkHasValueAndUpdate(DtsModel dtsModel, boolean onlyBaseInfo) {
        List<Map<String, String>> data = dtsModel.getData();
        if(CollectionUtil.isEmpty(data)){
            return;
        }
        List<Map<String, String>> old = dtsModel.getOld();
        if(CollectionUtil.isEmpty(old)){
            return;
        }

        // 一条sql更新数条信息,dts更新后的信息与更新前信息可能出现顺序不一致问题,无法校验,直接批量更新
        if(data.size() > 1){
            LOGGER.info("DTS数据变更过多,批量更新es文档数据");
            EsUpdate esUpdate = new EsUpdate();
            esUpdate.setIndexName(EsIndexContext.INDEX_MERCHANT_CRM);

            Map<String, Object> jsonList = new HashMap<>(data.size());
            for (Map<String, String> datum : data) {
                datum = Optional.ofNullable(datum).orElse(new HashMap<>(0));
                String mId = datum.get("m_id");
                if(Objects.isNull(mId)){
                    continue;
                }
                EsMerchantIndexDTO merchantIndexInfo = getMerchantIndexInfo(mId, onlyBaseInfo);
                if(Objects.isNull(merchantIndexInfo)){
                    // 数据库读写延迟,写入dts携带数据
                    merchantIndexInfo = retryUpdate(datum);
                }
                jsonList.put(mId, merchantIndexInfo);
            }
            esUpdate.setJsonList(jsonList);

            // 批量更新
            RestHighLevelClient client = null;
            try {
                client = EsClientPoolUtil.getClient();
                this.batchUpdateDoc(esUpdate,client);
            } catch (Exception e) {
                LOGGER.error("批量更新es文档失败:{}",Global.collectExceptionStackMsg(e));
            }finally {
                EsClientPoolUtil.returnClient(client);
            }
            return;
        }

        // 单条更新,校验是否具有更新字段
        Map<String, String> oldMap = old.get(0);
        String jsonString = JSON.toJSONString(oldMap);
        LOGGER.info("更新的字段:{}",jsonString);

        EsMerchantIndexDTO esMerchantIndexDTO = JSONObject.parseObject(jsonString, EsMerchantIndexDTO.class);
        if(Objects.isNull(esMerchantIndexDTO)){
            return;
        }
        Map<String, Object> stringObjectMap = EsUtil.entityToMap(esMerchantIndexDTO);
        for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
            if(Objects.nonNull(stringObjectEntry.getValue())){
                Map<String, String> map = data.get(0);
                String mId = map.get("m_id");
                if(Objects.nonNull(mId)){
                    this.insertOrUpdateByMid(mId,data.get(NumberUtils.INTEGER_ZERO), onlyBaseInfo);
                    break;
                }
            }
        }
    }

    /**
     * 数据库读写延时,使用dts数据进行同步
     * @param datum dts携带数据
     * @return es商户索引对象
     */
    private EsMerchantIndexDTO retryUpdate(Map<String, String> datum) {
        EsMerchantIndexDTO esMerchantIndexDTO = new EsMerchantIndexDTO();
        esMerchantIndexDTO.setmId(Long.valueOf(datum.get("m_id")));
        esMerchantIndexDTO.setMname(datum.get("mname"));
        esMerchantIndexDTO.setPhone(datum.get("phone"));
        esMerchantIndexDTO.setProvince(datum.get("province"));
        esMerchantIndexDTO.setCity(datum.get("city"));
        esMerchantIndexDTO.setArea(datum.get("area"));
        esMerchantIndexDTO.setAddress(datum.get("address"));
        esMerchantIndexDTO.setSize(datum.get("size"));
        try {
            String isLockStr = datum.get("islock");
            Integer isLock = Integer.valueOf(isLockStr);
            esMerchantIndexDTO.setIslock(isLock);
            String registerTime = datum.get("register_time");
            Date date = BaseDateUtils.string2Date(registerTime, BaseDateUtils.LONG_DATE_FORMAT);
            esMerchantIndexDTO.setRegisterTime(date);
            String areaNoStr = datum.get("area_no");
            Integer areaNo = Integer.valueOf(areaNoStr);
            esMerchantIndexDTO.setAreaNo(areaNo);
        } catch (Exception e) {
            LOGGER.error("es商户索引使用dts数据进行同步异常,参数:{},异常信息:{}",datum,Global.collectExceptionStackMsg(e));
        }
        LOGGER.info("es商户索引使用dts数据进行同步,参数:{}",esMerchantIndexDTO);
        return esMerchantIndexDTO;
    }

    /**
     * 不存在则 新增商户索引中的doc,存在则更新
     * @param mId 文档id
     * @param mId id不存在时的更新信息
     * @param onlyBaseInfo 是否只更新基础信息，true：只更新基础信息，false：更新全部信息
     */
    @Override
    public void insertOrUpdateByMid(String mId,Map<String, String> datum, boolean onlyBaseInfo) {
        EsMerchantIndexDTO esMerchantIndexDTO = getMerchantIndexInfo(mId, onlyBaseInfo);
        if(Objects.isNull(esMerchantIndexDTO)){
            esMerchantIndexDTO = retryUpdate(datum);
        }

        LOGGER.info("开始新增/更新 es文档数据,id:{}", mId);
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
            String merchantIndexInfoJsonStr = JSON.toJSONString(esMerchantIndexDTO);
            JSONObject jsonObject = JSONObject.parseObject(merchantIndexInfoJsonStr);

            UpdateRequest updateRequest = new UpdateRequest(EsIndexContext.INDEX_MERCHANT_CRM,mId);
            updateRequest.doc(jsonObject, XContentType.JSON);
            updateRequest.upsert(jsonObject, XContentType.JSON);
            // 版本冲突重试
            updateRequest.retryOnConflict(NumberUtils.INTEGER_THREE);
            client.update(updateRequest, RequestOptions.DEFAULT);
            LOGGER.info("新增/更新es文档数据结束,id:{}", mId);
        } catch (Exception e) {
            LOGGER.error("新增/更新 es文档数据失败,mId:{},{}",mId,Global.collectExceptionStackMsg(e));
        }finally {
            EsClientPoolUtil.returnClient(client);
        }
    }

    /**
     * 删除商户索引中的doc
     * @param mId 文档id
     */
    private void delDoc(String mId) {
        EsUpdate update = new EsUpdate(EsIndexContext.INDEX_MERCHANT_CRM,mId,null,null);
        try {
            EsUtil.delDoc(update);
        } catch (Exception e) {
            LOGGER.error("删除索引失败,mId:{},{}",mId,Global.collectExceptionStackMsg(e));
        }
    }

    /**
     * 获取商户数据
     * @param mIdStr 商户mIdString
     * @param onlyBaseInfo 是否只获取基础信息，true：只获取基础信息，false：获取全部信息
     *
     * @return 商户索引信息 注意: 该方法可能返回null
     */
    private EsMerchantIndexDTO getMerchantIndexInfo(String mIdStr, boolean onlyBaseInfo){
        Long mId = Long.valueOf(mIdStr);
        // 获取信息
        List<EsMerchantIndexDTO> merchantIndexInfo = merchantMapper.queryMerchantWithContactToEs(mId, 0, 1);
        if(CollectionUtil.isEmpty(merchantIndexInfo)){
            LOGGER.info("mid:{},数据库读写未同步,时间:{}",mIdStr,System.currentTimeMillis());
            // 返回null
            return null;
        }
        EsMerchantIndexDTO esMerchantIndexDTO = merchantIndexInfo.get(0);
        if (onlyBaseInfo) {
            FollowUpRelation followUpRelation = followUpRelationMapper.selectLastFollowOne(esMerchantIndexDTO.getBdId(), esMerchantIndexDTO.getmId().intValue());
            if (followUpRelation != null && followUpRelation.getCareBdId() != null) {
                esMerchantIndexDTO.setCareBdId(followUpRelation.getCareBdId().longValue());
            }
            return esMerchantIndexDTO;
        }

        this.selectOtherInfo(esMerchantIndexDTO);
        return esMerchantIndexDTO;
    }

    /**
     * 获取其他纬度商户信息
     * @param esMerchantIndexDTO 商户信息
     */
    private void selectOtherInfo(EsMerchantIndexDTO esMerchantIndexDTO){
        // 联系人信息
        List<EsContactDTO> esContactList = contactMapper.selectEsContactByMId(esMerchantIndexDTO.getmId());

        esContactList.forEach(el -> {
            PoiVO poi = SplitUtils.string2poi(el.getPoiNote());
            if(Objects.nonNull(poi)){
                el.setPoi(new Poi(poi.getLon(), poi.getLat()));
            }
        });
        esMerchantIndexDTO.setContacts(esContactList);

        // 白名单标识
        FollowWhiteList followWhiteList = followWhiteListMapper.queryFollowWhiteListOne(esMerchantIndexDTO.getmId());
        Integer merchantWhiteListTag = Objects.isNull(followWhiteList) ? 0 : 1;
        esMerchantIndexDTO.setMerchantWhiteListTag(merchantWhiteListTag);

        // 未完结的省心送标识,核心客户标识,总gmv,生命周期,下单频率,未拜访天数,未下单天数,首单客户,30天注册为下单
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_ATTRIBUTE.getTableName());
        CrmMerchantDayAttribute crmMerchantDayAttribute = crmMerchantDayAttributeMapper.selectByPrimaryKey(esMerchantIndexDTO.getmId(), information.getDateFlag());
        if (Objects.nonNull(crmMerchantDayAttribute)) {
            BeanCopyUtil.copyProperties(crmMerchantDayAttribute, esMerchantIndexDTO);
        }

        // 客户标签
        DataSynchronizationInformation tableName = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        List<String> labelList = crmMerchantDayLabelMapper.selectByPrimaryKey(esMerchantIndexDTO.getmId(), tableName.getDateFlag());
        if (CollectionUtil.isNotEmpty(labelList)) {
            //检查是否下过单
            if (esMerchantIndexDTO.getLastOrderTime() != null) {
                labelList.remove(REGISTER_NO_ORDER_30_DAY);
            }
            //检查是否是首单
            List<OrderVO> orderVOS = ordersMapper.selectOrderStatusLimit2(esMerchantIndexDTO.getmId());
            if (orderVOS.size() == 2) {
                labelList.remove(REGISTER_FIRST_ORDER_30_DAY);
            }
            esMerchantIndexDTO.setMerchantLabel(labelList);
        }
        List<String> merchantLabels = merchantLabelMapper.selectNameByMid(esMerchantIndexDTO.getmId());
        esMerchantIndexDTO.setOfficialWechatFlag(merchantLabels.contains("已加官微") ? 0 : 1);
        esMerchantIndexDTO.setBdWechatFlag(merchantLabels.contains("已加销微") ? 0 : 1);

        FollowUpRelation followUpRelation = followUpRelationMapper.selectLastFollowOne(esMerchantIndexDTO.getBdId(), esMerchantIndexDTO.getmId().intValue());
        if (followUpRelation != null) {
            esMerchantIndexDTO.setCareBdId(followUpRelation.getCareBdId().longValue());
        }

        // 客户名称是否包含英文
        String mNameEn = SplitUtils.removeChineseInStr(esMerchantIndexDTO.getMname());
        if(StringUtils.isNotBlank(mNameEn)){
            esMerchantIndexDTO.setMnameEn(mNameEn);
        }

        if (MerchantEnum.BusinessLineEnum.POP.getCode().equals(esMerchantIndexDTO.getBusinessLine())) {
            // pop客户生命周期
            FruitPopCustValueLable popCustValueLable = fruitPopCustValueLableMapper.selectOneByCustId(esMerchantIndexDTO.getmId());
            if (popCustValueLable != null) {
                esMerchantIndexDTO.setLifecycle(popCustValueLable.getCustValueLable());
            } else {
                esMerchantIndexDTO.setLifecycle(null);
            }
        } else {
            // 客户价值标签
            CustAfterDlvProfitLabel custAfterDlvProfitLabel = custAfterDlvProfitLabelMapper.selectOneByCustIdAndNoSaas(esMerchantIndexDTO.getmId());
            String valueLabel = custAfterDlvProfitLabel == null ? "B" : custAfterDlvProfitLabel.getDlvProfitGroup();
            esMerchantIndexDTO.setValueLabel(valueLabel);
        }

        // 高价值客户标签
        String highValueLabel = custMtdPerformanceMapper.selectOneCustValueLableByCustId(esMerchantIndexDTO.getmId());
        esMerchantIndexDTO.setHighValueLabel(highValueLabel);

    }

    @Override
    public void batchUpdateMerchantIndex(Integer startPage,Integer pageSize) {
        // 批量更新
        if (startPage==null){
            startPage=1;
        }
        if (pageSize==null){
            pageSize=500;
        }

        EsUpdate esUpdate = new EsUpdate();
        esUpdate.setIndexName(EsIndexContext.INDEX_MERCHANT_CRM);
        RestHighLevelClient client = null;

        try {
            client = EsClientPoolUtil.getClient();
            while (true) {
                Map<String, Object> jsonList = new HashMap<>(pageSize);
                List<EsMerchantIndexDTO> esMerchantIndexDTOList = getEsMerchantIndexDTOList(startPage, pageSize, jsonList);
                if (CollectionUtil.isEmpty(esMerchantIndexDTOList)) {
                    return;
                }
                esUpdate.setJsonList(jsonList);
                // 批量更新
                this.batchUpdateDoc(esUpdate,client);
                if (esMerchantIndexDTOList.size() < pageSize) {
                    LOGGER.info("结束更新");
                    break;
                }
                startPage++;
            }
        }catch (Exception e) {
            LOGGER.error("批量更新失败:{}",Global.collectExceptionStackMsg(e));
        }finally {
            EsClientPoolUtil.returnClient(client);
        }
    }

    /**
     * 批量更新文档
     * @param esUpdate es更新对象
     */
    private void batchUpdateDoc(EsUpdate esUpdate,RestHighLevelClient client){
        esUpdate = Optional.ofNullable(esUpdate).orElse(new EsUpdate());
        LOGGER.info("开始批量更新文档,索引:{},数量:{}",esUpdate.getIndexName(),esUpdate.getJsonList().size());

        BulkRequest request = new BulkRequest();
        try {
            for (Map.Entry<String, Object> entry : esUpdate.getJsonList().entrySet()){
                String merchantIndexInfoJsonStr = JSON.toJSONString(entry.getValue());
                UpdateRequest updateRequest = new UpdateRequest(esUpdate.getIndexName(), entry.getKey());
                updateRequest.doc(merchantIndexInfoJsonStr, XContentType.JSON);
                updateRequest.upsert(merchantIndexInfoJsonStr, XContentType.JSON);
                request.add(updateRequest);
            }

            BulkResponse bulk = client.bulk(request, RequestOptions.DEFAULT);
            // 是否失败
            if(bulk.hasFailures()){
                LOGGER.error("批量更新文档失败,失败信息:{}",bulk.buildFailureMessage());
            }
            LOGGER.info("批量更新文档结束,索引:{},更新数量:{}",esUpdate.getIndexName(),esUpdate.getJsonList().size());
        }catch (Exception e){
            LOGGER.error("批量更新文档异常,堆栈信息:{},req:{}",Global.collectExceptionStackMsg(e),JSONUtil.toJsonStr(request));
        }
    }


    @Nullable
    private List<EsMerchantIndexDTO> getEsMerchantIndexDTOList(int startPage, int pageSize, Map<String, Object> jsonList) {
        List<EsMerchantIndexDTO> esMerchantIndexDTOList = merchantMapper.queryMerchantWithContactToEs(null, (startPage - 1) * pageSize, pageSize);
        if (CollectionUtil.isEmpty(esMerchantIndexDTOList)) {
            return null;
        }

        // 准备数据
        for (EsMerchantIndexDTO esMerchantIndexDTO : esMerchantIndexDTOList) {
            this.selectOtherInfo(esMerchantIndexDTO);
            jsonList.put(esMerchantIndexDTO.getmId().toString(), esMerchantIndexDTO);
        }
        return esMerchantIndexDTOList;
    }

    @Override
    public void initDeleteEsMerchantIndex() {
        try {
            EsUtil.delAllDoc(EsIndexContext.INDEX_MERCHANT_CRM);
        } catch (Exception e) {
            LOGGER.error("初始化,删除数据失败:{}",Global.collectExceptionStackMsg(e));
        }
    }

    @Async
    @Override
    public void initInsertEsMerchantIndex() {
        // 批量新增
        int startPage = 1;
        int pageSize = 1000;

        EsInsert esInsert = new EsInsert();
        esInsert.setIndexName(EsIndexContext.INDEX_MERCHANT_CRM);

        try {
            while (true) {
                Map<String, Object> jsonList = new HashMap<>(pageSize);
                List<EsMerchantIndexDTO> esMerchantIndexDTOList = getEsMerchantIndexDTOList(startPage, pageSize, jsonList);
                if (CollectionUtil.isEmpty(esMerchantIndexDTOList)) {
                    return;
                }

                esInsert.setJsonList(jsonList);
                LOGGER.info("开始执行更新程序");
                EsUtil.batchAddDoc(esInsert);


                if (esMerchantIndexDTOList.size() < pageSize) {
                    LOGGER.info("结束更新");
                    break;
                }
                startPage++;
            }
        }catch (Exception e) {
            LOGGER.error("索引初始化失败:{}",Global.collectExceptionStackMsg(e));
        }
    }
}
