package net.summerfarm.biz.finance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: George
 * @date: 2024-05-10
 **/
@Getter
public class FinancialInvoiceAsyncTaskEnum {

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    public enum Status {
        INIT(0,"初始化"),

        FINISHED(1,"已完成"),

        FAIL(2,"失败"),

        PROCESSING(3,"处理中"),

        ;

        /**
         * 任务结果
         */
        private Integer result;

        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public enum TaskType {

        BLUE(0, "蓝票"),

        RED(1, "红票"),

        INVALID(2, "作废"),

        DOWNLOAD(3, "下载"),

        RED_INVOICE_PREPARATION(4, "红字发票准备单"),

        INVALID_INVOICE_PREPARATION(5, "作废红字发票准备单"),

        RED_INVOICE_QUERY(6, "红字发票查询"),
        ;

        private Integer type;

        private String desc;
    }
}
