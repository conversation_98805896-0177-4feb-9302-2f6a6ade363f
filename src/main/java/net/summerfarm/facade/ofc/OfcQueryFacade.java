package net.summerfarm.facade.ofc;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.ContactMapper;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.provider.DeliveryInfoQueryProvider;
import net.summerfarm.ofc.client.req.CloseTimeQueryReq;
import net.summerfarm.ofc.client.req.DeliveryDateQueryReq;
import net.summerfarm.ofc.client.resp.CloseTimeQueryResp;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfcQueryFacade {

    @DubboReference
    private DeliveryInfoQueryProvider deliveryInfoQueryProvider;

    @Resource
    private ContactMapper contactMapper;

    public DeliveryDateQueryResp queryDeliveryDate(LocalDateTime orderTime, Long merchantId, Long contactId, LocalDate queryBeginDate, LocalDate queryEndDate, OfcOrderSourceEnum source, List<String> skuList) {
        //查询用户地址信息
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        if (Objects.isNull(contact)) {
            throw new BizException("地址信息异常！");
        }

        //封装入参
        DeliveryDateQueryReq deliveryDateQueryReq = new DeliveryDateQueryReq();
        deliveryDateQueryReq.setCity(contact.getCity());
        deliveryDateQueryReq.setArea(contact.getArea());
        deliveryDateQueryReq.setStoreId(contact.getContactId());
        deliveryDateQueryReq.setPayTime(orderTime);
        deliveryDateQueryReq.setQueryBeginDate(queryBeginDate);
        deliveryDateQueryReq.setQueryEndDate(queryEndDate);
        deliveryDateQueryReq.setSkuCodeList(skuList);
        deliveryDateQueryReq.setMerchantId(merchantId);
        deliveryDateQueryReq.setSource(source);

        log.info("OFC查询履约配送日期 >>> {}", JSON.toJSONString(deliveryDateQueryReq));

        DubboResponse<DeliveryDateQueryResp> dubboResponse = deliveryInfoQueryProvider.queryDeliveryDate(deliveryDateQueryReq);
        if (Objects.isNull(dubboResponse) || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            log.error("queryDeliveryDate[]getInfo[]error[]queryDeliveryDate:{}", JSON.toJSONString(dubboResponse));
            throw new BizException(dubboResponse.getMsg());
        }

        log.info("OFC查询履约配送日期 >>> {}", JSON.toJSONString(dubboResponse.getData()));
        return dubboResponse.getData();
    }


    public LocalTime queryCloseTime(Long contactId, OfcOrderSourceEnum source) {
        //查询用户地址信息
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        if (Objects.isNull(contact)) {
            throw new BizException("地址信息异常！");
        }
        //封装入参
        CloseTimeQueryReq closeTimeQueryReq = new CloseTimeQueryReq();
        closeTimeQueryReq.setCity(contact.getCity());
        closeTimeQueryReq.setArea(contact.getArea());
        closeTimeQueryReq.setStoreId(contact.getContactId());
        closeTimeQueryReq.setSource(source);

        DubboResponse<CloseTimeQueryResp> dubboResponse = deliveryInfoQueryProvider.queryCloseTime(closeTimeQueryReq);
        if (Objects.isNull(dubboResponse) || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            log.error("queryCloseTime[]getInfo[]error[]queryCloseTime:{}", JSON.toJSONString(dubboResponse));
            throw new BizException(dubboResponse.getMsg());
        }

        return dubboResponse.getData().getCloseTime();
    }
}
