package net.summerfarm.facade.tms.converter;


import net.summerfarm.facade.tms.input.DeliveryRuleQueryInput;
import net.summerfarm.wnc.client.req.DeliveryRuleQueryReq;

/**
 * Description: <br/>
 * date: 2023/3/10 10:47<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryRuleConverter {

    public static DeliveryRuleQueryReq input2Req(DeliveryRuleQueryInput deliveryRuleQueryInput) {
        if (deliveryRuleQueryInput == null) {
            return null;
        }
        DeliveryRuleQueryReq req = new DeliveryRuleQueryReq();
        req.setOrderTime(deliveryRuleQueryInput.getOrderTime());
        req.setMerchantId(deliveryRuleQueryInput.getMerchantId());
        req.setContactId(deliveryRuleQueryInput.getContactId());
        req.setCity(deliveryRuleQueryInput.getCity());
        req.setArea(deliveryRuleQueryInput.getArea());
        req.setQueryBeginDate(deliveryRuleQueryInput.getQueryBeginDate());
        req.setQueryEndDate(deliveryRuleQueryInput.getQueryEndDate());
        req.setSource(deliveryRuleQueryInput.getSource());

        return req;
    }
}
