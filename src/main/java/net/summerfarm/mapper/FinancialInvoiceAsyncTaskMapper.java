package net.summerfarm.mapper;

import net.summerfarm.model.domain.FinancialInvoiceAsyncTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @description: 发票异步任务
* @author: George
* @date: 2024-05-10
**/
public interface FinancialInvoiceAsyncTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinancialInvoiceAsyncTask record);

    int insertSelective(FinancialInvoiceAsyncTask record);

    FinancialInvoiceAsyncTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FinancialInvoiceAsyncTask record);

    int updateByPrimaryKey(FinancialInvoiceAsyncTask record);

    List<FinancialInvoiceAsyncTask> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据发票id和类型查询任务
     * 因为可能一个发票会先开票后作废，再重新开票，所以需要根据发票id和类型查询最后一次任务
     * @param invoiceId
     * @return
     */
    FinancialInvoiceAsyncTask queryLastByInvoiceIdAndType(@Param("invoiceId") Long invoiceId, @Param("type") Integer type);

    /**
     * 查询待执行的任务
     * @param maxInvokeCount
     */
    List<FinancialInvoiceAsyncTask> queryWaitInvokeTasks(@Param("maxInvokeCount") Integer maxInvokeCount, @Param("limit") Integer limit);

    /**
     * 更新任务结果
     * @param id
     * @param result
     */
    void updateTaskResult(@Param("id") Long id, @Param("taskResult") Integer result);

    /**
     * 清除taskId
     * @param id
     */
    void removeTaskId(@Param("id") Long id);
}