package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchaseInvoice;
import net.summerfarm.model.input.PurchaseInvoiceQuery;
import net.summerfarm.model.vo.PurchaseInvoiceResultVO;
import net.summerfarm.model.vo.PurchaseInvoiceVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface PurchaseInvoiceMapper {

    /**
     * 分页查询
     * @param purchaseInvoiceQuery
     * @return
     */
    List<PurchaseInvoiceVO> selectList(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 待匹配
     * @param purchaseInvoiceQuery
     * @return
     */
    PurchaseInvoiceResultVO sumMatching(PurchaseInvoiceQuery purchaseInvoiceQuery);



    /**
     * 待提交分页查询
     * @param purchaseInvoiceQuery
     * @return
     */
    List<PurchaseInvoiceVO> selectListWait(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 待提交
     * @param purchaseInvoiceQuery
     * @return
     */
    PurchaseInvoiceResultVO sumWaiting(PurchaseInvoiceQuery purchaseInvoiceQuery);


    /**
     * 含税金额总计
     * @param purchaseInvoiceQuery
     * @return
     */
    PurchaseInvoiceResultVO selectListSum(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 根据发票id查询发票信息
     * @param id
     * @return
     */
    PurchaseInvoiceVO queryByInvoiceId(Integer id);

    /**
     * 查询发票编码是否已经存在
     * @param invoiceCode
     * @param invoiceNumber
     * @return
     */
    int selectInvoiceCodes(String invoiceCode, String invoiceNumber);

    /**
     * 查找Id
     * @param invoiceCode
     * @param invoiceNumber
     * @return
     */
    List<PurchaseInvoice> selectInvoiceId(String invoiceCode, String invoiceNumber);

    /**
     * 新增采购发票
     * @param purchaseInvoiceVO
     * @return
     */
    int insertSelective(PurchaseInvoiceVO purchaseInvoiceVO);

    /**
     * 根据id查询该发票是否存在(待提交状态)
     * @param id
     * @return
     */
    int selectByPrimaryKey(Integer id);

    /**
     * 根据id查询该发票是否存在
     * @param id
     * @return
     */
    int selectByKey(Integer id);

    /**
     * 查询待提交状态的发票详情
     * @param purchaseInvoiceId
     * @param status
     * @return
     */
    PurchaseInvoiceVO selectDetailByFirst(Integer purchaseInvoiceId, Integer status);

    /**
     * 查询可匹配状态的发票详情
     * @param purchaseInvoiceId
     * @param status
     * @return
     */
    PurchaseInvoiceVO selectDetailBySecond(Integer purchaseInvoiceId, Integer status);

    /**
     * 查询发票明细
     * @param id
     * @return
     */
    PurchaseInvoice selectById(Integer id);

    /**
     * 查询发票明细
     * @param id
     * @return
     */
    PurchaseInvoiceVO selectByVO(Integer id);

    /**
     * 文件地址
     * @param purchaseInvoiceId
     * @param status
     * @return
     */
    List<String> fileAddress(Integer purchaseInvoiceId, Integer status);

    /**
     * 修改发票
     * @param purchaseInvoiceVO
     * @return
     */
    int updateByPrimaryKeySelective(PurchaseInvoiceVO purchaseInvoiceVO);


    /**
     * 全电类型发票 发票代码字段为NUll
     * @param id
     * @return
     */
    int updateInvoiceEmptyByQuanDian(Integer id);

    /**
     * 修改发票
     * @param purchaseInvoiceVO
     * @return
     */
    int updateByPrimaryKey(PurchaseInvoiceVO purchaseInvoiceVO);

    /**
     * 修改发票的实际抵扣税额
     * @param purchaseInvoice
     * @return
     */
    int updateByActual(PurchaseInvoice purchaseInvoice);

    /**
     * 删除采购发票
     * @param id
     * @return
     */
    int delete(Integer id);

    /**
     * 查询待匹配的发票
     * @param purchaseInvoiceQuery
     * @return
     */
    List<PurchaseInvoiceVO> selectByTaxNumber(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 查询发票是否已经被匹配
     * @param id
     * @return
     */
    PurchaseInvoice selectWallets(Integer id);

    /**
     * 查询发票信息
     * @param id
     * @return
     */
    PurchaseInvoiceVO select(Integer id);

    /**
     * 发票匹配票夹
     * @param purchaseInvoice
     * @return
     */
    int update(PurchaseInvoice purchaseInvoice);

    /**
     * 查询票价中最新时间的发票
     * @param walletsId
     * @return
     */
    PurchaseInvoiceVO selectSubmissionTime(Long walletsId);

    /**
     * 根据票夹id查询发票
     * @param walletsId
     * @return
     */
    List<PurchaseInvoiceVO> selectAll(Long walletsId);

    /**
     * 根据票夹id分页查询数据
     * @param walletsId
     * @return
     */
    List<PurchaseInvoiceVO> selectByAdd(Long walletsId);

    /**
     * 解除发票和票夹的匹配
     * @param walletsId
     * @return
     */
    int updateByWalletsId(Long walletsId);

    /**
     * 解除发票和票夹的匹配
     * @param id
     * @return
     */
    int updateById(Integer id);

    /**
     * 统计票夹发票的可抵扣税额（蓝）
     * @param walletsId
     * @return
     */
    BigDecimal addDeductibleTax(Long walletsId);


    /**
     * 统计票夹发票的可抵扣税额(红）
     * @param walletsId
     * @return
     */
    BigDecimal addDeductibleTaxRed(Long walletsId);

    /**
     * 统计票夹发票的含税总额、实际可抵扣税额总额、不含税总额
     * 蓝色
     * @param walletsId
     * @return
     */
    PurchaseInvoiceVO selectData(Long walletsId);

    /**
     * 统计票夹发票的含税总额、实际可抵扣税额总额、不含税总额
     * 红色
     * @param walletsId
     * @return
     */
    PurchaseInvoiceVO selectRedInvoiceData(Long walletsId);


    /**
     * 根据发票id列表查询数据
     * @param invoiceIdList
     */
    List<PurchaseInvoiceVO> selectByIdList(@Param("invoiceIdList") List<Integer> invoiceIdList);

}