package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@ApiModel(description = "商户实体类")
public class Merchant implements Serializable {

    @ApiModelProperty(value = "商户id")
    @Null(message = "invertory.invId", groups = {Add.class, Update.class, MerchantSubAccount.class})
    private Long mId;

    @ApiModelProperty(value = "商户名称")
    @NotNull(message = "merchant.name.null", groups = {Add.class,Update.class})
    private String mname;

    @ApiModelProperty(value = "主联系人")
    @NotNull(message = "linkman.null", groups = {Add.class,Update.class})
    private String mcontact;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "手机号")
    @NotNull(message = "phone.null", groups = {Add.class,Update.class})
    private String phone;

    @ApiModelProperty(value = "审核状态:0审核通过、1待审核、2审核未通过、3拉黑")
    private Byte islock;

    @ApiModelProperty(value = "等级")
    private Byte rankId;

    @ApiModelProperty(value = "注册时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date registerTime;

    @ApiModelProperty(value = "登录时间")
    private Date loginTime;

    @ApiModelProperty(value = "6位邀请码")
    private String invitecode;

    @ApiModelProperty(value = "用户分享码")
    private String inviterChannelCode;

    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    @ApiModelProperty(value = "审核人")
    private Integer auditUser;

    @ApiModelProperty(value = "审核人")
    private String auditUserName;

    @ApiModelProperty(value = "营业执照路径")
    private String businessLicense;

    @ApiModelProperty(value = "店铺招牌")
    private String shopSign;

    @ApiModelProperty(value = "其他证明照片")
    private String otherProof;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "商家腾讯地图坐标")
    private String poiNote;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "上次下单时间")
    private Date lastOrderTime;

    //所属二级城市No
    @ApiModelProperty(value = "所属二级城市No")
    private Integer areaNo;

    @ApiModelProperty(value = "大客户、大连锁、小连锁、单店")
    private String size;

    @ApiModelProperty(value = "客户类型")
    @NotNull(message = "type is null", groups = {Add.class,Update.class})
    private String type;

    @ApiModelProperty(value = "商圈")
    private String tradeArea;

    @ApiModelProperty(value = "商圈组")
    private String tradeGroup;

    @ApiModelProperty(value = "unionid")
    private String unionid;

    @ApiModelProperty(value = "小程序openid")
    private String mpOpenid;

    @ApiModelProperty(value = "1是直营 2是加盟")
    private Integer direct;

    @ApiModelProperty(value = "1定量展示 2全量展示")
    private Integer skuShow;

    @ApiModelProperty(value = "所属于大客户的id")
    private Integer adminId;

    @ApiModelProperty(value = "1服务区内 2服务区外")
    private Integer server;

    @ApiModelProperty(value = "会员当月积分")
    private BigDecimal memberIntegral;

    @ApiModelProperty(value = "会员等级")
    private Integer grade;

    @ApiModelProperty(value = "余额")
    private BigDecimal rechargeAmount;

    private BigDecimal cashAmount;

    private Date cashUpdateTime;

    private LocalDateTime mergeTime;

    private String mergeAdmin;

    private String channelCode;

    @ApiModelProperty(value = "配送单是否展示价格信息")
    private Boolean showPrice;

    private Integer changePop;

    @ApiModelProperty(value = "拉黑备注")
    private String pullBlackRemark;

    @ApiModelProperty(value = "拉黑操作人")
    private String pullBlackOperator;

    /**
    * 门牌号
    */
    private String houseNumber;

    /**
    * 企业规模
    */
    private String enterpriseScale;

    /**
    * 公司品牌
    */
    private String companyBrand;

    /** 外部编码 */
    private String outerNo;

    /**
    * 是否选择了线索池 0 不是 1 是
    */
    private Integer cluePool;

    /**
    * 大客户类型 ka， 批发大客户
    */
    private String merchantType;

    /**
    * 审核类型
    */
    private Integer examineType;

    @ApiModelProperty(value = "开关状态 0 开（展示） 1 关（不展示）")
    private Integer displayButton;
    /**
     * 免邮日
     */
    private String freeDay;
    /**
     * 0,正常经营 1,倒闭
     */
    private Integer operateStatus;

    /**
     * 更新人
     */
    private Integer updater;

    @Getter
    @Setter
    private String doorPic;

    /**
     * 预注册标记
     */
    private Integer preRegisterFlag;

    /**
     * 业务线:0=鲜沐;1=pop
     */
    @Getter
    @Setter
    private Integer businessLine;

    public Integer getPreRegisterFlag() {
        return preRegisterFlag;
    }

    public void setPreRegisterFlag(Integer preRegisterFlag) {
        this.preRegisterFlag = preRegisterFlag;
    }

    public Integer getOperateStatus() {
        return operateStatus;
    }

    public void setOperateStatus(Integer operateStatus) {
        this.operateStatus = operateStatus;
    }

    public String getFreeDay() {
        return freeDay;
    }

    public void setFreeDay(String freeDay) {
        this.freeDay = freeDay;
    }

    public Integer getDisplayButton() {
        return displayButton;
    }

    public void setDisplayButton(Integer displayButton) {
        this.displayButton = displayButton;
    }

    public String getOuterNo() {
        return this.outerNo;
    }

    public void setOuterNo(String outerNo) {
        this.outerNo = outerNo;
    }

    public Boolean getShowPrice() {
        return showPrice;
    }

    public void setShowPrice(Boolean showPrice) {
        this.showPrice = showPrice;
    }

    public String getPoiNote() {
        return poiNote;
    }

    public void setPoiNote(String poiNote) {
        this.poiNote = poiNote;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname == null ? null : mname.trim();
    }

    public String getMcontact() {
        return mcontact;
    }

    public void setMcontact(String mcontact) {
        this.mcontact = mcontact == null ? null : mcontact.trim();
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid == null ? null : openid.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public Byte getIslock() {
        return islock;
    }

    public void setIslock(Byte islock) {
        this.islock = islock;
    }

    public Byte getRankId() {
        return rankId;
    }

    public void setRankId(Byte rankId) {
        this.rankId = rankId;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    public String getInvitecode() {
        return invitecode;
    }

    public void setInvitecode(String invitecode) {
        this.invitecode = invitecode;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Integer getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(Integer auditUser) {
        this.auditUser = auditUser;
    }

    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense == null ? null : businessLicense.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getShopSign() {
        return shopSign;
    }

    public void setShopSign(String shopSign) {
        this.shopSign = shopSign;
    }

    public String getOtherProof() {
        return otherProof;
    }

    public void setOtherProof(String otherProof) {
        this.otherProof = otherProof;
    }

    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }


    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTradeArea() {
        return tradeArea;
    }

    public void setTradeArea(String tradeArea) {
        this.tradeArea = tradeArea;
    }

    public String getTradeGroup() {
        return tradeGroup;
    }

    public void setTradeGroup(String tradeGroup) {
        this.tradeGroup = tradeGroup;
    }

    public String getInviterChannelCode() {
        return inviterChannelCode;
    }

    public void setInviterChannelCode(String inviterChannelCode) {
        this.inviterChannelCode = inviterChannelCode;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getMpOpenid() {
        return mpOpenid;
    }

    public void setMpOpenid(String mpOpenid) {
        this.mpOpenid = mpOpenid;
    }

    public Integer getDirect() {
        return direct;
    }

    public void setDirect(Integer direct) {
        this.direct = direct;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public BigDecimal getMemberIntegral() {
        return memberIntegral;
    }

    public void setMemberIntegral(BigDecimal memberIntegral) {
        this.memberIntegral = memberIntegral;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    @Override
    public String toString() {
        return "Merchant{" +
                "mId=" + mId +
                ", mname='" + mname + '\'' +
                ", mcontact='" + mcontact + '\'' +
                ", openid='" + openid + '\'' +
                ", phone='" + phone + '\'' +
                ", islock=" + islock +
                ", rankId=" + rankId +
                ", registerTime=" + registerTime +
                ", loginTime=" + loginTime +
                ", invitecode='" + invitecode + '\'' +
                ", inviterChannelCode='" + inviterChannelCode + '\'' +
                ", auditTime=" + auditTime +
                ", auditUser=" + auditUser +
                ", businessLicense='" + businessLicense + '\'' +
                ", shopSign='" + shopSign + '\'' +
                ", otherProof='" + otherProof + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", area='" + area + '\'' +
                ", address='" + address + '\'' +
                ", poiNote='" + poiNote + '\'' +
                ", remark='" + remark + '\'' +
                ", lastOrderTime=" + lastOrderTime +
                ", areaNo=" + areaNo +
                ", size='" + size + '\'' +
                ", type='" + type + '\'' +
                ", tradeArea='" + tradeArea + '\'' +
                ", tradeGroup='" + tradeGroup + '\'' +
                ", unionid='" + unionid + '\'' +
                ", mpOpenid='" + mpOpenid + '\'' +
                ", direct=" + direct +
                ", adminId=" + adminId +
                ", server=" + server +
                ", memberIntegral=" + memberIntegral +
                ", houseNumber=" + houseNumber +
                '}';
    }

    public Integer getServer() {
        return server;
    }

    public void setServer(Integer server) {
        this.server = server;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }


    public Integer getSkuShow() {
        return skuShow;
    }

    public void setSkuShow(Integer skuShow) {
        this.skuShow = skuShow;
    }

    public BigDecimal getCashAmount() {
        return cashAmount;
    }

    public void setCashAmount(BigDecimal cashAmount) {
        this.cashAmount = cashAmount;
    }

    public Date getCashUpdateTime() {
        return cashUpdateTime;
    }

    public void setCashUpdateTime(Date cashUpdateTime) {
        this.cashUpdateTime = cashUpdateTime;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public LocalDateTime getMergeTime() {
        return mergeTime;
    }

    public void setMergeTime(LocalDateTime mergeTime) {
        this.mergeTime = mergeTime;
    }

    public String getMergeAdmin() {
        return mergeAdmin;
    }

    public void setMergeAdmin(String mergeAdmin) {
        this.mergeAdmin = mergeAdmin;
    }

    public Integer getChangePop() {
        return changePop;
    }

    public void setChangePop(Integer changePop) {
        this.changePop = changePop;
    }

    public String getPullBlackRemark() {
        return this.pullBlackRemark;
    }

    public void setPullBlackRemark(String pullBlackRemark) {
        this.pullBlackRemark = pullBlackRemark;
    }

    public String getHouseNumber() {
        return houseNumber;
    }

    public void setHouseNumber(String houseNumber) {
        this.houseNumber = houseNumber;
    }

    public String getCompanyBrand() {
        return companyBrand;
    }

    public void setCompanyBrand(String companyBrand) {
        this.companyBrand = companyBrand;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getEnterpriseScale() {
        return enterpriseScale;
    }

    public void setEnterpriseScale(String enterpriseScale) {
        this.enterpriseScale = enterpriseScale;
    }

    public Integer getCluePool() {
        return cluePool;
    }

    public void setCluePool(Integer cluePool) {
        this.cluePool = cluePool;
    }

    public Integer getExamineType() {
        return examineType;
    }

    public void setExamineType(Integer examineType) {
        this.examineType = examineType;
    }

    public String getPullBlackOperator() {
        return pullBlackOperator;
    }

    public void setPullBlackOperator(String pullBlackOperator) {
        this.pullBlackOperator = pullBlackOperator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getAuditUserName() {
        return auditUserName;
    }

    public void setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
    }
}
