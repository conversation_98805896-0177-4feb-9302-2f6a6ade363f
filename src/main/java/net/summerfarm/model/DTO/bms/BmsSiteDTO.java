package net.summerfarm.model.DTO.bms;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.enums.DeliverySiteEnums;
import net.summerfarm.tms.enums.DeliverySiteInterceptStateEnum;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BmsSiteDTO extends SiteDTO {
    /**
     * @see DeliverySiteStatusEnum
     */
    private Integer status;
    /**
     * 拦截状态
     * @see DeliverySiteInterceptStateEnum
     */
    private Integer interceptState;
    /**
     * 配送方式 0正常配送 1专车配送
     * @see DeliverySiteEnums
     */
    private Integer sendWay;

    /**
     * 实际到达时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signInTime;
}
