package net.summerfarm.service.srm.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.mapper.manage.PurchaseInvoiceMapper;
import net.summerfarm.mapper.manage.SupplierMapper;
import net.summerfarm.model.domain.PurchaseInvoice;
import net.summerfarm.model.domain.srm.SrmSupplierUser;
import net.summerfarm.model.input.SupplierReq;
import net.summerfarm.model.vo.PurchaseInvoiceVO;
import net.summerfarm.service.PurchaseInvoiceService;
import net.summerfarm.service.srm.SrmSupplierInvoiceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: SrmSupplierInvoiceServiceImpl
 * @date 2022/9/2114:13
 */
@Service
public class SrmSupplierInvoiceServiceImpl extends BaseService implements SrmSupplierInvoiceService {

    @Resource
    private PurchaseInvoiceService purchaseInvoiceService;
    @Resource
    private SupplierMapper supplierMapper;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult upsertSupplierInvoice(PurchaseInvoiceVO purchaseInvoice) {
        String srmSave = purchaseInvoiceService.srmSave(purchaseInvoice);
        if (!ObjectUtils.isEmpty(srmSave)) {
            return AjaxResult.getErrorWithMsg(srmSave);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryInvoiceDetail(Integer id) {
        PurchaseInvoiceVO purchaseInvoiceVOList = purchaseInvoiceService.queryInvoiceDetail(id);
        return AjaxResult.getOK(purchaseInvoiceVOList);
    }

    @Override
    public AjaxResult deleteInvoice(Integer id) {
        SrmSupplierUser srmUser = getSrmUser();
        SupplierReq supplierReq = supplierMapper.selectBill(srmUser.getSupplierId());
        if (ObjectUtils.isEmpty(supplierReq) || ObjectUtils.isEmpty(supplierReq.getTaxNumber())) {
            return AjaxResult.getErrorWithMsg("税号等信息需要补充！");
        }
        purchaseInvoiceService.deleteInvoice(id, supplierReq.getSupplierName());
        return AjaxResult.getOK();
    }
}
