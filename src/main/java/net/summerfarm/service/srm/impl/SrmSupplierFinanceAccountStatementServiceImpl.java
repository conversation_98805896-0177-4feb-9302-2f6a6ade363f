package net.summerfarm.service.srm.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.enums.login.SystemLoginTypeEnum;
import net.summerfarm.enums.srm.SrmSupplierFinanceAccountReasonEnum;
import net.summerfarm.enums.srm.SrmSupplierFinanceAccountStatementEnum;
import net.summerfarm.facade.auth.AuthUserBaseFacade;
import net.summerfarm.mapper.FinancePaymentOrderMapper;
import net.summerfarm.mapper.manage.AdminAuthExtendMapper;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.SupplierCoordinationConfigMapper;
import net.summerfarm.mapper.manage.SupplierMapper;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.mapper.srm.SrmSupplierFinanceAccountStatementMapper;
import net.summerfarm.mapper.srm.SrmSupplierUserMapper;
import net.summerfarm.model.DTO.WechatDTO;
import net.summerfarm.model.DTO.srm.WechatTemplateMiniProgramEntity;
import net.summerfarm.model.DTO.srm.WechatTemplateMsgDataEntity;
import net.summerfarm.model.SrmReceiptWechat;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.srm.SrmSupplierFinanceAccountStatement;
import net.summerfarm.model.domain.srm.SrmSupplierUser;
import net.summerfarm.model.input.FinanceAccountStatementInput;
import net.summerfarm.model.input.PurchaseInvoiceQuery;
import net.summerfarm.model.input.SupplierReq;
import net.summerfarm.model.input.srm.SrmSupplierFinanceAccountStatementQuery;
import net.summerfarm.model.vo.FinanceAccountStatementDetailVO;
import net.summerfarm.model.vo.FinanceAccountStatementVO;
import net.summerfarm.model.vo.PurchaseInvoiceVO;
import net.summerfarm.model.vo.srm.SrmStockTaskProcessDetailVO;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.mq.constant.SrmMqConstant;
import net.summerfarm.service.FinanceAccountStatementService;
import net.summerfarm.service.PurchaseInvoiceService;
import net.summerfarm.service.srm.SrmSupplierFinanceAccountStatementService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @title: SrmSupplierFinanceAccountStatementServiceImpl
 * @date 2022/9/1411:11
 */
@Service
@Slf4j
public class SrmSupplierFinanceAccountStatementServiceImpl extends BaseService implements SrmSupplierFinanceAccountStatementService {

    @Resource
    private SrmSupplierFinanceAccountStatementMapper srmSupplierFinanceAccountStatementMapper;
    @Resource
    private SupplierMapper supplierMapper;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private SrmSupplierUserMapper srmSupplierUserMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private PurchaseInvoiceService purchaseInvoiceService;
    @Resource
    private FinanceAccountStatementService financeAccountStatementService;

    @Resource
    private SupplierCoordinationConfigMapper supplierCoordinationConfigMapper;
    @Resource
    private FinancePaymentOrderMapper financePaymentOrderMapper;
    @Resource
    private AuthUserBaseFacade authUserBaseFacade;

    @Autowired
    MqProducer mqProducer;

    /**
     * 微信模板API
     */
    public static String TEMPLATE_ID = "IbDsVk5XlKwO3wLYJZgBb3hc-wT0u1u9SePunCebN7s";

    /**
     * 供应商收款单微信模版API
     */
    public static final String SRM_RECEIPT_TEMPLATE_ID = "ucUPhZyPud8TLdsFkT9lQE1pGtvIOSQEnQV4BlJTyfg";

    /**
     * 跳转开关key
     */
    public static String WECHAT_TEMPLATE_JUMP_SWITCH = "WECHAT_TEMPLATE_JUMP_SWITCH";

    public static String WECHAT_TEMPLATE_JUMP_SWITCH_ON = "on";

    @Override
    public AjaxResult queryList(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = new SrmSupplierFinanceAccountStatement();
        srmSupplierFinanceAccountStatement.setStatus(srmSupplierFinanceAccountStatementQuery.getStatus());
        srmSupplierFinanceAccountStatement.setFinanceAccountStatementId(srmSupplierFinanceAccountStatementQuery.getFinanceAccountStatementId());
        SrmSupplierUser srmUser = getSrmUser();
        srmSupplierFinanceAccountStatement.setSupplierId(srmUser.getSupplierId());
        PageHelper.startPage(srmSupplierFinanceAccountStatementQuery.getPageIndex(), srmSupplierFinanceAccountStatementQuery.getPageSize());
        List<SrmSupplierFinanceAccountStatement> srmSupplierFinanceAccountStatements = srmSupplierFinanceAccountStatementMapper.queryList(srmSupplierFinanceAccountStatement);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(srmSupplierFinanceAccountStatements));
    }

    @Override
    public AjaxResult<SrmSupplierFinanceAccountStatement> queryDetail(SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement) {
        SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(srmSupplierFinanceAccountStatement.getFinanceAccountStatementId());
        return AjaxResult.getOK(supplierFinanceAccountStatement);
    }

    @Override
    public AjaxResult purchaseQueryDetail(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        FinanceAccountStatementInput financeAccountStatementInput = new FinanceAccountStatementInput();
        financeAccountStatementInput.setId(srmSupplierFinanceAccountStatementQuery.getId());
        financeAccountStatementInput.setSku(srmSupplierFinanceAccountStatementQuery.getSku());
        List<FinanceAccountStatementDetailVO> financeAccountStatementDetailList = financeAccountStatementService.selectStatementDetail(financeAccountStatementInput);
        return AjaxResult.getOK(financeAccountStatementDetailList);
    }

    @Override
    public AjaxResult purchaseQueryDetails(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        FinanceAccountStatementInput financeAccountStatementInput = new FinanceAccountStatementInput();
        financeAccountStatementInput.setId(srmSupplierFinanceAccountStatementQuery.getId());
        List<SrmStockTaskProcessDetailVO> srmStockTaskProcessDetailVOList = financeAccountStatementService.selectPurchaseQueryDetails(financeAccountStatementInput);
        return AjaxResult.getOK(srmStockTaskProcessDetailVOList);
    }

    @Override
    public AjaxResult queryInvoiceList(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        SrmSupplierUser srmUser = getSrmUser();
        SupplierReq supplierReq = supplierMapper.selectBill(srmUser.getSupplierId());
        if (ObjectUtils.isEmpty(supplierReq) || ObjectUtils.isEmpty(supplierReq.getTaxNumber())) {
            return AjaxResult.getErrorWithMsg("税号等信息需要补充！");
        }
        PurchaseInvoiceQuery purchaseInvoiceQuery = new PurchaseInvoiceQuery();
        purchaseInvoiceQuery.setTaxNumber(supplierReq.getTaxNumber());
        List<PurchaseInvoiceVO> purchaseInvoiceList = purchaseInvoiceService.selectSrmInvoice(srmSupplierFinanceAccountStatementQuery.getPageIndex(), srmSupplierFinanceAccountStatementQuery.getPageSize(), purchaseInvoiceQuery);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseInvoiceList));
    }

    @Override
    public AjaxResult queryDetailInvoice(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        FinanceAccountStatement financeAccountStatement = financeAccountStatementService.selectIdToBms(srmSupplierFinanceAccountStatementQuery.getFinanceAccountStatementId());
        List<PurchaseInvoiceVO> purchaseInvoiceList = purchaseInvoiceService.selectSrmInvoiceByWalletsId(srmSupplierFinanceAccountStatementQuery.getPageIndex(), srmSupplierFinanceAccountStatementQuery.getPageSize(), financeAccountStatement.getWalletsId());
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseInvoiceList));
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult matchAccount(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        if (ObjectUtils.isEmpty(srmSupplierFinanceAccountStatementQuery)) {
            return AjaxResult.getErrorWithMsg("没有匹配的对账单和采购发票");
        }
        List<Integer> purchaseInvoiceIdList = srmSupplierFinanceAccountStatementQuery.getPurchaseInvoiceIdList();
        Long financeAccountStatementId = srmSupplierFinanceAccountStatementQuery.getFinanceAccountStatementId();
        BigDecimal totalIncludedTax = srmSupplierFinanceAccountStatementQuery.getTotalIncludedTax();
        if (CollectionUtils.isEmpty(purchaseInvoiceIdList)) {
            return AjaxResult.getErrorWithMsg("没有匹配的采购发票");
        }
        if (ObjectUtils.isEmpty(financeAccountStatementId)) {
            return AjaxResult.getErrorWithMsg("没有匹配的对账单");
        }
        SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(srmSupplierFinanceAccountStatementQuery.getFinanceAccountStatementId());

        SrmSupplierUser srmUser = getSrmUser();
        SupplierReq supplierReq = supplierMapper.selectBill(srmUser.getSupplierId());
        if (ObjectUtils.isEmpty(supplierReq) || ObjectUtils.isEmpty(supplierReq.getTaxNumber())) {
            return AjaxResult.getErrorWithMsg("税号等信息需要补充！");
        }
        String message = purchaseInvoiceService.addWallets(purchaseInvoiceIdList, financeAccountStatementId, totalIncludedTax, supplierReq.getName(), srmUser.getSupplierId());
        if (!ObjectUtils.isEmpty(message)) {
            return AjaxResult.getErrorWithMsg(message);
        }

        log.info(supplierFinanceAccountStatement.getSupplierName() + "的账单已开票并提交审核");
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult supplierConfirm(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(srmSupplierFinanceAccountStatementQuery.getFinanceAccountStatementId());
        if (!Objects.equals(srmSupplierFinanceAccountStatement.getStatus(), SrmSupplierFinanceAccountStatementEnum.WAIT_CONFIRM.ordinal())) {
            return AjaxResult.getErrorWithMsg("该对账单已经被确认或不存在。");
        }
        FinanceAccountStatementVO financeAccountStatementVO = financeAccountStatementService.statementDetails(srmSupplierFinanceAccountStatement.getFinanceAccountStatementId());
        if (!Objects.equals(financeAccountStatementVO.getStatus(), FinanceAccountStatementStatusEnum.WAIT_SUPPLIER_CONFIRM.ordinal())) {
            return AjaxResult.getErrorWithMsg("该对账单已经被确认或不存在。");
        }
        sendMessage(srmSupplierFinanceAccountStatementQuery.getFinanceAccountStatementId(), srmSupplierFinanceAccountStatementQuery.getConfirm(), MType.SRM_SUPPLIER_CONFIRM.name());
        SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = new SrmSupplierFinanceAccountStatement();
        supplierFinanceAccountStatement.setId(srmSupplierFinanceAccountStatement.getId());
        supplierFinanceAccountStatement.setFinanceAccountStatementId(srmSupplierFinanceAccountStatement.getFinanceAccountStatementId());
        if (Objects.equals(srmSupplierFinanceAccountStatementQuery.getConfirm(), CommonNumbersEnum.ONE.getNumber())) {
            supplierFinanceAccountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVALID.ordinal());
            supplierFinanceAccountStatement.setDeleteReason(SrmSupplierFinanceAccountReasonEnum.SUPPLIER_REJECTION.ordinal());
            supplierFinanceAccountStatement.setRemark(srmSupplierFinanceAccountStatementQuery.getRemark());
            srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(supplierFinanceAccountStatement);
            log.info(financeAccountStatementVO.getSupplierName() + "的账单被供应商拒绝");
            sendOfferMessage(srmSupplierFinanceAccountStatement, financeAccountStatementVO.getSupplierName() + "的账单被供应商拒绝，请及时联系供应商处理", "作废");
            return AjaxResult.getOK();
        }
        supplierFinanceAccountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVOICE_TO_BE_MATCHED.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(supplierFinanceAccountStatement);
        return AjaxResult.getOK();

    }

    /**
     * 供应商确认/驳回对账单
     *
     * @param id
     * @param key
     */
    private void sendMessage(Long id, Integer confirm, String key) {
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("id", id);
        msgJson.put("confirm", confirm);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(SrmMqConstant.Topic.TOPIC_PMS_SRM, null, JSON.toJSONString(mqData));
    }

    /**
     * 采购待确认 供应商对账单改变
     *
     * @param id
     * @return
     */
    @Override
    public String purchaserConfirm(Long id) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(id);
        SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = new SrmSupplierFinanceAccountStatement();
        supplierFinanceAccountStatement.setId(srmSupplierFinanceAccountStatement.getId());
        supplierFinanceAccountStatement.setFinanceAccountStatementId(srmSupplierFinanceAccountStatement.getFinanceAccountStatementId());
        supplierFinanceAccountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVOICE_TO_BE_MATCHED.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(supplierFinanceAccountStatement);
        return null;
    }

    @Override
    public void updateSrmAccount(Long id) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(id);
        SrmSupplierFinanceAccountStatement accountStatement = new SrmSupplierFinanceAccountStatement();
        accountStatement.setId(srmSupplierFinanceAccountStatement.getId());
        accountStatement.setFinanceAccountStatementId(id);
        accountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.WAIT_AUDIT.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(accountStatement);
    }

    @Override
    public void updateSrmAccountBack(Long id) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(id);
        SrmSupplierFinanceAccountStatement accountStatement = new SrmSupplierFinanceAccountStatement();
        accountStatement.setId(srmSupplierFinanceAccountStatement.getId());
        accountStatement.setFinanceAccountStatementId(id);
        accountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVOICE_TO_BE_MATCHED.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(accountStatement);
    }


    @Override
    public void cancelSrmAccountBack(Long id) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(id);
        SrmSupplierFinanceAccountStatement accountStatement = new SrmSupplierFinanceAccountStatement();
        accountStatement.setId(srmSupplierFinanceAccountStatement.getId());
        accountStatement.setFinanceAccountStatementId(id);
        accountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVALID.ordinal());
        accountStatement.setDeleteReason(SrmSupplierFinanceAccountReasonEnum.PAYMENT_APPROVAL_FAILED.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(accountStatement);
    }

    @Override
    public void waitSrmAccountBack(Long id) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(id);
        SrmSupplierFinanceAccountStatement accountStatement = new SrmSupplierFinanceAccountStatement();
        accountStatement.setId(srmSupplierFinanceAccountStatement.getId());
        accountStatement.setFinanceAccountStatementId(id);
        accountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.WAIT_PAY.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(accountStatement);
    }

    @Override
    public void srmAccountCompleted(Long id) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(id);
        if (Objects.isNull(srmSupplierFinanceAccountStatement)) {
            return;
        }
        SrmSupplierFinanceAccountStatement accountStatement = new SrmSupplierFinanceAccountStatement();
        accountStatement.setId(srmSupplierFinanceAccountStatement.getId());
        accountStatement.setFinanceAccountStatementId(id);
        accountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.HAS_PAY.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(accountStatement);
        // feat(发货管理优化&财务往来)：对账单完成后发送小程序收款通知
        // 对账单id查询付款单id
        FinancePaymentOrder financePaymentOrderQuery = new FinancePaymentOrder();
        financePaymentOrderQuery.setAdditionalId(id);
        financePaymentOrderQuery.setType(FinancePaymentOrderTypeEnum.ACCOUNT_STATEMENT.ordinal());
        financePaymentOrderQuery.setStatus(FinancePaymentOrderStatusEnum.PAY_SUCCESS.ordinal());
        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.selectOne(financePaymentOrderQuery);
        if(Objects.isNull(financePaymentOrder)){
            return;
        }
        // 微信小程序消息通知
        SrmReceiptWechat receiptWechat = new SrmReceiptWechat();
        receiptWechat.setOrderId(financePaymentOrder.getId());
        receiptWechat.setSupplierId(srmSupplierFinanceAccountStatement.getSupplierId());
        receiptWechat.setSupplierName(srmSupplierFinanceAccountStatement.getSupplierName());
        receiptWechat.setReceiptAmount(financePaymentOrder.getAmount().toPlainString());
        receiptWechat.setFinishTime(financePaymentOrder.getUpdateTime());
        receiptWechat.setPayer(BaseConstant.XIANMU_TEACH);
        sendReceiptWechatMsg(receiptWechat);
    }


    /**
     * 钉钉消息通知采购
     *
     * @param srmSupplierFinanceAccountStatement
     * @param reason
     * @param state
     */
    private void sendOfferMessage(SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement, String reason, String state) {
        //查询所属销售的钉钉对应信息
        AdminAuthExtend creatorInfo = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), srmSupplierFinanceAccountStatement.getCreatorAdminId());
        FinanceAccountStatementVO financeAccountStatementVO = financeAccountStatementService.statementDetails(srmSupplierFinanceAccountStatement.getFinanceAccountStatementId());
        String title = "【采购对账通知】";
        StringBuilder text = new StringBuilder("##### " + title + "\n");
        text.append(reason).append("\n");
        text.append("> ###### 供应商：").append(srmSupplierFinanceAccountStatement.getSupplierName()).append("\n");
        text.append("> ###### 账单状态：").append(state).append("\n");
        text.append("> ###### 账单号：").append(srmSupplierFinanceAccountStatement.getFinanceAccountStatementId()).append("\n");
        text.append("> ###### 账单总额：").append(srmSupplierFinanceAccountStatement.getTotalBillAmount()).append("\n");
        text.append("> ###### 应付总额：").append(srmSupplierFinanceAccountStatement.getTotalBillAmount().subtract(srmSupplierFinanceAccountStatement.getWriteOffAmount())).append("\n");
        text.append("> ###### 核销金额：").append(srmSupplierFinanceAccountStatement.getWriteOffAmount()).append("\n");
        text.append("> ###### 创建时间：").append(financeAccountStatementVO.getCreateTime()).append("\n");
        if (Objects.nonNull(creatorInfo)) {
            logger.info("【采购对账通知】钉钉Id{}" + creatorInfo.getUserId());
            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), creatorInfo.getUserId(), title, text.toString());
            dingTalkMsgSender.sendMessage(dingTalkMsgBO);
        }
    }


    @Override
    public void upsertSrmSupplierAccount(FinanceAccountStatement financeAccountStatement) {
        SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = new SrmSupplierFinanceAccountStatement();
        supplierFinanceAccountStatement.setSupplierId(financeAccountStatement.getSupplierId());
        supplierFinanceAccountStatement.setSupplierName(financeAccountStatement.getSupplierName());
        supplierFinanceAccountStatement.setFinanceAccountStatementId(financeAccountStatement.getId());
        supplierFinanceAccountStatement.setEstimateAmount(financeAccountStatement.getEstimateAmount());
        supplierFinanceAccountStatement.setTotalBillAmount(financeAccountStatement.getTotalBillAmount());
        supplierFinanceAccountStatement.setWriteOffAmount(financeAccountStatement.getWriteOffAmount());
        supplierFinanceAccountStatement.setPurchaserName("杭州鲜沐科技有限公司");
        supplierFinanceAccountStatement.setTaxNumber(financeAccountStatement.getTaxNumber());
        supplierFinanceAccountStatement.setPayType(financeAccountStatement.getPayType());
        supplierFinanceAccountStatement.setAccount(financeAccountStatement.getAccount());
        supplierFinanceAccountStatement.setAccountBank(financeAccountStatement.getAccountBank());
        supplierFinanceAccountStatement.setAccountName(financeAccountStatement.getAccountName());
        supplierFinanceAccountStatement.setAccountAscription(financeAccountStatement.getAccountAscription());
        supplierFinanceAccountStatement.setCreator(financeAccountStatement.getCreator());
        supplierFinanceAccountStatement.setCreatorAdminId(financeAccountStatement.getCreatorAdminId());
        supplierFinanceAccountStatement.setCreateTime(LocalDateTime.now());
        // 查询供应商是否协同
        if (isNeedConfirm(financeAccountStatement.getSupplierId())) {
            supplierFinanceAccountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.WAIT_CONFIRM.ordinal());
        } else {
            supplierFinanceAccountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVOICE_TO_BE_MATCHED.ordinal());
        }
        //落库前幂等校验，如果存在则不插入
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(financeAccountStatement.getId());
        if (Objects.isNull(srmSupplierFinanceAccountStatement)){
            srmSupplierFinanceAccountStatementMapper.insertSelective(supplierFinanceAccountStatement);
        }
        log.info("生成srm对账单" + supplierFinanceAccountStatement.getId());
    }

    private boolean isNeedConfirm(Integer supplierId) {
        SupplierCoordinationConfig coordinationConfig = supplierCoordinationConfigMapper.selectBySupplierId(supplierId.longValue());
        // 协同配置为空,对账协同配置为空,或者不需要协同返回false
        if (Objects.isNull(coordinationConfig) || Objects.isNull(coordinationConfig.getReconciliationCoordinationTab())
                || Objects.equals(coordinationConfig.getReconciliationCoordinationTab(), 0)) {
            return false;
        }
        return Objects.equals(coordinationConfig.getReconciliationCoordinationTab(), 1);
    }

    @Override
    public void upsertSrmWechatMessage(FinanceAccountStatement financeAccountStatement) {
        //供应商微信openId
        SrmSupplierUser srmSupplierUser = srmSupplierUserMapper.selectBySupplierId(financeAccountStatement.getSupplierId());
        if (ObjectUtils.isEmpty(srmSupplierUser)) {
            log.info("供应商暂未关注公众号,无法推送消息,供应商id：{},供应商名称:{}", financeAccountStatement.getSupplierId(), financeAccountStatement.getSupplierName());
            return;
        }
        log.info("供应商id" + financeAccountStatement.getSupplierId() + "srmId" + srmSupplierUser.getId());

        //发送公众号消息
        WechatDTO wechatDTO = new WechatDTO();
        wechatDTO.setFirst("新增账单，请确认账单并开票");
        wechatDTO.setKeywordOne("账单ID+" + financeAccountStatement.getId().toString());
        wechatDTO.setKeywordTwo("账单状态：待确认");
        wechatDTO.setKeywordThree("采购员:" + financeAccountStatement.getCreator());
        wechatDTO.setKeywordFour("￥账单总额:" + financeAccountStatement.getTotalBillAmount().toPlainString());
        wechatDTO.setKeywordFive("账单创建时间:" + DateUtils.localDateToString(LocalDate.now(), DateUtils.SPECIFIC_DATE));
        wechatDTO.setRemark("点击进入账单详情");

        //根据creator和类型查询用户属性扩展表的openId信息
        List<String> openIdList = authUserBaseFacade.queryWechatOpenIdByBizUserId(net.xianmu.authentication.client.input.SystemOriginEnum.SRM, srmSupplierUser.getId());
        if (CollectionUtils.isEmpty(openIdList)) {
            log.info("供应商暂未关注公众号,无法推送消息,srm用户id:{}", srmSupplierUser.getId());
        } else {
            for (String openId : openIdList) {
                log.info("srm openId" + openId);
                sendTemplateMsg(financeAccountStatement.getId(), openId, "pages/statement-details/index?financeAccountStatementId=", wechatDTO);
            }
        }

    }

    @Override
    public void addSrmWechatMessage(FinanceAccountStatementVO financeAccountStatement) {
        //供应商微信openId
        SrmSupplierUser srmSupplierUser = srmSupplierUserMapper.selectBySupplierId(financeAccountStatement.getSupplierId());
        if (ObjectUtils.isEmpty(srmSupplierUser)) {
            log.info("供应商暂未关注公众号,无法推送消息,供应商id：{},供应商名称:{}", financeAccountStatement.getSupplierId(), financeAccountStatement.getSupplierName());
            return;
        }
        //srm对账单的创建时间
        SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(financeAccountStatement.getId());
        if (ObjectUtils.isEmpty(supplierFinanceAccountStatement)) {
            log.info("srm对账单不存在");
            return;
        }
        //发送公众号消息
        WechatDTO wechatDTO = new WechatDTO();
        wechatDTO.setFirst("账单发票审核不通过，请检查后重试或联系采购经理介入解决");
        wechatDTO.setKeywordOne("账单ID：" + financeAccountStatement.getId().toString());
        wechatDTO.setKeywordTwo("账单状态：待开票");
        wechatDTO.setKeywordThree("采购员:" + financeAccountStatement.getCreator());
        wechatDTO.setKeywordFour("￥账单总额:" + financeAccountStatement.getTotalBillAmount().toPlainString());
        wechatDTO.setKeywordFive("账单创建时间:" + DateUtils.localDateTimeToString(supplierFinanceAccountStatement.getCreateTime()));
        wechatDTO.setRemark("点击进入账单详情");

        //根据creator和类型查询用户属性扩展表的openId信息
        List<String> openIdList = authUserBaseFacade.queryWechatOpenIdByBizUserId(net.xianmu.authentication.client.input.SystemOriginEnum.SRM, srmSupplierUser.getId());
        if (CollectionUtils.isEmpty(openIdList)) {
            log.info("供应商暂未关注公众号,无法推送消息,srm用户id:{}", srmSupplierUser.getId());
        } else {
            for (String openId : openIdList) {
                sendTemplateMsg(financeAccountStatement.getId(), openId, "pages/statement-details/index?financeAccountStatementId=", wechatDTO);
            }
        }

        //修改srm对账单
        supplierFinanceAccountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVOICE_TO_BE_MATCHED.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(supplierFinanceAccountStatement);

    }

    @Override
    public void paymentFailSrmWechatMessage(Long id) {

        FinanceAccountStatement financeAccountStatement = financeAccountStatementService.selectIdToBms(id);
        if (Objects.equals(financeAccountStatement.getConfirmUser(), CommonNumbersEnum.ONE.getNumber())) {
            //供应商微信openId
            SrmSupplierUser srmSupplierUser = srmSupplierUserMapper.selectBySupplierId(financeAccountStatement.getSupplierId());
            if (ObjectUtils.isEmpty(srmSupplierUser)) {
                return;
            }

            //srm对账单的创建时间
            SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(financeAccountStatement.getId());
            if (ObjectUtils.isEmpty(supplierFinanceAccountStatement)) {
                return;
            }

            //发送公众号消息
            WechatDTO wechatDTO = new WechatDTO();
            wechatDTO.setFirst("账单审核不通过，请联系采购经理介入解决");
            wechatDTO.setKeywordOne("账单ID：" + financeAccountStatement.getId().toString());
            wechatDTO.setKeywordTwo("账单状态：已作废");
            wechatDTO.setKeywordThree("采购员:" + financeAccountStatement.getCreator());
            wechatDTO.setKeywordFour("￥账单总额:" + financeAccountStatement.getTotalBillAmount().toPlainString());
            wechatDTO.setKeywordFive("账单创建时间:" + DateUtils.localDateTimeToString(supplierFinanceAccountStatement.getCreateTime()));
            wechatDTO.setRemark("点击进入账单详情");

            //根据creator和类型查询用户属性扩展表的openId信息
            List<String> openIdList = authUserBaseFacade.queryWechatOpenIdByBizUserId(net.xianmu.authentication.client.input.SystemOriginEnum.SRM, srmSupplierUser.getId());
            if (CollectionUtils.isEmpty(openIdList)) {
                log.info("供应商暂未关注公众号,无法推送消息,srm用户id:{}", srmSupplierUser.getId());
            } else {
                for (String openId : openIdList) {
                    log.info("对账单付款单审批拒绝钉钉通知，对账单：" + financeAccountStatement.getId());
                    sendTemplateMsg(financeAccountStatement.getId(), openId, "pages/statement-details/index?financeAccountStatementId=", wechatDTO);
                }
            }
        }
    }

    @Override
    public void cancelSrmWechatMessage(FinanceAccountStatement financeAccountStatement) {

        //供应商微信openId
        SrmSupplierUser srmSupplierUser = srmSupplierUserMapper.selectBySupplierId(financeAccountStatement.getSupplierId());
        if (ObjectUtils.isEmpty(srmSupplierUser)) {
            log.info("供应商暂未关注公众号,无法推送消息,供应商id：" + financeAccountStatement.getSupplierId() + ",供应商名称:" + financeAccountStatement.getSupplierName());
            return;
        }

        //srm对账单的创建时间
        SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(financeAccountStatement.getId());
        if (ObjectUtils.isEmpty(supplierFinanceAccountStatement)) {
            log.info("srm对账单不存在");
            return;
        }

        //srm对账单关闭
        SrmSupplierFinanceAccountStatement accountStatement = new SrmSupplierFinanceAccountStatement();
        accountStatement.setId(supplierFinanceAccountStatement.getId());
        accountStatement.setFinanceAccountStatementId(financeAccountStatement.getId());
        accountStatement.setStatus(SrmSupplierFinanceAccountStatementEnum.INVALID.ordinal());
        accountStatement.setDeleteReason(SrmSupplierFinanceAccountReasonEnum.CLOSE_BILL.ordinal());
        srmSupplierFinanceAccountStatementMapper.updateByPrimaryKeySelective(accountStatement);

        if (!Objects.equals(financeAccountStatement.getConfirmUser(), CommonNumbersEnum.ONE.getNumber())) {
            return;
        }

        //发送公众号消息
        WechatDTO wechatDTO = new WechatDTO();
        wechatDTO.setFirst("账单已作废，请联系采购经理介入解决");
        wechatDTO.setKeywordOne("账单ID：" + financeAccountStatement.getId().toString());
        wechatDTO.setKeywordTwo("账单状态：已作废");
        wechatDTO.setKeywordThree("采购员:" + financeAccountStatement.getCreator());
        wechatDTO.setKeywordFour("￥账单总额:" + financeAccountStatement.getTotalBillAmount().toPlainString());
        wechatDTO.setKeywordFive("账单创建时间:" + DateUtils.localDateTimeToString(supplierFinanceAccountStatement.getCreateTime()));
        wechatDTO.setRemark("点击进入账单详情");

        //根据creator和类型查询用户属性扩展表的openId信息
        List<String> openIdList = authUserBaseFacade.queryWechatOpenIdByBizUserId(net.xianmu.authentication.client.input.SystemOriginEnum.SRM, srmSupplierUser.getId());
        if (CollectionUtils.isEmpty(openIdList)) {
            log.info("供应商暂未关注公众号,无法推送消息,srm用户id:{}", srmSupplierUser.getId());
        } else {
            for (String openId : openIdList) {
                sendTemplateMsg(financeAccountStatement.getId(), openId, "pages/statement-details/index?financeAccountStatementId=", wechatDTO);
            }
        }

    }


    @Override
    public void sendWechatMessage(Long id) {
        SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(id);
        //采购对账通知
        sendOfferMessage(srmSupplierFinanceAccountStatement, srmSupplierFinanceAccountStatement.getSupplierName() + "的账单已完成", "已完成");
        FinanceAccountStatement financeAccountStatement = financeAccountStatementService.selectIdToBms(id);
        if (Objects.equals(financeAccountStatement.getConfirmUser(), CommonNumbersEnum.ONE.getNumber())) {
            //供应商微信openId
            SrmSupplierUser srmSupplierUser = srmSupplierUserMapper.selectBySupplierId(financeAccountStatement.getSupplierId());
            if (ObjectUtils.isEmpty(srmSupplierUser)) {
                return;
            }


            //srm对账单的创建时间
            SrmSupplierFinanceAccountStatement supplierFinanceAccountStatement = srmSupplierFinanceAccountStatementMapper.selectByPrimaryKey(financeAccountStatement.getId());
            if (ObjectUtils.isEmpty(supplierFinanceAccountStatement)) {
                return;
            }

            //发送公众号消息
            WechatDTO wechatDTO = new WechatDTO();
            wechatDTO.setFirst("账单已完成");
            wechatDTO.setKeywordOne("账单ID：" + financeAccountStatement.getId().toString());
            wechatDTO.setKeywordTwo("账单状态：已完成");
            wechatDTO.setKeywordThree("采购员:" + financeAccountStatement.getCreator());
            wechatDTO.setKeywordFour("￥账单总额:" + financeAccountStatement.getTotalBillAmount().toPlainString());
            wechatDTO.setKeywordFive("账单创建时间:" + DateUtils.localDateTimeToString(supplierFinanceAccountStatement.getCreateTime()));
            wechatDTO.setRemark("点击进入账单详情");

            //根据creator和类型查询用户属性扩展表的openId信息
            List<String> openIdList = authUserBaseFacade.queryWechatOpenIdByBizUserId(net.xianmu.authentication.client.input.SystemOriginEnum.SRM, srmSupplierUser.getId());
            if (CollectionUtils.isEmpty(openIdList)) {
                log.info("供应商暂未关注公众号,无法推送消息,srm用户id:{}", srmSupplierUser.getId());
            } else {
                for (String openId : openIdList) {
                    sendTemplateMsg(financeAccountStatement.getId(), openId, "pages/statement-details/index?financeAccountStatementId=", wechatDTO);
                }
            }
        }
    }

    /**
     * 预付收款
     *
     * @param id 预付单id
     */
    @Override
    public void prepayReceipt(Long id) {
        log.info("[SRM-prepayReceipt] 预付单付款完成,预付单id:{}", id);
        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.queryById(id);
        if (!Objects.equals(financePaymentOrder.getType(), FinancePaymentOrderTypeEnum.ADVANCED_ORDER.ordinal())
                || !Objects.equals(financePaymentOrder.getStatus(), FinancePaymentOrderStatusEnum.PAY_SUCCESS.ordinal())) {
            log.info("[SRM-prepayReceipt] 该单据不是预付单或预付未完成支付");
            return;
        }
        Supplier supplier = supplierMapper.selectByName(financePaymentOrder.getSupplierName());
        if(supplier==null){
            return;
        }
        // 根据对账单id查询付款单id
        SrmReceiptWechat receiptWechat = new SrmReceiptWechat();
        receiptWechat.setOrderId(financePaymentOrder.getId());
        receiptWechat.setSupplierId(supplier.getId());
        receiptWechat.setSupplierName(financePaymentOrder.getSupplierName());
        receiptWechat.setReceiptAmount(financePaymentOrder.getAmount().toPlainString());
        receiptWechat.setFinishTime(financePaymentOrder.getUpdateTime());
        receiptWechat.setPayer(BaseConstant.XIANMU_TEACH);
        sendReceiptWechatMsg(receiptWechat);
        log.info("[SRM-prepayReceipt] 预付单付款完成,微信公众号消息推送成功,context:{}", JSON.toJSONString(receiptWechat));
    }

    private void sendReceiptWechatMsg(SrmReceiptWechat receiptWechat) {
        log.info("供应商收款单微信公众号消息通知,账单:{}", JSON.toJSONString(receiptWechat));
        WechatDTO wechatDTO = new WechatDTO();
        wechatDTO.setFirst("您收到一笔汇款,请确认");
        wechatDTO.setKeywordOne(receiptWechat.getSupplierName());
        wechatDTO.setKeywordTwo(receiptWechat.getPayer());
        wechatDTO.setKeywordThree(receiptWechat.getReceiptAmount());
        wechatDTO.setKeywordFour(DateUtil.formatYMdhms(receiptWechat.getFinishTime()));
        Integer supplierId = receiptWechat.getSupplierId();
        SrmSupplierUser srmSupplierUser = srmSupplierUserMapper.selectBySupplierId(supplierId);
        if(Objects.isNull(srmSupplierUser)){
            return;
        }
        List<String> openIdList = authUserBaseFacade.queryWechatOpenIdByBizUserId(net.xianmu.authentication.client.input.SystemOriginEnum.SRM, srmSupplierUser.getId());
        if (CollectionUtils.isEmpty(openIdList)) {
            log.info("供应商暂未关注公众号,无法推送消息,srm用户id:{}", srmSupplierUser.getId());
        } else {
            for (String openId : openIdList) {
                sendWechatMsg(receiptWechat.getOrderId(), SRM_RECEIPT_TEMPLATE_ID, openId, "pages/finance-details/index?receiptNo=", wechatDTO);
            }
            log.info("供应商收款单微信公众号消息通知发送成功,单据id:{}", receiptWechat.getOrderId());
        }
    }


    /**
     * 微信公众号消息通知
     *
     * @param id
     * @param openId
     * @param pagePath
     * @param wechatDTO
     */
    private void sendTemplateMsg(Long id, String openId, String pagePath, WechatDTO wechatDTO) {
        log.info("对账单提醒，微信公众号通知供应商");
        log.info("对账单提醒内容" + wechatDTO.getFirst());
        //将审核结果，微信公众号通知供应商
        //构造通知json
        sendWechatMsg(id, TEMPLATE_ID, openId, pagePath, wechatDTO);
    }

    private void sendWechatMsg(Long id, String templateId, String openId, String pagePath, WechatDTO wechatDTO) {
        Map<String, Object> msg = new HashMap<>(4);
        msg.put("touser", openId);
        msg.put("template_id", templateId);
        Config wechatTemplateJumpSwitch = configMapper.selectOne(WECHAT_TEMPLATE_JUMP_SWITCH);
        if (Objects.nonNull(wechatTemplateJumpSwitch) && StringUtils.isNotBlank(wechatTemplateJumpSwitch.getValue()) && wechatTemplateJumpSwitch.getValue().equals(WECHAT_TEMPLATE_JUMP_SWITCH_ON)) {
            //pagePath微信跳转链路 id为该链接id
            msg.put("miniprogram", WechatTemplateMiniProgramEntity.builder().appid(SystemLoginTypeEnum.SRM_WX.getAppId()).pagepath(pagePath + id).build());
        }
        Map<String, Object> data = new HashMap<>(16);
        data.put("first", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getFirst()).build());
        data.put("keyword1", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordOne()).build());
        data.put("keyword2", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordTwo()).build());
        data.put("keyword3", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordThree()).build());
        data.put("keyword4", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordFour()).build());
        data.put("keyword5", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordFive()).build());
        data.put("remark", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getRemark()).build());

        msg.put("data", data);
        JSONObject json = new JSONObject(msg);
        //将处理结果集合发送至MQ
        MQData mqData = new MQData(MType.WECHAT_TEMPLATE.name());
        mqData.setData(json);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
    }

}
