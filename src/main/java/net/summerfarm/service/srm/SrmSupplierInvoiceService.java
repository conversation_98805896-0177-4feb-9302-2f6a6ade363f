package net.summerfarm.service.srm;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.PurchaseInvoice;
import net.summerfarm.model.vo.PurchaseInvoiceVO;

/**
 * <AUTHOR>
 * @title: SrmSupplierInvoiceService
 * @date 2022/9/2114:13
 */
public interface SrmSupplierInvoiceService {

    /**
     * SRM供应商提交发票（提交的发票为待匹配状态）
     * @param purchaseInvoice
     * @return
     */
    AjaxResult upsertSupplierInvoice(PurchaseInvoiceVO purchaseInvoice);

    /**
     * SRM供应商发票详情（发票池发票详情）
     * @param id
     * @return
     */
    AjaxResult queryInvoiceDetail(Integer id);

    /**
     * 删除发票
     * @param id
     * @return
     */
    AjaxResult deleteInvoice(Integer id);
}
