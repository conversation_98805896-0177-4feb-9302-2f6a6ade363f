package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.*;
import net.summerfarm.facade.item.MarketProviderFacade;
import net.summerfarm.facade.item.dto.MarketItemDetailDTO;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.MarketPriceChangeDTO;
import net.summerfarm.model.bo.price.PriceInfoBO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.price.CommodityVO;
import net.summerfarm.model.vo.price.PriceAdjustDetailVO;
import net.summerfarm.model.vo.price.SkuMinPriceVO;
import net.summerfarm.mq.constant.ProductMqConstant;
import net.summerfarm.mq.item.dto.PriceAdjustmentMsgDTO;
import net.summerfarm.repository.price.CycleInventoryCostRepository;
import net.summerfarm.service.*;
import net.summerfarm.task.quartz.JobManage;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.enums.InventorySubTypeEnum.CONSIGNMENT_NO_WAREHOUSE;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/11/8
 */
@Service("priceAdjustService")
public class PriceAdjustServiceImpl extends BaseService implements PriceAdjustService {

    private final static Logger logger = LoggerFactory.getLogger(PriceAdjustService.class);
    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private PriceAdjustmentMapper priceAdjustmentMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private MarketRuleMapper marketRuleMapper;
    @Lazy
    @Resource
    private InterestRateConfigService rateConfigService;
    @Resource
    private AreaSuitMapper areaSuitMapper;
    @Resource
    private PriceAdjustmentPoolMapper priceAdjustmentPoolMapper;
    @Resource
    private PriceAdjustmentTriggerMapper priceAdjustmentTriggerMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;
    @Resource
    private InterestRateConfigMapper interestRateConfigMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private JobManage jobManage;
    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private PriceStrategyAuditService priceStrategyAuditService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private FenceService fenceService;
    @Resource
    private WarehouseStorageService storageService;
    @Resource
    private PriceAdjustmentRuleAreaMapper priceAdjustmentRuleAreaMapper;
    @Resource
    private PriceAdjustmentRuleSectionMapper priceAdjustmentRuleSectionMapper;
    @Resource
    private CycleInventoryCostMapper cycleInventoryCostMapper;
    @Resource
    private OrderOuterInfoService orderOuterInfoService;
    @Resource
    private ActivityService activityService;
    @Resource
    private PriceAdjustRecordMapper priceAdjustRecordMapper;
    @Resource
    private ProductPriceAdjustmentMapper productPriceAdjustmentMapper;
    @Resource
    private ProductPriceAdjustmentDetailMapper productPriceAdjustmentDetailMapper;
    @Resource
    private Executor asyncServiceExecutor;

    @Resource(name = "priceAdjustExecutor")
    private Executor priceAdjustExecutor;
    @Resource
    PriceService priceService;
    @Lazy
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private PriceStrategyService priceStrategyService;

    @Resource
    private InventoryBindMapper inventoryBindMapper;

    @Resource
    private ActivityNewService activityNewService;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private MarketProviderFacade marketProviderFacade;

    @Resource
    private MqProducer mqProducer;

    private final static String AREA_NO = "area_no";

    @Override
    public AjaxResult selectSku(int pageIndex, int pageSize, PriceAdjustVO selectKeys) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PriceAdjustVO> priceAdjustVOS;
        PageInfo<PriceAdjustmentPoolVO> pageInfo = null;
        if (PriceAdjustStatus.NORMAL.ordinal() == selectKeys.getPriceStatus()) {
            //根据area_sku查询
            priceAdjustVOS = priceAdjustmentMapper.selectPriceAdjustVO3(selectKeys);
            priceAdjustVOS.forEach(el -> {
                //是否在活动中
//                ActivitySku activitySku = activitySkuMapper.selectOne(el.getCityNo(), el.getSku());
                CommonResult<ActivitySku> result = activityNewService.getActivitySku(
                        el.getSku(), el.getCityNo());
                ActivitySku activitySku = result.getData();
                if (activitySku != null) {
                    AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(el.getCityNo(), el.getSku());
                    PriceInfoBO priceInfo = priceService.getNormalPrice(areaSku, true);
                    el.setPrice(priceInfo.getPrice());
                }
                // 查询毛利率
                InterestRateConfig rateConfig = interestRateConfigMapper.selectByAreaNoAndSku(el.getCityNo(), el.getSku());
                if (Objects.nonNull(rateConfig)){
                    el.setInterestRate(rateConfig.getInterestRate());
                }

                //查询sku对应仓库采购价格
                WarehouseInventoryMapping wim = warehouseInventoryService.selectByUniqueIndex(el.getStoreNo(), el.getSku());
                Integer warehouseNo = Objects.isNull(wim) ||  Objects.isNull(wim.getWarehouseNo()) ? null : wim.getWarehouseNo();

                // 查询成本价
                if(dynamicConfig.getMallNewCostPriceSwitch()) {
                    if(warehouseNo != null) {
                        // dubbo调用
                        BigDecimal cost = cycleInventoryCostRepository.selectCycleCost(el.getSku(), warehouseNo);
                        el.setCostPrice(cost);
                    }
                } else {
                    StoreRecord record = storeRecordMapper.selectInStoreRecord(warehouseNo, el.getSku());
                    if (record != null) {
                        el.setCostPrice(record.getCost());

                        PurchasesPlan plan = purchasesPlanMapper.selectByNoAndSku(record.getBatch(), record.getSku(), record.getQualityDate());
                        if (plan != null) {
                            el.setMarketPrice(plan.getMarketPrice());
                        }
                    }
                }
            });
        } else {
            //根据状态查询调价池表中数据
            List<PriceAdjustmentPoolVO> poolList;
            if (PriceAdjustStatus.WAIT_AUDIT.ordinal() == selectKeys.getPriceStatus()) {
                poolList = priceAdjustmentPoolMapper.select(selectKeys.getAreaNo(), selectKeys.getCityNo(),
                        PriceAdjustPoolStatus.WAIT_AUDIT.getStatus(), selectKeys.getPdName(),
                        selectKeys.getSku(), selectKeys.getOnSale(),selectKeys.getRuleFlag(),selectKeys.getBusinessId(),
                        selectKeys.getParentCategoryId(),selectKeys.getGrandCategoryId(),selectKeys.getCategoryId(),
                        selectKeys.getCategoryTypes(), selectKeys.getType(), selectKeys.getExtType(),selectKeys.getSubType(), selectKeys.getMType());
            } else if (PriceAdjustStatus.WAIT_ADJUST.ordinal() == selectKeys.getPriceStatus()) {
                poolList = priceAdjustmentPoolMapper.select(selectKeys.getAreaNo(), selectKeys.getCityNo(),
                        PriceAdjustPoolStatus.WAIT_ADJUST.getStatus(), selectKeys.getPdName(),
                        selectKeys.getSku(), selectKeys.getOnSale(),selectKeys.getRuleFlag(),selectKeys.getBusinessId(),
                        selectKeys.getParentCategoryId(),selectKeys.getGrandCategoryId(),selectKeys.getCategoryId(),
                        selectKeys.getCategoryTypes(), selectKeys.getType(), selectKeys.getExtType(),selectKeys.getSubType(), selectKeys.getMType());
            } else {
                return AjaxResult.getError(ResultConstant.PARAM_FAULT);
            }

            pageInfo = new PageInfo<>(poolList);
            //数据转换
            priceAdjustVOS = poolList.stream().map(el -> {
                PriceAdjustVO vo = new PriceAdjustVO();
                //id为poolId
                vo.setId(el.getId());
                vo.setAreaNo(el.getStoreNo());
                vo.setCityNo(el.getAreaNo());
                vo.setLadderStatus(el.getLadderStatus());
                vo.setPdName(el.getPdName());
                vo.setSku(el.getSku());
                vo.setWeight(el.getWeight());
                vo.setCreateAdminName(el.getCreateAdminName());
                vo.setAddTime(el.getCreateTime());
                vo.setExtType(el.getExtType());
                vo.setMType(el.getMType());

//                ActivitySku activitySku = activitySkuMapper.selectOne(el.getAreaNo(), el.getSku());
                CommonResult<ActivitySku> result = activityNewService.getActivitySku(
                        el.getSku(), el.getAreaNo());
                ActivitySku activitySku = result.getData();
                if (activitySku != null) {
                    AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(el.getAreaNo(), el.getSku());
                    PriceInfoBO priceInfo = priceService.getNormalPrice(areaSku, true);
                    vo.setPrice(priceInfo.getPrice());
                } else {
                    vo.setPrice(el.getPrice());
                }

                // 查询毛利率
                InterestRateConfig rateConfig = interestRateConfigMapper.selectByAreaNoAndSku(el.getAreaNo(), el.getSku());
                if (Objects.nonNull(rateConfig)){
                    vo.setInterestRate(rateConfig.getInterestRate());
                }

                // 查询成本价
                if(dynamicConfig.getMallNewCostPriceSwitch()) {
                    // 先去拿库存仓，这个复用详情拿库存仓的逻辑
                    Integer warehouseNo = fenceService.selectWarehouseNo(el.getAreaNo(), el.getSku());
                    if(warehouseNo != null) {
                        // dubbo调用
                        BigDecimal cost = cycleInventoryCostRepository.selectCycleCost(el.getSku(), warehouseNo);
                        vo.setCostPrice(cost);
                    }
                } else {
                    List<PriceAdjustmentTrigger> triggerList = priceAdjustmentTriggerMapper.selectByBusinessId(el.getBusinessId());
                    if (!CollectionUtils.isEmpty(triggerList)) {
                        vo.setCostPrice(triggerList.get(0).getCostPrice());
                        vo.setMarketPrice(triggerList.get(0).getMarketPrice());
                    }
                }
                return vo;
            }).collect(Collectors.toList());
        }

        //查询活动 ok
        Area area = new Area();
        area.setLargeAreaNo(selectKeys.getAreaNo());
        List<Area> areas = areaMapper.selectList(area);
        List<String> activitySkus = new ArrayList<>();

        if(!CollectionUtils.isEmpty(areas)){
//            activitySkus = activitySkuMapper.selectOpen(areas);
            List<Integer> areaNos = areas.stream().map(x -> x.getAreaNo())
                    .collect(Collectors.toList());
            CommonResult<List<String>> commonResult = activityNewService.listActivitySkuByAreaNos(
                    areaNos);
            activitySkus = commonResult.getData();
        }

        // 查询满减、满返
        // 新增根据SKU过滤
        Set<String> skuSet = priceAdjustVOS.stream().map(PriceAdjustVO::getSku).collect(Collectors.toSet());
        List<MarketRuleVO> ruleVOList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(skuSet)) {
            ruleVOList = marketRuleMapper.selectOpen(skuSet);
        }

        for (PriceAdjustVO priceAdjustVO : priceAdjustVOS) {
            StringBuilder activity = new StringBuilder();
            if (activitySkus.contains(priceAdjustVO.getSku())) {
                activity.append("活动");
            }
            if (!CollectionUtils.isEmpty(ruleVOList)) {
                for (MarketRuleVO ruleVO : ruleVOList) {
                    if (Objects.equals(priceAdjustVO.getCityNo(), ruleVO.getAreaNo()) && Objects.equals(priceAdjustVO.getSku(), ruleVO.getSku())) {
                        if (activity.length() != 0) {
                            activity.append("，");
                        }
                        activity.append(Objects.equals(ruleVO.getType(), 0) ? "满减" : "满返");
                    }
                }
            }
            priceAdjustVO.setActivity(activity.toString());
        }

        if (PriceAdjustStatus.NORMAL.ordinal() == selectKeys.getPriceStatus()){
            return AjaxResult.getOK(PageInfoHelper.createPageInfo(priceAdjustVOS));
        } else {
            ReflectUtils.setFieldValue(pageInfo, "list", priceAdjustVOS);
            return AjaxResult.getOK(pageInfo);
        }
    }

    @Override
    public AjaxResult selectSimple(String sku, Integer status, Integer areaNo, Integer poolId) {
        PriceAdjustmentPoolVO pool = priceAdjustmentPoolMapper.selectById(poolId);

        Integer warehouseNo;
        if(Objects.isNull(pool)){
            WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(areaNo, sku);
            warehouseNo = mapping.getWarehouseNo();
        } else {
            warehouseNo = fenceService.selectWarehouseNo(pool.getAreaNo(), sku);
        }
        LocalDateTime upTime = null;
        List<PriceAdjustVO> priceAdjustVOS = new ArrayList<>();
        if (PriceAdjustStatus.NORMAL.ordinal() == status) {
            PriceAdjustVO selectKeys = new PriceAdjustVO();
            selectKeys.setSku(sku);
            selectKeys.setPriceStatus(status);
            selectKeys.setStoreNo(areaNo);
            priceAdjustVOS = priceAdjustmentMapper.selectPriceAdjustVO2(selectKeys);
            priceAdjustVOS.forEach(el -> {
                CommonResult<ActivitySku> result = activityNewService.getActivitySku(
                        el.getSku(), el.getCityNo());
                ActivitySku activitySku = result.getData();
                if (activitySku != null) {
                    AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(el.getCityNo(), el.getSku());
                    PriceInfoBO priceInfo = priceService.getNormalPrice(areaSku, true);
                    el.setPrice(priceInfo.getPrice());
                }
            });
        } else {
            //handlePriceAdjustmentPool处理价格调整的执行逻辑
            List<PriceAdjustmentPoolVO> poolVOList = Collections.emptyList();
            if (pool != null) {
                poolVOList = priceAdjustmentPoolMapper.selectByBusinessId(pool.getBusinessId());
            }
            List<InterestRateConfigVO> rateConfigList = interestRateConfigMapper.selectBySku(null, sku);
            List<AreaSkuVO> areaSkuList = poolVOList.stream().map(el -> {
                AreaSkuVO areaSku = new AreaSkuVO();
                areaSku.setAreaNo(el.getAreaNo());
                areaSku.setAreaName(el.getAreaName());
                areaSku.setSku(el.getSku());
                //这里有处理阶梯价
                areaSku.setLadderPrice(el.getLadderPrice());
                areaSku.setPrice(el.getMarketPrice());

                //活动价
                AreaSku queryCondition = new AreaSku();
                queryCondition.setAreaNo(el.getAreaNo());
                queryCondition.setSku(sku);
                AreaSku ak = areaSkuMapper.selectOne(queryCondition);
                areaSku.setOriginalPrice(ak.getPrice());

//                ActivitySku activitySku = activitySkuMapper.selectOne(el.getAreaNo(), el.getSku());

                CommonResult<ActivitySku> result = activityNewService.getActivitySku(
                        el.getSku(), el.getAreaNo());
                ActivitySku activitySku = result.getData();
                if (activitySku!=null){
                    areaSku.setActivityPrice(activitySku.getActivityPrice());
                }

                //毛利率
                for (InterestRateConfigVO configVO : rateConfigList) {
                    if (Objects.equals(el.getAreaNo(), configVO.getAreaNo())) {
                        areaSku.setAutoFlagOld(configVO.getAutoFlag());
                        areaSku.setInterestRateOld(configVO.getInterestRate());
                        break;
                    }
                }
                if (Objects.nonNull(el.getInterestRate())){
                    areaSku.setInterestRateNew(el.getInterestRate());
                }
                if (Objects.nonNull(el.getAutoFlag())){
                    areaSku.setAutoFlagNew(el.getAutoFlag());
                }

                return areaSku;
            }).collect(Collectors.toList());

            //获取城市仓信息
            WarehouseStorageCenter center = storageService.selectByWarehouseNo(warehouseNo);
            if (null != pool) {
                pool.setWarehouseNo(center.getWarehouseNo());
                pool.setWarehouseName(center.getWarehouseName());

                PriceAdjustVO vo = new PriceAdjustVO();
                vo.setAreaNo(pool.getStoreNo());
                vo.setSku(pool.getSku());
                vo.setOriginalCostPrice(pool.getOriginalCostPrice());
                vo.setOriginalMarketPrice(pool.getOriginalMarketPrice());
                vo.setMarketPrice(pool.getMarketPrice());
                vo.setPdName(pool.getPdName());
                vo.setReason(pool.getReason());
                vo.setAreaPriceAdjustment(JSON.toJSONString(areaSkuList));
                vo.setCreateAdminName(pool.getCreateAdminName());
                vo.setAddTime(pool.getCreateTime());
                vo.setCityNo(pool.getAreaNo());
                if (!CollectionUtils.isEmpty(poolVOList)) {
                    upTime = poolVOList.get(0).getUpTime();
                    vo.setSubType(poolVOList.get(0).getSubType());
                }
                priceAdjustVOS.add(vo);
            }
        }
        if (CollectionUtils.isEmpty(priceAdjustVOS)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        //规格信息
        PriceAdjustVO result = priceAdjustVOS.get(0);
        result.setUpTime(upTime);
        result.setWeight(result.getWeight());
        result.setWarehouseNo(warehouseNo);
        CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(sku,warehouseNo);
        if(cycleInventoryCost!=null){
            result.setFirstCycleCostTime(cycleInventoryCost.getFirstCycleCostTime());
            result.setFirstCycleCost(cycleInventoryCost.getFirstCycleCost());
            result.setEndCycleCost(cycleInventoryCost.getEndCycleCost());
            result.setEndCycleCostTime(cycleInventoryCost.getEndCycleCostTime());
        }
        //运营是否可调标识
        if (PriceAdjustStatus.NORMAL.ordinal() == status) {
            List<InterestRateConfigVO> configList = rateConfigService.selectBySku(areaNo, sku);
            int autoCount = Math.toIntExact(configList.stream().filter(el -> Objects.equals(1, el.getAutoFlag())).count());
            //全部开启自动调价，运营不可调整
            result.setUpdateFlag(configList.size() != autoCount);
        } else {
            if(pool != null) {
                //生效批次
                String batch = priceAdjustmentTriggerMapper.selectValidBatch(pool.getBusinessId());
                result.setValidBatch(batch);
            }
        }

        //sku类型
        boolean flag = inventoryMapper.isFruit(sku);
        result.setCategoryType(flag ? 0 : 1);

        // 获取库存记录
        List<AreaStoreVO> areaStoreVOS = areaStoreMapper.selectBySkuAndAreaNo(warehouseNo, sku);
        if(CollectionUtils.isEmpty(areaStoreVOS)){
            return AjaxResult.getOK(result);
        }
        AreaStoreVO areaStoreVO = areaStoreVOS.get(0);
        if(areaStoreVO != null){
            Integer quantity = areaStoreVO.getQuantity();
            Integer onlineQuantity = areaStoreVO.getOnlineQuantity();
            Integer lockQuantity = areaStoreVO.getLockQuantity();
            Integer safeQuantity = areaStoreVO.getSafeQuantity();
            // 可用库存
            Integer availableStock = quantity - lockQuantity - safeQuantity;
            result.setQuantity(quantity);
            result.setOnlineQuantity(onlineQuantity);
            result.setLockQuantity(lockQuantity);
            result.setSafeQuantity(safeQuantity);
            result.setAvailableStock(availableStock);
        }
        return AjaxResult.getOK(result);
    }


    @Override
    public AjaxResult<PriceAdjustDetailVO> selectDetail(String sku, Integer status, Integer areaNo, Integer poolId) {
        logger.info("qurey price adjust detail,sku:{}, status:{}, areaNo:{}, poolId:{}", sku, status, areaNo, poolId);

        // 1.获取原本的的一大坨逻辑
        PriceAdjustmentPoolVO pool = priceAdjustmentPoolMapper.selectById(poolId);
        Integer warehouseNo;
        if(Objects.isNull(pool)){
            WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(areaNo, sku);
            warehouseNo = mapping.getWarehouseNo();
        } else {
            warehouseNo = fenceService.selectWarehouseNo(pool.getAreaNo(), sku);
        }
        LocalDateTime upTime = null;
        List<PriceAdjustVO> priceAdjustVOS = new ArrayList<>();
        if (PriceAdjustStatus.NORMAL.ordinal() == status) {
            PriceAdjustVO selectKeys = new PriceAdjustVO();
            selectKeys.setSku(sku);
            selectKeys.setPriceStatus(status);
            selectKeys.setStoreNo(areaNo);
            priceAdjustVOS = priceAdjustmentMapper.selectPriceAdjustVO2(selectKeys);
            priceAdjustVOS.forEach(el -> {
                CommonResult<ActivitySku> result = activityNewService.getActivitySku(
                        el.getSku(), el.getCityNo());
                ActivitySku activitySku = result.getData();
                if (activitySku != null) {
                    AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(el.getCityNo(), el.getSku());
                    PriceInfoBO priceInfo = priceService.getNormalPrice(areaSku, true);
                    el.setPrice(priceInfo.getPrice());
                }
            });
        } else {
            //handlePriceAdjustmentPool处理价格调整的执行逻辑
            List<PriceAdjustmentPoolVO> poolVOList = Collections.emptyList ();
            if(pool != null) {
                poolVOList =priceAdjustmentPoolMapper.selectByBusinessId(pool.getBusinessId());
            }
            List<InterestRateConfigVO> rateConfigList = interestRateConfigMapper.selectBySku(null, sku);
            List<AreaSkuVO> areaSkuList = poolVOList.stream().map(el -> {
                AreaSkuVO areaSku = new AreaSkuVO();
                areaSku.setAreaNo(el.getAreaNo());
                areaSku.setAreaName(el.getAreaName());
                areaSku.setSku(el.getSku());
                //这里有处理阶梯价
                //areaSku.setLadderPrice(el.getLadderPrice());
                areaSku.setPrice(el.getMarketPrice());

                //活动价
                AreaSku queryCondition = new AreaSku();
                queryCondition.setAreaNo(el.getAreaNo());
                queryCondition.setSku(sku);
                AreaSku ak = areaSkuMapper.selectOne(queryCondition);
                areaSku.setOriginalPrice(ak.getPrice());

//                ActivitySku activitySku = activitySkuMapper.selectOne(el.getAreaNo(), el.getSku());

                CommonResult<ActivitySku> result = activityNewService.getActivitySku(
                        el.getSku(), el.getAreaNo());
                ActivitySku activitySku = result.getData();
                if (activitySku!=null){
                    //areaSku.setActivityPrice(activitySku.getActivityPrice());
                    areaSku.setActivityLadderPrice(activitySku.getLadderPrice());
                }

                //毛利率
                for (InterestRateConfigVO configVO : rateConfigList) {
                    if (Objects.equals(el.getAreaNo(), configVO.getAreaNo())) {
                        areaSku.setAutoFlagOld(configVO.getAutoFlag());
                        areaSku.setInterestRateOld(configVO.getInterestRate());
                        break;
                    }
                }
                if (Objects.nonNull(el.getInterestRate())){
                    areaSku.setInterestRateNew(el.getInterestRate());
                }
                if (Objects.nonNull(el.getAutoFlag())){
                    areaSku.setAutoFlagNew(el.getAutoFlag());
                }

                return areaSku;
            }).collect(Collectors.toList());

            //获取城市仓信息
            WarehouseStorageCenter center = storageService.selectByWarehouseNo(warehouseNo);
            if (null != pool && null != center) {
                pool.setWarehouseNo(center.getWarehouseNo());
                pool.setWarehouseName(center.getWarehouseName());

                PriceAdjustVO vo = new PriceAdjustVO();
                vo.setAreaNo(pool.getStoreNo());
                vo.setSku(pool.getSku());
                vo.setOriginalCostPrice(pool.getOriginalCostPrice());
                vo.setOriginalMarketPrice(pool.getOriginalMarketPrice());
                vo.setMarketPrice(pool.getMarketPrice());
                vo.setPdName(pool.getPdName());
                vo.setReason(pool.getReason());
                vo.setAreaPriceAdjustment(JSON.toJSONString(areaSkuList));
                vo.setCreateAdminName(pool.getCreateAdminName());
                vo.setAddTime(pool.getCreateTime());
                vo.setCityNo(pool.getAreaNo());
                if (!CollectionUtils.isEmpty(poolVOList)) {
                    upTime = poolVOList.get(0).getUpTime();
                    vo.setSubType(poolVOList.get(0).getSubType());
                }
                priceAdjustVOS.add(vo);
            }
        }
        if (CollectionUtils.isEmpty(priceAdjustVOS)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        //规格信息
        PriceAdjustVO vo = priceAdjustVOS.get(0);
        vo.setUpTime(upTime);
        vo.setWeight(vo.getWeight());
        vo.setWarehouseNo(warehouseNo);
        CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(sku,warehouseNo);
        if(cycleInventoryCost!=null){
            vo.setFirstCycleCostTime(cycleInventoryCost.getFirstCycleCostTime());
            vo.setFirstCycleCost(cycleInventoryCost.getFirstCycleCost());
            vo.setEndCycleCost(cycleInventoryCost.getEndCycleCost());
            vo.setEndCycleCostTime(cycleInventoryCost.getEndCycleCostTime());
            vo.setCurrentCostUpdateTime(cycleInventoryCost.getCurrentCostUpdateTime());
        }
        //运营是否可调标识
        if (PriceAdjustStatus.NORMAL.ordinal() == status) {
            List<InterestRateConfigVO> configList = rateConfigService.selectBySku(areaNo, sku);
            int autoCount = Math.toIntExact(configList.stream().filter(el -> Objects.equals(1, el.getAutoFlag())).count());
            //全部开启自动调价，运营不可调整
            vo.setUpdateFlag(configList.size() != autoCount);
        } else {
            //生效批次
            if(pool != null) {
                String batch = priceAdjustmentTriggerMapper.selectValidBatch (pool.getBusinessId ());
                vo.setValidBatch (batch);
            }
        }
        //sku类型
        boolean flag = inventoryMapper.isFruit(sku);
        vo.setCategoryType(flag ? 0 : 1);

        // 获取库存记录
        List<AreaStoreVO> areaStoreVOS = areaStoreMapper.selectBySkuAndAreaNo(warehouseNo, sku);
        if (!CollectionUtils.isEmpty(areaStoreVOS)) {
            AreaStoreVO areaStoreVO = areaStoreVOS.get(0);
            if (areaStoreVO != null) {
                Integer quantity = areaStoreVO.getQuantity();
                Integer onlineQuantity = areaStoreVO.getOnlineQuantity();
                Integer lockQuantity = areaStoreVO.getLockQuantity();
                Integer safeQuantity = areaStoreVO.getSafeQuantity();
                // 可用库存
                Integer availableStock = quantity - lockQuantity - safeQuantity;
                vo.setQuantity(quantity);
                vo.setOnlineQuantity(onlineQuantity);
                vo.setLockQuantity(lockQuantity);
                vo.setSafeQuantity(safeQuantity);
                vo.setAvailableStock(availableStock);
            }
        }

        // ------------------上面是保持老的逻辑，下面是构建新的商品信息-----------------
        // 2.获取商品信息
        Inventory selectKey = new Inventory();
        selectKey.setSku(sku);
        Inventory inventory = inventoryMapper.selectOne(selectKey);
        if(inventory == null) {
            logger.warn("商品信息不存在，sku：{}", sku);
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        Products products = productsMapper.selectByPrimaryKey(inventory.getPdId());
        if(products == null) {
            logger.warn("商品信息不存在，pdId：{}", inventory.getPdId());
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        // 3.构建返回信息
        PriceAdjustDetailVO result = new PriceAdjustDetailVO();
        CommodityVO commodityVO = new CommodityVO();
        commodityVO.setSku(sku);
        commodityVO.setPdImage(StringUtils.isBlank(inventory.getSkuPic()) ? products.getDetailPicture() : inventory.getSkuPic());
        commodityVO.setPdName(StringUtils.isBlank(inventory.getSkuName()) ? products.getPdName() : inventory.getSkuName());
        commodityVO.setWeight(inventory.getWeight());
        commodityVO.setSubType(inventory.getSubType());
        commodityVO.setAreaNo(vo.getAreaNo());
        commodityVO.setWarehouseNo(warehouseNo);

        //查询商品标签
        List<MarketItemDetailDTO> itemDetailByOutId = marketProviderFacade.getItemDetailByOutId(Collections.singletonList(inventory.getInvId()));
        if (!CollectionUtils.isEmpty(itemDetailByOutId)) {
            commodityVO.setItemLabel(itemDetailByOutId.get(0).getItemLabel());
        }

        result.setCommodityVO(commodityVO);
        result.setOldPriceAdjustVO(vo);
        return AjaxResult.getOK(result);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult operationAdjustSave(PriceAdjustment priceAdjustment) {
        // TODO: 2022/6/10 优化点，校验后可以直接返回 areaSkus，没必要再做一次json
        //校验调整的数据
        checkPrice(priceAdjustment.getAreaPriceAdjustment());

        if (priceAdjustment.getUpTime() != null && LocalDateTime.now().isAfter(priceAdjustment.getUpTime())) {
            throw new DefaultServiceException("生效时间已过期,请重新指定生效时间");
        }

        Long nextBusinessId = SnowflakeUtil.nextId();
        List<AreaSkuVO> areaSkus = JSON.parseArray(priceAdjustment.getAreaPriceAdjustment(), AreaSkuVO.class);

        Integer id = null;
        for (AreaSkuVO el : areaSkus) {
            //将未审核数据改为已取消
            priceAdjustmentPoolMapper.updateStatus(el.getAreaNo(), el.getSku(), PriceAdjustPoolStatus.WAIT_AUDIT.getStatus(), PriceAdjustPoolStatus.AUDIT_REFUSE.getStatus(),null);

            //查询原售价
            AreaSku query = new AreaSku();
            query.setSku(el.getSku());
            query.setAreaNo(el.getAreaNo());
            AreaSku areaSku = areaSkuMapper.selectOne(query);

            PriceAdjustmentPool pool = new PriceAdjustmentPool();
            pool.setAreaNo(el.getAreaNo());
            pool.setSku(priceAdjustment.getSku());
            pool.setStatus(PriceAdjustPoolStatus.WAIT_AUDIT.getStatus());
            pool.setOriginalMarketPrice(areaSku.getPrice());
            pool.setMarketPrice(el.getPrice());
            // pool.setLadderPrice(el.getLadderPrice());
            pool.setReason(priceAdjustment.getReason());
            pool.setCreateAdminName(getAdminName());
            pool.setCreateTime(LocalDateTime.now());
            pool.setUpTime(priceAdjustment.getUpTime());
            pool.setBusinessId(nextBusinessId);
            pool.setRuleFlag(2);

            InterestRateConfig rateConfig = interestRateConfigMapper.selectByAreaNoAndSku(el.getAreaNo(), el.getSku());
            if (Objects.isNull(rateConfig)){
                pool.setInterestRate(el.getInterestRateNew());
                pool.setAutoFlag(el.getAutoFlagNew());
            }else {
                // 当毛利率修改了,interestRateOld才有值,才需要吧修改后的毛利率存到调价池里
                if (Objects.nonNull(el.getInterestRateOld())){
                    pool.setInterestRate(el.getInterestRateNew());
                }
                pool.setAutoFlag(el.getAutoFlagNew());
            }



            if (el.getAutoFlagNew() == null) {
                //重新发起时查询原businessId
                Long businessId = priceAdjustmentPoolMapper.selectBusinessId(el.getAreaNo(), el.getSku(), el.getOriginalPrice(), el.getPrice());
                if (businessId != null) {
                    //复制触发数据
                    List<PriceAdjustmentTrigger> triggerList = priceAdjustmentTriggerMapper.selectByBusinessId(businessId);
                    triggerList.forEach(trigger -> {
                        trigger.setId(null);
                        trigger.setBusinessId(nextBusinessId);
                        priceAdjustmentTriggerMapper.insertSelective(trigger);
                    });

                    List<PriceAdjustmentPoolVO> voList = priceAdjustmentPoolMapper.selectByBusinessId(businessId);
                    if (!CollectionUtils.isEmpty(voList)) {
                        PriceAdjustmentPool tempPool = priceAdjustmentPoolMapper.selectByPrimaryKey(voList.get(0).getId());
                        pool.setPrice(tempPool.getPrice());
                        pool.setOriginalPrice(tempPool.getOriginalPrice());
                    }
                }
            }
            priceAdjustmentPoolMapper.insertSelective(pool);
            id = pool.getId();
        }
        logger.info("id"+nextBusinessId);
        return AjaxResult.getOK(id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult purchaseAdjust(PriceAdjustment priceAdjustment) {
        selectInFlow(priceAdjustment.getSku(), priceAdjustment.getAreaNo());
        //先更新sku
        AreaStore areaStore = new AreaStore();
        areaStore.setSku(priceAdjustment.getSku());
        areaStore.setAreaNo(priceAdjustment.getAreaNo());
        areaStore.setCostPrice(priceAdjustment.getCostPrice());
        areaStore.setMarketPrice(priceAdjustment.getMarketPrice());
        areaStore.setPriceStatus(Integer.valueOf(priceAdjustment.getStatus()));
        int rs = areaStoreMapper.update(areaStore);
        if (rs != 1) {
            throw new DefaultServiceException(ResultConstant.PARAM_FAULT);
        }
        //采购调整/状态变成待处理
        priceAdjustment.setStatus((byte) PriceAdjustStatus.WAIT_HANDLE.ordinal());
        priceAdjustment.setAddTime(LocalDateTime.now());
        rs = priceAdjustmentMapper.insert(priceAdjustment);
        if (rs != 1) {
            throw new DefaultServiceException(ResultConstant.PARAM_FAULT);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult select(int pageIndex, int pageSize, PriceAdjustVO selectKeys) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PriceAdjustVO> priceAdjustVOS = priceAdjustmentMapper.selectPriceAdjustVORecord(selectKeys);

        return AjaxResult.getOK(PageInfoHelper.createPageInfo(priceAdjustVOS));
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult auditAdjust(PriceAdjustment priceAdjustment) {
        logger.info("开始调价,data:{}", JSON.toJSONString(priceAdjustment));
        //校验调整的数据
        checkPrice(priceAdjustment.getAreaPriceAdjustment());
        Integer poolId = priceAdjustment.getId();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("auditAdjust581");
        PriceAdjustmentPoolVO poolVO = priceAdjustmentPoolMapper.selectByIdForceMaster(poolId);
        //有待执行且时间一致的
        int num1 = priceAdjustmentPoolMapper.selectByCondition(poolVO.getAreaNo(), poolVO.getSku(), PriceAdjustPoolStatus.WAIT_ADJUST.getStatus(), priceAdjustment.getUpTime(), 1);
        if(num1!=0){
            throw new DefaultServiceException("已存在相同生效时间的待执行任务");
        }
        //有待执行的
        int num2 = priceAdjustmentPoolMapper.selectByCondition(poolVO.getAreaNo(), poolVO.getSku(), PriceAdjustPoolStatus.WAIT_ADJUST.getStatus(), priceAdjustment.getUpTime(), 2);
        if(num2 > 1){
            throw new DefaultServiceException("已经存在两个待执行任务");
        }

        String areaPriceAdjustment = priceAdjustment.getAreaPriceAdjustment();
        List<AreaSku> areaSkus = JSON.parseArray(areaPriceAdjustment, AreaSku.class);

        boolean jobFlag = false;
        boolean execFlag = false;
        stopWatch.stop();
        logger.info("任务名称:{},任务耗时:{}",stopWatch.getId(),stopWatch.getLastTaskTimeMillis());
        stopWatch.start("auditAdjust602");
        if (PriceAdjustStatus.AUDIT_REFUSE.ordinal() == priceAdjustment.getStatus()) {
            //审核不通过
            priceAdjustmentPoolMapper.updateStatusByBusinessId(PriceAdjustPoolStatus.AUDIT_REFUSE.getStatus(), poolVO.getBusinessId());
        } else if (PriceAdjustStatus.WAIT_ADJUST.ordinal() == priceAdjustment.getStatus()) {
            if (priceAdjustment.getUpTime() == null) {
                //立即执行
                priceAdjustment.setUpTime(LocalDateTime.now());
                execFlag = true;
            } else {
                //定时执行部分
                if (LocalDateTime.now().isAfter(priceAdjustment.getUpTime())) {
                    throw new DefaultServiceException("生效时间已过期,请重新指定生效时间");
                }
                jobFlag = true;
            }

            //将最新通过更新为调价池状态为待执行
            priceAdjustmentPoolMapper.updateStatus2waitAdjust(poolVO.getBusinessId(), priceAdjustment.getUpTime());
        }
        stopWatch.stop();
        logger.info("任务名称:{},任务耗时:{}",stopWatch.getId(),stopWatch.getLastTaskTimeMillis());
        stopWatch.start("auditAdjust628 更新调价池价格");
        //更新调价池价格
        areaSkus.forEach(el -> priceAdjustmentPoolMapper.updateByBusinessIdAndAreaNo(el.getPrice(), el.getLadderPrice(), poolVO.getBusinessId(), el.getAreaNo(), el.getSku()));
        stopWatch.stop();
        logger.info("任务名称:{},任务耗时:{}",stopWatch.getId(),stopWatch.getLastTaskTimeMillis());

        //添加待执行记录
        PriceAdjustmentPool tempPool = priceAdjustmentPoolMapper.selectByPrimaryKeyForceMaster(poolVO.getId());
        priceAdjustment.setOriginalMarketPrice(tempPool.getOriginalPrice());
        priceAdjustment.setMarketPrice(tempPool.getPrice());
        priceAdjustment.setCostPrice(poolVO.getCostPrice());
        priceAdjustment.setOriginalCostPrice(poolVO.getOriginalCostPrice());
        priceAdjustment.setPdName(poolVO.getPdName());
        priceAdjustment.setAddTime(LocalDateTime.now());
        priceAdjustment.setCreateAdminName(poolVO.getCreateAdminName());
        priceAdjustmentMapper.insert(priceAdjustment);

        logger.info("完成调价单记录,data:{}", JSON.toJSONString(priceAdjustment));
        PriceAdjustmentMsgDTO msgDTO = new PriceAdjustmentMsgDTO();
        msgDTO.setId(priceAdjustment.getId());
        msgDTO.setBusinessId(poolVO.getBusinessId());
        msgDTO.setUpTime(priceAdjustment.getUpTime());
        //立即执行
        if (execFlag) {
            stopWatch.start("auditAdjust648 立即执行");
            getContext().getBean(PriceAdjustService.class).executePriceAdjust(priceAdjustment.getId());
            stopWatch.stop();
            logger.info("任务名称:{},任务耗时:{}",stopWatch.getId(),stopWatch.getLastTaskTimeMillis());
        } else if (jobFlag) {
            //延时消息队列处理
            mqProducer.sendStartDeliver(ProductMqConstant.PRICE_ADJUSTMENT, ProductMqConstant.TAG_NORMAL_ADJUSTMENT, JSONObject.toJSONString(msgDTO), msgDTO.getUpTime());
        }
        logger.info("任务名称:{},任务耗时:{}",stopWatch.getId(),stopWatch.getLastTaskTimeMillis());
        logger.info("AuditAdjust 总耗时:{}",stopWatch.getTotalTimeMillis());
        logger.info("AuditAdjust 时间分布:{}",stopWatch.prettyPrint());
        return AjaxResult.getOK(auditResult(priceAdjustment.getAreaPriceAdjustment()));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult cancelAdjust(int id) {
        PriceAdjustmentPool pool = priceAdjustmentPoolMapper.selectById(id);
        if (pool == null) {
            return AjaxResult.getError(ResultConstant.RECORD_NOT_EXIST);
        }
        if (pool.getStatus() != 1) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        priceAdjustmentPoolMapper.updateStatusByBusinessId(4, pool.getBusinessId());

        //默认只有一条待执行调价，所以可以这么操作
        Integer warehouseNo = fenceService.selectWarehouseNo(pool.getAreaNo(), pool.getSku());
        priceAdjustmentMapper.update2cancel(warehouseNo, pool.getSku());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectLeftInStock(Integer areaNo, String sku) {

        Integer storeNo = fenceService.selectStoreNoByAreaNo(areaNo);

        WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
        List<StoreRecordVO> storeRecordVOS = storeRecordMapper.selectLeftInStock(mapping.getWarehouseNo(), sku);
        List<StoreRecordVO> collect = storeRecordVOS.stream().filter(x -> x.getStoreQuantity() > 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return AjaxResult.getOK(collect);
        }
        for (StoreRecordVO storeRecordVO : collect) {
            String batch = storeRecordVO.getBatch();
            storeRecordVO.setAreaNo(mapping.getWarehouseNo());
            storeRecordVO.setSku(sku);
            //获取最新入库类型批次信息
            StoreRecord storeRecord = storeRecordMapper.selectInStoreType(storeRecordVO);
            if(Objects.nonNull(storeRecord)){
                storeRecordVO.setType(storeRecord.getType());
                storeRecordVO.setUpdateTime(storeRecord.getUpdateTime());
            }
            PurchasesPlan purchasesPlan = purchasesPlanMapper.selectOriginBySku(batch, sku);
            BigDecimal marketPrice = Objects.isNull(purchasesPlan) ? BigDecimal.ZERO : Objects.isNull(purchasesPlan.getMarketPrice()) ? BigDecimal.ZERO : purchasesPlan.getMarketPrice();
            storeRecordVO.setMarketPrice(marketPrice);

        }
        List<StoreRecordVO> sortStoreRecordVO = collect.stream().sorted(Comparator.comparing(StoreRecordVO::getUpdateTime).reversed()).collect(Collectors.toList());

        return AjaxResult.getOK(sortStoreRecordVO);
    }

//    @Override
//    public void init() {
//        logger.info("调价定时任务初始化开始");
//        List<PriceAdjustment> waitAdjustList = priceAdjustmentMapper.selectByStatus(PriceAdjustStatus.WAIT_ADJUST.ordinal());
//        for (PriceAdjustment adjustment : waitAdjustList) {
//            if (adjustment.getUpTime().isAfter(LocalDateTime.now().plus(10, ChronoUnit.SECONDS))) {
//                ScheduleJob job = new ScheduleJob(adjustment.getId().toString(), JobGroupType.PRICE_ADJUST);
//                job.setCronExpression(adjustment.getUpTime().format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
//                jobManage.addJob(job);
//                logger.info("调价任务id:{} 执行时间为: {}", adjustment.getId(), adjustment.getUpTime());
//            } else {
//                try {
//                    executePriceAdjust(adjustment.getId());
//                } catch (Exception e) {
//                    logger.error("调价定时任务初始化异常:{}", adjustment.getId(), e);
//                }
//            }
//        }
//        logger.info("调价定时任务初始化结束");
//    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @XmLock(waitTime = 1000 * 60, key = "(PriceAdjustService.executePriceAdjust):{id}")
    public void executePriceAdjust(Integer id) {
        logger.info("自动调价开始执行，id：{}", id);
        StopWatch stopWatch = new StopWatch();
        PriceAdjustment adjustment = priceAdjustmentMapper.selectByPrimaryKey(id);
        if (adjustment.getStatus() == PriceAdjustStatus.WAIT_ADJUST.ordinal()) {
            List<AreaSku> areaSkuList = JSON.parseArray(adjustment.getAreaPriceAdjustment(), AreaSku.class);
            logger.info("自动调价执行内容：{}", JSONObject.toJSONString(areaSkuList));
            if (!CollectionUtils.isEmpty(areaSkuList)) {

                stopWatch.start("executePriceAdjust738");
                //执行调价
                String updater = adjustment.getCreateAdminName() == null ? "price_adjustment:" + id : adjustment.getCreateAdminName();
                areaSkuList.stream().forEach(areaSku -> areaSku.setUpdater(updater));
                this.priceAdjust(areaSkuList,0);
                stopWatch.stop();
                logger.info("任务名称:{},任务耗时:{}",stopWatch.getId(),stopWatch.getLastTaskTimeMillis());
                stopWatch.start("executePriceAdjust750");

                PriceAdjustment update = new PriceAdjustment();
                update.setId(id);
                update.setStatus((byte) PriceAdjustStatus.ADJUSTED.ordinal());
                update.setUpdateTime(new Date());
                priceAdjustmentMapper.updateByPrimaryKeySelective(update);
            }
            List<PriceAdjustRecord> priceAdjustRecords = JSON.parseArray(adjustment.getAreaPriceAdjustment(), PriceAdjustRecord.class);
            logger.info("自动调价记录{}",JSONObject.toJSONString(priceAdjustRecords));
            if (!CollectionUtils.isEmpty(priceAdjustRecords)){
                priceAdjustRecordMapper.insertBatch(priceAdjustRecords);
            }
            stopWatch.stop();
            logger.info("executePriceAdjust 总耗时:{}",stopWatch.getTotalTimeMillis());
            logger.info("executePriceAdjust 时间分布:{}",stopWatch.prettyPrint());
        } else {
            logger.info("自动调价不为待执行状态，不执行");
        }
        logger.info("自动调价结束执行，id：{}", id);
    }


    /**
     * 价格变化后调整其他位置价格
     * @param areaSkuList 变化后的价格列表
     * @param type :0：普通调价 1：调价单调价
     */
    private void priceAdjust(List<AreaSku> areaSkuList,Integer type) {
        logger.info("开始执行价格调整。操作人：{}, type:{}, areaSkuList:{}", this.getAdminName(), type, JSON.toJSONString(areaSkuList));
        // key为warehouseNo value为Set<AreaSku>
        Map<Integer, Set<AreaSku>> warehouseSkuMap = new HashMap<>(16);
        List<AreaSkuVO>  notifyList = new ArrayList<>();
        Set<String> skus = new HashSet<>();

        for (AreaSku areaSku : areaSkuList) {
            // 1.更新原价
            areaSkuMapper.updatePriceByAreaSku(areaSku);
            if (Objects.equals(type, 0)) {
                //更新调价池为已执行
                priceAdjustmentPoolMapper.updateStatus2success(areaSku.getAreaNo(), areaSku.getSku(), LocalDateTime.now());
            }

            //2.處理营销价审批 （活动相关）
            priceStrategyAuditService.initPriceStrategyAudit(areaSku.getAreaNo(), areaSku.getSku(), areaSku.getPrice(), null, null);

            // 3.大客户低价监控
            this.lowPriceWhenPriceChange(areaSku.getAreaNo(), areaSku.getSku());

            //获取库存使用仓
            Integer warehouseNo = fenceService.selectWarehouseNo(areaSku.getAreaNo(), areaSku.getSku());
            Set<AreaSku> areaSkus = warehouseSkuMap.get(warehouseNo);
            if(CollectionUtils.isEmpty(areaSkus)){
                areaSkus = new HashSet<>();
                areaSkus.add(areaSku);
                warehouseSkuMap.put(warehouseNo, areaSkus);
            }else{
                Optional<AreaSku> areaSkuOptional = areaSkus.stream()
                        .filter(priceRiskAreaSku -> priceRiskAreaSku.getSku().equals(areaSku.getSku()))
                        .findAny();
                if(!areaSkuOptional.isPresent()){
                    areaSkus.add(areaSku);
                }
            }
            skus.add(areaSku.getSku());
            //判断是否是临保SKU
            List<AreaSkuVO> nearExpiredSku = areaSkuMapper.selectNearExpiredSku(AreaSkuEnum.EXT_TYPE_EXPIRED.getExtType(), areaSku.getAreaNo(), areaSku.getSku());
            if (!CollectionUtils.isEmpty(nearExpiredSku)) {
                nearExpiredSku.stream().forEach(i -> {
                    if (i != null && i.getWarnTime() == null) {
                        i.setWarnTime(10);
                    }
                });
                notifyList.add(nearExpiredSku.get(0));
            }

            // 外部对接，向外部平台推送商品价格信息
            orderOuterInfoService.pushOnSaleUpdatePrice(areaSku.getSku(),areaSku.getAreaNo(),null,PushTypeEnum.UPDATEPRICE.getPushType());


            // 调整毛利率和自动调价
            if (Objects.isNull(areaSku.getInterestRateNew())){
                continue;
            }
            InterestRateConfig interestRateConfig = interestRateConfigMapper.selectByAreaNoAndSku(areaSku.getAreaNo(), areaSku.getSku());
            logger.info("开始处理毛利率信息。old:{}, new:{}", JSON.toJSONString(interestRateConfig), JSON.toJSONString(areaSku));
            if (interestRateConfig == null){
                InterestRateConfig rateConfig = new InterestRateConfig();
                rateConfig.setInterestRate(areaSku.getInterestRateNew());
                rateConfig.setAutoFlag(areaSku.getAutoFlagNew());
                rateConfig.setSku(areaSku.getSku());
                rateConfig.setAreaNo(areaSku.getAreaNo());
                rateConfig.setCreateTime(new Date());
                rateConfig.setCreateAdminName(getAdminName());
                interestRateConfigMapper.insertSelective(rateConfig);
            }else {
                interestRateConfig.setInterestRate(areaSku.getInterestRateNew());
                interestRateConfig.setAutoFlag(areaSku.getAutoFlagNew());
                interestRateConfigMapper.updateConfigById(interestRateConfig);
            }
        }

        //生成临保活动池并提醒优化
        if (!CollectionUtils.isEmpty(notifyList)){
            activityService.sendDiscountNotification(notifyList);
        }

        // 倒挂监控逻辑
        adjustPriceRiskControl(warehouseSkuMap, skus);
        logger.info("调价执行完成!");

    }

    private void lowPriceWhenPriceChange(Integer areaNo, String sku){
        Admin currentUser = this.getCurrentUser();
        if(null != currentUser) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    logger.info("人工发起审批操作，开始处理大客户低价监控.areaNo:{}, sku:{}", areaNo, sku);
                    majorPriceService.lowPriceWhenPriceChange(areaNo, sku);
                }
            });
        }
    }

    /**
     * sku倒挂监控逻辑
     * @param warehouseSkuMap 需要做倒挂监控的仓库sku
     * @param skus 当前调价的所有sku（产品编号）
     */
    private void adjustPriceRiskControl(Map<Integer, Set<AreaSku>> warehouseSkuMap, Set<String> skus) {
        // 不需要倒挂监控的sku性质
        Set<String> noPriceRiskSkus;
        if(!CollectionUtils.isEmpty(skus)){
            List<Integer> inventoryExtType = Lists.newArrayList(InventoryExtTypeEnum.ACTIVITY.type(), InventoryExtTypeEnum.TEMPORARY_INSURANCE.type(),
                    InventoryExtTypeEnum.NOT_SALE.type(), InventoryExtTypeEnum.BROKEN_BAG.type());
            noPriceRiskSkus = inventoryMapper.selectBySkusAndExtTypes(skus, inventoryExtType);
        }else{
            noPriceRiskSkus = new HashSet<>();
        }

        for(Map.Entry<Integer, Set<AreaSku>> me : warehouseSkuMap.entrySet()){
            Integer warehouseNo = me.getKey();
            Set<AreaSku> areaSkus = me.getValue();
            if (CollectionUtils.isEmpty(areaSkus)) {
                continue;
            }
            for (AreaSku areaSku : areaSkus) {
                // 如果当前sku不需要倒挂监控，则不做提醒
                if (noPriceRiskSkus.contains(areaSku.getSku())) {
                    logger.info("售价调整,当前sku性质不做倒挂监控,sku:{}", areaSku);
                    continue;
                }
                priceRiskTip(areaSku, warehouseNo);
            }
        }
    }

    @Lazy
    @Resource
    private PriceAdjustService priceAdjustService;

    /**
     * 价格风控判断，判断是否成本倒挂
     * @param areaSku 区域sku信息
     * @param warehouseNo 库存仓编号
     */
    private void priceRiskTip(AreaSku areaSku, Integer warehouseNo) {
        // 查询当前区域此sku最新周期成本
        CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(areaSku.getSku(), warehouseNo);
        if (cycleInventoryCost != null && cycleInventoryCost.getFirstCycleCost() != null) {
            // 最新周期成本
            BigDecimal costPrice = cycleInventoryCost.getFirstCycleCost();
            // 最新周期成本时间
            LocalDateTime firstCycleCostTime = cycleInventoryCost.getFirstCycleCostTime();
            String batch = DateUtils.localDateTimeToStringThree(firstCycleCostTime);
            SkuMinPriceVO skuMinPriceVO = areaSkuService.selectSkuAreaPrice(areaSku.getSku(), warehouseNo);
            if(skuMinPriceVO != null && skuMinPriceVO.getPrice() != null){
                if(costPrice.compareTo(skuMinPriceVO.getPrice()) > 0){
                    priceAdjustService.sendDingDingMsg(costPrice, skuMinPriceVO.getPrice(), areaSku.getAreaNo(), areaSku.getSku(), warehouseNo, batch);
                }
            }else{
                logger.info("当前sku在库存仓对应城市中无售价信息,sku:{},库存仓:{}", areaSku.getSku(), warehouseNo);
            }
        }else{
            CostChangeVo costChangeVo = areaStoreMapper.selectLastBatchCostPriceBySkuAndAreaNo(warehouseNo, areaSku.getSku());
            if(costChangeVo == null || costChangeVo.getCostPrice() == null){
                logger.info("当前sku在库存仓无最新批次成本,sku:{},库存仓:{}", areaSku.getSku(), warehouseNo);
                return;
            }
            SkuMinPriceVO skuMinPriceVO = areaSkuService.selectSkuAreaPrice(areaSku.getSku(), warehouseNo);
            if(skuMinPriceVO != null && skuMinPriceVO.getPrice() != null){
                if(costChangeVo.getCostPrice().compareTo(skuMinPriceVO.getPrice()) > 0){
                    priceAdjustService.sendDingDingMsg(costChangeVo.getCostPrice(), skuMinPriceVO.getPrice(), areaSku.getAreaNo(), areaSku.getSku(), warehouseNo, costChangeVo.getBatch());
                }
            }else{
                logger.info("当前sku在库存仓对应城市中无售价信息,sku:{},库存仓:{}", areaSku.getSku(), warehouseNo);
            }
        }
    }

    /**
     * 校验价格调整的阶梯价
     * @param price 售卖价
     * @param ladderPrice 阶梯价
     */
    private void checkLadderPrice(BigDecimal price,String ladderPrice, BigDecimal costPrice, Integer areaNo) {
        if (StringUtils.isNotBlank(ladderPrice) && !"[]".equals(ladderPrice)) {
            List<LadderPriceVO> ladderPriceVOS = JSON.parseArray(ladderPrice, LadderPriceVO.class);
            for (LadderPriceVO ladderPriceVO : ladderPriceVOS) {
                if (Objects.isNull(ladderPriceVO.getAdjustType()) || Objects.isNull(ladderPriceVO.getAmount())){
                    continue;
                }

                // 根据售卖价得出最后的阶梯价
                PriceStrategy strategy = new PriceStrategy();
                strategy.setAdjustType(ladderPriceVO.getAdjustType());
                strategy.setAmount(ladderPriceVO.getAmount());
                strategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                PriceStrategyAuditRecordVO vo = priceStrategyService.calcStrategyPrice(strategy, costPrice, price);
                BigDecimal resultPrice = vo.getNewPrice();
                if (resultPrice.compareTo(ladderPriceVO.getPrice()) != 0){
                    logger.error("阶梯价计算错误,areaNo:{},新售价:{},售卖价:{},前端参数计算的阶梯价:{}",areaNo,resultPrice,price,ladderPrice);
                    throw new DefaultServiceException("阶梯价计算错误");
                }
            }
        }
    }

    @Async
    @Override
    @XmLock(waitTime = 1000 * 60, key = "(PriceAdjustService.sendDingDingMsg)")
    public void sendDingDingMsg(BigDecimal costPrice, BigDecimal price, Integer areaNo,
                                             String sku, Integer warehouseNo,
                                             String batchNo) {
        try {
            Config config = configMapper.selectOne(ConfigValueEnum.COST_PRODUCTS_SENDER_ROBOT.getKey());

            InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
            String warehouseName = Global.warehouseMap.get(warehouseNo);
            if (StringUtils.isEmpty(warehouseName)) {
                logger.error("未从Global.warehouseMap中获取到仓库名称,仓库编号:{}", warehouseNo);
                warehouseName = "";
            }
            // 查询sku在所有区域
            String title = "【倒挂提醒】 " ;
            StringBuilder content = new StringBuilder("##### " + title + "\n");
            content.append("> ###### 描述：").append(inventoryVO.getPdName() + " " + inventoryVO.getWeight() + " 在" + warehouseName + " 价格倒挂").append("\n");
            content.append("> ###### SKU：").append(sku).append("\n");
            content.append("> ###### 比对成本：").append(new DecimalFormat("#.00").format(costPrice)).append("\n");
            content.append("> ###### 批次号：").append(batchNo).append("\n");
            content.append("> ###### 当前售价：").append(price).append("\n");
            Map<String, String> msg = new HashMap<>();
            msg.put("title", title);
            msg.put("text", content.toString());
            msg.put("categoryType", CategoryTypeEnum.getValue(inventoryVO.getCategoryType()));
            msg.put("subType", SubTypeEnum.getValueByKey(inventoryVO.getSubType()));
            if(dynamicConfig.getEnableFeiShuRobotForPriceAdjustment()) {
                DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> msg, null);
            }
            logger.info("基于sls发送成本倒挂消息：{}",  JSON.toJSONString(msg));
        } catch (Exception e) {
            logger.error("倒挂钉钉提醒异常,costPrice:{}, price:{}, areaNo:{}, sku:{}, warehouseNo:{}, batchNo:{}",
                    costPrice, price, areaNo, sku, warehouseNo, batchNo, e);
        }

    }

    @Override
    public AjaxResult selectRule() {
        List<Integer> areaNo = priceAdjustmentRuleAreaMapper.selectByFieldName(AREA_NO);
        areaNo.removeAll(Collections.singleton(null));
        List<PriceAdjustmentRuleSection> priceAdjustmentRuleSections = priceAdjustmentRuleSectionMapper.selectAll();
        HashMap<String,Object> info = new HashMap<>();
        info.put("areaNo",areaNo);
        info.put("priceAdjustmentRuleSections",priceAdjustmentRuleSections);
        return AjaxResult.getOK(info);
    }

    @Override
    @Transactional
    public AjaxResult saveRule(PriceAdjustRuleVo priceAdjustRuleVo) {
        //清除所有数据
        priceAdjustmentRuleAreaMapper.deleteAll();
        priceAdjustmentRuleSectionMapper.deleteAll();
        Integer adminId = getAdminId();
        //上传浮动波动值
        priceAdjustRuleVo.getPriceAdjustmentRuleSections().forEach(el->{
            el.setCreater(adminId);
            el.setCreatTime(LocalDateTime.now());
            priceAdjustmentRuleSectionMapper.insertSelective(el);
        });
        // 插入城市
        List<Integer> areaNo = priceAdjustRuleVo.getAreaNo();
        if(!CollectionUtils.isEmpty(areaNo)){
            priceAdjustmentRuleAreaMapper.insertByFieldName(areaNo,AREA_NO,adminId);
        }
        return AjaxResult.getOK();
    }


    @Override
    @Transactional
    public AjaxResult initialization() {
        //将area_store数据初始化放在cycle_inventory_cost中
        cycleInventoryCostMapper.insertInitialization();
        //取store_record中最新的一条数据
        List<StoreRecord> storeRecordList = storeRecordMapper.selectLastedInfo();
        cycleInventoryCostMapper.updateByStoreRecord(storeRecordList);
        return AjaxResult.getOK();
    }

    /**
     * 定时任务
     * @return
     */
    @Override
    public AjaxResult ruleTimedTask() {

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nowTen = now.withHour(9).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime yesterdayTen = nowTen.minusDays(1L);
        //查看9点到9点之间的入库记录所有得库存仓 （不能在内存里用流排序，所有先取出城市再循环操作）
        List<Integer> warehouseNos =  storeRecordMapper.selectCycleCostArea(nowTen,yesterdayTen);
        List<List<StoreRecordVO>> costChangeList = new ArrayList<>();
        for (Integer warehouseNo : warehouseNos) {
            //查询时间内9-9点的入库记录
            List<StoreRecordVO> storeRecordVOS = storeRecordMapper.selectCycleCost(nowTen,yesterdayTen,warehouseNo);
            //处理日周期成本
            getContext().getBean(PriceAdjustService.class).handleCycleInventoryCost(storeRecordVOS);
            costChangeList.add(storeRecordVOS);
        }
        //自动调价
        // 目前已经全部走新成本规则了，等于下面的代码都废弃了----20240515
        if(dynamicConfig.getMallNewCostPriceSwitch()) {
            logger.info("当前走新成本规则，不在老的入口做调价。");
            return AjaxResult.getOK();
        }

/*        //取城市在规则中并且是待定时处理的的
        List<Integer> areaNos = priceAdjustmentRuleAreaMapper.selectByFieldName(AREA_NO);
        //根据时间取出所有区域 （不能在内存里用流排序，所有先取出城市再循环操作）
        List<Integer> priceAreaNos = priceAdjustmentPoolMapper.selectAreaNoByTime(nowTen,yesterdayTen);
        List<PriceAdjustment> poolVOList = new LinkedList<>();
        for (Integer priceAreaNo : priceAreaNos) {
            List<PriceAdjustmentPoolVO> priceAdjustmentPoolVOS = priceAdjustmentPoolMapper.selectByAreaNO(nowTen,yesterdayTen,priceAreaNo);
            try {
                //按照规则更新价格
                List<PriceAdjustment> info = getContext().getBean(PriceAdjustService.class).handlePriceAdjustmentPool(areaNos,priceAdjustmentPoolVOS);
                poolVOList.addAll(info);
            }catch (Exception e){
                logger.error("区域 {} 生成价格审核失败,错误信息：{}",priceAreaNo,e.getMessage());
            }
        }
        logger.info("需要自动调价的商品总数量：{}", poolVOList.size());
        if (!CollectionUtils.isEmpty(poolVOList)) {
            this.handleAutoAdjustPrice(poolVOList);
        }
        // 对未开启自动调价的sku,
        this.handleNotOpenAutoPriceMarketPrice(costChangeList);*/

        return AjaxResult.getOK();
    }

/*    *//**
     * 处理未开启自动调价的sku,去根据最新成本计算营销价格
     * @param costChangeList
     *//*
    private void handleNotOpenAutoPriceMarketPrice(List<List<StoreRecordVO>> costChangeList) {
        logger.info("处理未开启自动调价的sku的成本价调营销价格开始,costChangeList:{}",costChangeList.size());
        if (CollectionUtils.isEmpty(costChangeList)){
            logger.info("handleNotOpenAutoPriceMarketPrice() costChangeList 是空集合");
            return;
        }
        List<MarketPriceChangeDTO> marketPriceChangeDTOS = new ArrayList<>();
        for (List<StoreRecordVO> storeRecordVOS : costChangeList) {
            for (StoreRecordVO storeRecordVO : storeRecordVOS) {
                String sku = storeRecordVO.getSku();
                // 根据库存总仓和sku查询所有自动调价配置
                List<InterestRateConfig> interestRateConfigs = interestRateConfigMapper.selectConfig(sku, storeRecordVO.getAreaNo());
                // 如果为空 说明没开启自动调价 加入到集合
                if (CollectionUtils.isEmpty(interestRateConfigs)){
                    MarketPriceChangeDTO marketPriceChangeDTO = new MarketPriceChangeDTO(sku,null);
                    marketPriceChangeDTO.setUpdater("系统默认-未开启自动调价");
                    marketPriceChangeDTOS.add(marketPriceChangeDTO);
                    continue;
                }
                // 遍历自动调价配置 如果自动调价标识是关闭状态 封装sku和areaNo到对象里 加入集合
                for (InterestRateConfig interestRateConfig : interestRateConfigs) {
                    if(interestRateConfig != null && interestRateConfig.getAutoFlag() != null && interestRateConfig.getAutoFlag() == 0){
                        MarketPriceChangeDTO marketPriceChangeDTO = new MarketPriceChangeDTO(sku,interestRateConfig.getAreaNo());
                        marketPriceChangeDTO.setUpdater(interestRateConfig.getCreateAdminName() == null ? "interest_rate_config:" +
                                interestRateConfig.getId() : interestRateConfig.getCreateAdminName());
                        marketPriceChangeDTOS.add(marketPriceChangeDTO);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(marketPriceChangeDTOS)){
            logger.info("没有未开启自动调价的sku");
            return;
        }
        List<List<MarketPriceChangeDTO>> lists = new LinkedList<>();
        if( marketPriceChangeDTOS.size() < 4){
            //小于4个的话 直接插入
            lists.add(marketPriceChangeDTOS);
        }else{
            lists = SplitUtils.avgSplit(marketPriceChangeDTOS, 4);
        }
        for (List<MarketPriceChangeDTO> list : lists) {
            asyncServiceExecutor.execute(()->{
                for (MarketPriceChangeDTO marketPriceChangeDTO : list) {
                    try {
                        getContext().getBean(PriceAdjustService.class).changeMarketPrice(marketPriceChangeDTO.getSku(),marketPriceChangeDTO.getAreaNo()
                                , marketPriceChangeDTO.getUpdater());
                    }catch (Exception e){
                        logger.error("{} 通过成本价调整营销价格失败,错误信息为:{}",marketPriceChangeDTO, e);
                    }
                }
            });
        }
    }*/

    /**
     * 多线程自动调价
     * @param poolVOList
     */
    public void handleAutoAdjustPrice(List<PriceAdjustment> poolVOList) {
        List<PriceAdjustment> poolVOListSort = poolVOList.stream().sorted(Comparator.comparing(PriceAdjustment::getSku)).collect(Collectors.toList());
        List<List<PriceAdjustment>> lists = new LinkedList<>();
        if( poolVOListSort.size() < 4){
            //小于4个的话 直接插入
            lists.add(poolVOListSort);
        }else{
            lists = SplitUtils.avgSplit(poolVOListSort, 4);
        }
        for (List<PriceAdjustment> list : lists) {
            priceAdjustExecutor.execute(()->{
                for (PriceAdjustment priceAdjustment : list) {
                    try {
                        // 之前人实现的时候用priceAdjustment实体存了pool的id，这里分离出来，避免被数据库插入时的id覆盖掉
                        Integer priceAdjustmentPoolId = priceAdjustment.getId();
                        getContext().getBean(PriceAdjustService.class).auditAdjust(priceAdjustment);
                        priceAdjustmentPoolMapper.updateRuleFlag(1,null,priceAdjustmentPoolId);
                    }catch (Exception e){
                        logger.error("{} 价格调整失败,错误信息为:{}",priceAdjustment,e.getMessage());
                        logger.warn("sku:{}, areaNo:{} 调价失败, 堆栈信息:", priceAdjustment.getSku(), priceAdjustment.getAreaNo(), e);
                    }
                }
            });
        }
    }


    /**
     * 多线程将成本价变动时修改营销价格
     * @param poolVOList 调价池集合对象
     */
   /* public void handleCostPriceChange(List<PriceAdjustmentPoolVO> poolVOList) {
        logger.info("handleCostPriceChange() 成本价变动时修改营销价格任务开始{}",poolVOList.size());
        if (CollectionUtils.isEmpty(poolVOList)){
            return;
        }
        List<List<PriceAdjustmentPoolVO>> lists = new LinkedList<>();
        if( poolVOList.size() < 4){
            //小于4个的话 直接插入
            lists.add(poolVOList);
        }else{
            lists = SplitUtils.avgSplit(poolVOList, 4);
        }
        for (List<PriceAdjustmentPoolVO> list : lists) {
            asyncServiceExecutor.execute(()->{
                for (PriceAdjustmentPoolVO priceAdjustmentPoolVO : list) {
                    try {
                        String updater = priceAdjustmentPoolVO.getCreateAdminName() == null ? "price_adjustment_pool:" + priceAdjustmentPoolVO.getId()
                                : priceAdjustmentPoolVO.getCreateAdminName();
                        getContext().getBean(PriceAdjustService.class).changeMarketPrice(priceAdjustmentPoolVO.getSku(),priceAdjustmentPoolVO.getAreaNo(), updater);
                    }catch (Exception e){
                        logger.error("{} 通过成本价调整营销价格失败,错误信息为:{}",priceAdjustmentPoolVO, e);
                    }
                }
            });
        }
    }*/

    /**
     * 处理价格调整
     */
    @Transactional
    @Override
    public List<PriceAdjustment> handlePriceAdjustmentPool(List<Integer> areaNos,List<PriceAdjustmentPoolVO> priceAdjustmentPoolVOS) {
        List<PriceAdjustment> info = new LinkedList<>();
        // sku的基本信息
        final List<String> skus = priceAdjustmentPoolVOS.stream().map(PriceAdjustmentPoolVO::getSku).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skus)) {
            logger.info("无需要处理的sku数据。priceAdjustmentPoolVOS：{}", JSON.toJSONString(priceAdjustmentPoolVOS));
            return info;
        }
        final List<InventoryVO> inventoryVOS = inventoryMapper.selectInventoryVOBySkus(skus);
        Map<String, InventoryVO> inventoryVOMap = inventoryVOS.stream().collect(Collectors.toMap(Inventory::getSku, Function.identity()));
        logger.info("当前待处理的sk信息：{}", JSON.toJSONString(inventoryVOMap));
        for (PriceAdjustmentPoolVO priceAdjustmentPoolVO : priceAdjustmentPoolVOS) {
            //取所在的仓
            Integer warehouseNo = storeRecordMapper.selectWarehouseNo(priceAdjustmentPoolVO.getAreaNo(), priceAdjustmentPoolVO.getSku());
            if (Objects.isNull(warehouseNo)) {
                logger.warn("获取库存仓失败,sku:{},areaNo:{}", priceAdjustmentPoolVO.getSku(), priceAdjustmentPoolVO.getAreaNo());
                continue;
            }
            //取所在仓的周期报价
            CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(priceAdjustmentPoolVO.getSku(), warehouseNo);
            //取周报价所在的波动值
            PriceAdjustmentRuleSection parsCycleCost = priceAdjustmentRuleSectionMapper.selectByCycleCost(cycleInventoryCost.getEndCycleCost());
            //取原售价所在的波动值
            PriceAdjustmentRuleSection parsOriginalPrice = priceAdjustmentRuleSectionMapper.selectByCycleCost(priceAdjustmentPoolVO.getOriginalMarketPrice());
            //取原价价格
            AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(priceAdjustmentPoolVO.getAreaNo(), priceAdjustmentPoolVO.getSku());
            PriceInfoBO priceInfo = priceService.getNormalPrice(areaSku);
            BigDecimal originalPrice = priceInfo.getPrice();
            //如果有待审核的将其变为超时未处理
            priceAdjustmentPoolMapper.updateStatus(priceAdjustmentPoolVO.getAreaNo(),priceAdjustmentPoolVO.getSku(),PriceAdjustPoolStatus.WAIT_AUDIT.getStatus(),PriceAdjustPoolStatus.OUT_OF_TIME_ADJUST.getStatus(),2);
            //根据周成本价调整售价和成本价
            InterestRateConfig interestRateConfig = interestRateConfigMapper.selectByAreaNoAndSku(priceAdjustmentPoolVO.getAreaNo(), priceAdjustmentPoolVO.getSku());
            // 算最新售价
            BigDecimal newPrice = cycleInventoryCost.getFirstCycleCost().divide(BigDecimal.ONE.subtract(interestRateConfig.getInterestRate()), 0, BigDecimal.ROUND_UP);
            
            logger.info("原售价:{},新售价:{},成本价:{},priceAdjustmentPoolVO:{}",originalPrice,newPrice,cycleInventoryCost.getFirstCycleCost(),JSON.toJSONString(priceAdjustmentPoolVO));
            //当原价和最新售价相等时 跳过
            if(newPrice!=null && originalPrice!=null && newPrice.compareTo(originalPrice)==0){
                logger.warn("原价和最新售价相等,跳过处理,priceAdjustmentPoolVO:{}", priceAdjustmentPoolVO);
                continue;
            }else{
                //调整最新售价和成本价
                priceAdjustmentPoolMapper.updatePriceAndCost(priceAdjustmentPoolVO.getId(),originalPrice,newPrice,cycleInventoryCost.getFirstCycleCost());
            }

            // 1.基本成本判断
            boolean costFlag = cycleInventoryCost.getEndCycleCost() !=null&&
                    cycleInventoryCost.getEndCycleCost().compareTo(BigDecimal.ZERO)!=0&&
                    originalPrice!=null &&
                    originalPrice.compareTo(BigDecimal.ZERO)!=0;
            // 2. 区域规则
            boolean areaNoFlag = !CollectionUtils.isEmpty(areaNos)&&areaNos.contains(priceAdjustmentPoolVO.getAreaNo());
            // 3. 阈值波动判断
            boolean costFluctuationFlag = cycleInventoryCost.getEndCycleCost() != null && cycleInventoryCost.getFirstCycleCost().subtract(cycleInventoryCost.getEndCycleCost()).abs().divide(cycleInventoryCost.getEndCycleCost(),4,BigDecimal.ROUND_UP).compareTo(parsCycleCost.getFluctuationValue()) == -1;
            boolean priceFluctuationFlag = originalPrice != null &&
                    newPrice.subtract(originalPrice).abs().divide(originalPrice,4,BigDecimal.ROUND_HALF_DOWN).compareTo(parsOriginalPrice.getFluctuationValue()) == -1;//NOSONAR
            boolean fluctuationFlag = costFluctuationFlag && priceFluctuationFlag;
            // 3.判断是否是pop在鲜沐上架的”代销不入仓“的商品
            boolean isPopSkuUsedByXm = this.isPopSkuUsedByXm(priceAdjustmentPoolVO, inventoryVOMap);

            // 打印是否走人工审规则日志
            // 判断规则： |（C1-C2）/C2 | < x %   |（P1-P2）/P2 | < x %  (C1日周期库存成本、C2上个日周期库存成本\P1新售价、P2当前售价\x波动值)
            this.printAuditRuleLog(priceAdjustmentPoolVO, cycleInventoryCost, parsCycleCost, parsOriginalPrice, originalPrice, newPrice, areaNos, isPopSkuUsedByXm, costFlag, fluctuationFlag, areaNoFlag);

            if(areaNoFlag && ((costFlag && fluctuationFlag) || isPopSkuUsedByXm)){
                // 售价波动在范围内时 成本价和售价都发生变化
                //修改priceAdjustment
                Long nextBusinessId = SnowflakeUtil.nextId();
                priceAdjustmentPoolMapper.updateBusinessIdById(priceAdjustmentPoolVO.getId(),nextBusinessId);
                // 直接生效
                AjaxResult ajaxResult = this.selectSimple(priceAdjustmentPoolVO.getSku(), PriceAdjustStatus.WAIT_AUDIT.ordinal(), null, priceAdjustmentPoolVO.getId());
                PriceAdjustVO data = (PriceAdjustVO)ajaxResult.getData();
                PriceAdjustment priceAdjustment = new PriceAdjustment();
                priceAdjustment.setStatus((byte)3);
                priceAdjustment.setAreaPriceAdjustment(data.getAreaPriceAdjustment());
                Area area = areaMapper.selectByAreaNo(priceAdjustmentPoolVO.getAreaNo());
                priceAdjustment.setAreaNo(area.getLargeAreaNo());
                priceAdjustment.setSku(data.getSku());
                priceAdjustment.setId(priceAdjustmentPoolVO.getId());
                //将消息发到消息队列
                logger.info("直接生效价格调整：{}",priceAdjustment);
                info.add(priceAdjustment);
            }else{
                // 成本价发生变化，售价未改变
                //将其他的变为需要审核状态
                logger.info("走人工审核。sku:{}, areaNo:{}", priceAdjustmentPoolVO.getSku(), priceAdjustmentPoolVO.getAreaNo());
                priceAdjustmentPoolMapper.updateRuleFlag(2,PriceAdjustPoolStatus.WAIT_AUDIT.getStatus(),priceAdjustmentPoolVO.getId());
            }
        }
        return info;
    }

    /**
     * 判断是否是pop在鲜沐上架的”代销不入仓“的商品
     * @return
     */
    private boolean isPopSkuUsedByXm(PriceAdjustmentPoolVO priceAdjustmentPoolVO, Map<String, InventoryVO> inventoryVOMap) {
        String sku = priceAdjustmentPoolVO.getSku();
        InventoryVO inventoryVO = inventoryVOMap.get(sku);
        if(inventoryVO == null) {
            logger.error("sku不存在!priceAdjustmentPoolVO:{}, inventoryVOMap:{}", JSON.toJSONString(priceAdjustmentPoolVO), JSON.toJSONString(inventoryVOMap));
            return false;
        }

        return CONSIGNMENT_NO_WAREHOUSE.subType == inventoryVO.getSubType() && ProductCategoryTypeEnum.FRUIT.getCode().equals(inventoryVO.getCategoryType());
    }

    private void printAuditRuleLog(PriceAdjustmentPoolVO priceAdjustmentPoolVO, CycleInventoryCost cycleInventoryCost, PriceAdjustmentRuleSection parsCycleCost, PriceAdjustmentRuleSection parsOriginalPrice, BigDecimal originalPrice, BigDecimal newPrice, List<Integer> areaNos, boolean isPopSkuUsedByXm, boolean costFlag, boolean fluctuationFlag, boolean areaNoFlag) {
        try {
            String areaNoStr = priceAdjustmentPoolVO.getAreaNo() == null ? "" : priceAdjustmentPoolVO.getAreaNo().toString();
            String endCycleCost = cycleInventoryCost.getEndCycleCost() == null ? "" : cycleInventoryCost.getEndCycleCost().toString();
            String firstCycleCost = cycleInventoryCost.getFirstCycleCost() == null ? "" : cycleInventoryCost.getFirstCycleCost().toString();
            String costFluctuationValue = parsCycleCost == null || parsCycleCost.getFluctuationValue() == null ? "" : parsCycleCost.getFluctuationValue().toString();
            String priceFluctuationValue = parsOriginalPrice.getFluctuationValue() == null ? "" : parsOriginalPrice.getFluctuationValue().toString();
            String originalPriceStr = originalPrice == null ? "" : originalPrice.toString();
            String newPriceStr = newPrice == null ? "" : newPrice.toString();
            logger.info("是否走人工审规则判断,sku:{}, areaNo:{}, 区域是否支持自动审：{}, 是否pop代销不入仓:{}, 成本信息不为空：{}, 是否在阈值内：{}。规则参数:[上周期成本:{}, 当前成本:{}, 成本波动阈值:{}, 老售价:{}, 新售价:{}, 售价波动阈值:{}, 经营城市列表:{}]", priceAdjustmentPoolVO.getSku(), areaNoStr,areaNoFlag, isPopSkuUsedByXm, costFlag, fluctuationFlag, endCycleCost, firstCycleCost, costFluctuationValue, originalPriceStr, newPriceStr, priceFluctuationValue, JSON.toJSONString(areaNos));
        } catch (Exception e) {
            logger.warn("日志打印失败.调价单信息: {}", JSON.toJSONString(priceAdjustmentPoolVO), e);
        }
    }

    /**
     * 处理周期成本
     * @param storeRecordVOS
     */
    @Override
    @Transactional
    public void handleCycleInventoryCost(List<StoreRecordVO> storeRecordVOS) {
        //查看周期成本表中是否有记录 没有的话插入
        for (StoreRecordVO storeRecordVO : storeRecordVOS) {
            // 销采商品不触发自动调价
            Inventory inventory = inventoryMapper.selectOneBySku(storeRecordVO.getSku());
            if (inventory != null) {
                InventorySubTypeEnum subType = InventorySubTypeEnum.findByType(inventory.getSubType());
                if (subType != null && subType.equals(CONSIGNMENT_NO_WAREHOUSE)) {
                    continue;
                }
            }
            CycleInventoryCost cic = cycleInventoryCostRepository.selectBySku(storeRecordVO.getSku(), storeRecordVO.getAreaNo());
            //如果为空的话插入
            if(cic == null){
                CycleInventoryCost cycleInventoryCost = new CycleInventoryCost();
                cycleInventoryCost.setWarehouseNo(storeRecordVO.getAreaNo());
                cycleInventoryCost.setSku(storeRecordVO.getSku());
                cycleInventoryCost.setCreateTime(LocalDateTime.now());
                cycleInventoryCost.setFirstCycleCost(storeRecordVO.getCircleCost());
                cycleInventoryCost.setFirstCycleCostTime(LocalDateTime.now());
                cycleInventoryCost.setCreater(0);
                cycleInventoryCostMapper.insertSelective(cycleInventoryCost);
            }else{  //根据昨日的入库信息更新周期库存成本报价
                //幂等校验，今天处理过的 不处理。
                if (!Objects.isNull(cic.getFirstCycleCostTime()) && cic.getFirstCycleCostTime().toLocalDate().isEqual(LocalDate.now())) {
                    continue;
                }
                cycleInventoryCostMapper.update(storeRecordVO);
            }
        }
    }

    @Override
    public void handleOvertime() {
        logger.info("自动调价审批超时开始处理");
        List<Long> businessIdList = priceAdjustmentPoolMapper.selectOutOfTime(LocalDateTime.of(LocalDate.now(), Global.CLOSING_ORDER_TIME));
        for (Long businessId : businessIdList) {
            List<PriceAdjustmentPoolVO> poolList = priceAdjustmentPoolMapper.selectByBusinessId(businessId);
            if (!CollectionUtils.isEmpty(poolList)) {
                PriceAdjustmentPoolVO vo = poolList.get(0);

                PriceAdjustment adjustment = new PriceAdjustment();
                adjustment.setSku(vo.getSku());
                adjustment.setPdName(vo.getPdName());
                PriceAdjustmentPool tempPool = priceAdjustmentPoolMapper.selectByPrimaryKey(vo.getId());
                adjustment.setOriginalMarketPrice(tempPool.getOriginalPrice());
                adjustment.setMarketPrice(tempPool.getPrice());
                adjustment.setOriginalCostPrice(vo.getOriginalCostPrice());
                adjustment.setCostPrice(vo.getCostPrice());
                adjustment.setReason(vo.getReason());
                adjustment.setStatus((byte) PriceAdjustStatus.OUT_OF_AUDIT_TIME.ordinal());
                adjustment.setAddTime(vo.getCreateTime());
                adjustment.setAreaNo(vo.getStoreNo());
                adjustment.setCreateAdminName(vo.getCreateAdminName());
                adjustment.setUpTime(vo.getUpTime());
                List<AreaSku> skuList = poolList.stream()
                        .map(el -> {
                            AreaSku ak = new AreaSku();
                            ak.setSku(el.getSku());
                            ak.setAreaNo(el.getAreaNo());
                            ak.setAreaName(el.getAreaName());
                            ak.setPrice(el.getMarketPrice());
                            ak.setOriginalPrice(el.getOriginalMarketPrice());
                            ak.setLadderPrice(el.getLadderPrice());
                            return ak;
                        }).collect(Collectors.toList());
                adjustment.setAreaPriceAdjustment(JSON.toJSONString(skuList));

                priceAdjustmentPoolMapper.updateStatusByBusinessId(PriceAdjustPoolStatus.OUT_OF_TIME.getStatus(), businessId);
                priceAdjustmentMapper.insert(adjustment);
            }
        }
        logger.info("自动调价审批超时处理结束");
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult saveAdjustSheet(List<ProductPriceAdjustment> productPriceAdjustmentList){
        logger.info("开始调价单调价，操作人：{}，productPriceAdjustmentList：{}", this.getAdminName(), JSON.toJSONString(productPriceAdjustmentList));
        // 数据校验
        this.adjustSheetCheck(productPriceAdjustmentList);
        // 封装相同sku数据
        Map<String, List<ProductPriceAdjustment>> sameSkuMap = this.getSameSkuMap(productPriceAdjustmentList);
        // 批量新增调价单记录集合
        List<PriceAdjustRecord> adjustRecords = new ArrayList<>();
        // 自动调价的入参集合
        List<AreaSku> areaSkuList = new ArrayList<>();
        for (Map.Entry<String, List<ProductPriceAdjustment>> entry : sameSkuMap.entrySet()) {
            for (ProductPriceAdjustment priceAdjustment : entry.getValue()) {
                ProductPriceAdjustmentDO productPriceAdjustmentDO = new ProductPriceAdjustmentDO();
                productPriceAdjustmentDO.setSku(priceAdjustment.getSku());
                productPriceAdjustmentDO.setPdName(priceAdjustment.getPdName());
                productPriceAdjustmentDO.setWeight(priceAdjustment.getWeight());
                productPriceAdjustmentDO.setCreateAdminName(getAdminName());
                String ladderPrice = priceAdjustment.getLadderPrice();
                boolean deleteLadderPrice = priceAdjustment.isDeleteLadderPrice();

                //TODO 需要约定下阶梯价不填，前端传过来是什么结构，要注意其他地方调用改接口阶梯价是否能删除掉

                // 保存调价单数据
                productPriceAdjustmentMapper.insert(productPriceAdjustmentDO);
                for (Integer areaNo : priceAdjustment.getAreaNoList()) {
                    ProductPriceAdjustmentDetailDO productPriceAdjustmentDetailDO = new ProductPriceAdjustmentDetailDO();
                    productPriceAdjustmentDetailDO.setAdjustmentId(productPriceAdjustmentDO.getId());
                    productPriceAdjustmentDetailDO.setSku(priceAdjustment.getSku());
                    productPriceAdjustmentDetailDO.setPrice(priceAdjustment.getPrice());
                    productPriceAdjustmentDetailDO.setLadderPrice(ladderPrice);
                    productPriceAdjustmentDetailDO.setAreaNo(areaNo);
                    productPriceAdjustmentDetailDO.setInterestRate(priceAdjustment.getInterestRate());
                    productPriceAdjustmentDetailDO.setAutoFlag(priceAdjustment.getAutoFlag());
                    // 保存调价单详情数据
                    productPriceAdjustmentDetailMapper.insert(productPriceAdjustmentDetailDO);

                    // 查询当前sku在当前城市的售卖信息
                    AreaSku areaSkuDb = areaSkuMapper.selectByAreaNoAndSku(areaNo, priceAdjustment.getSku());
                    // 封装自动调价接口入参
                    AreaSku areaSku = new AreaSku();
                    if (Objects.isNull(priceAdjustment.getPrice())){
                        areaSku.setPrice(areaSkuDb.getPrice());
                    }else {
                        areaSku.setPrice(priceAdjustment.getPrice());
                    }

                    // 根据标识转换阶梯价
/*                    String ladderPriceNew = getNewLadderPrice(areaSkuDb, deleteLadderPrice, ladderPrice);


                    if (StringUtils.isNotBlank(ladderPriceNew) && !"[]".equals(ladderPriceNew)){
                        BigDecimal costPrice = cycleInventoryCostService.selectCostByAreaNo(areaSkuDb.getSku(), areaNo);
                        List<LadderPriceVO> ladderPriceVOS = JSON.parseArray(ladderPriceNew, LadderPriceVO.class);
                        for (LadderPriceVO ladderPriceVO : ladderPriceVOS) {
                            if (ladderPriceVO.getAdjustType() == null || ladderPriceVO.getAmount() == null){
                                continue;
                            }

                            PriceStrategy strategy = new PriceStrategy();
                            strategy.setAdjustType(ladderPriceVO.getAdjustType());
                            strategy.setAmount(ladderPriceVO.getAmount());
                            strategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                            PriceStrategyAuditRecordVO vo = priceStrategyService.calcStrategyPrice(strategy, costPrice, areaSku.getPrice());
                            BigDecimal resultPrice = vo.getNewPrice();
                            ladderPriceVO.setPrice(resultPrice);
                        }
                        areaSku.setLadderPrice(JSON.toJSONString(ladderPriceVOS));
                    } else {
                        areaSku.setLadderPrice(ladderPriceNew);
                    }*/
                    areaSku.setInterestRateNew(priceAdjustment.getInterestRate());
                    areaSku.setAreaNo(areaNo);
                    areaSku.setSku(priceAdjustment.getSku());
                    areaSku.setAutoFlagNew(priceAdjustment.getAutoFlag());
                    areaSku.setUpdater("product_price_adjustment:" + productPriceAdjustmentDO.getId());
                    areaSkuList.add(areaSku);

                    // 封装调价记录
                    PriceAdjustRecord priceAdjustRecord = new PriceAdjustRecord();
                    priceAdjustRecord.setSku(priceAdjustment.getSku());
                    priceAdjustRecord.setPdName(priceAdjustment.getPdName());
                    priceAdjustRecord.setAreaNo(areaNo);
                    priceAdjustRecord.setPrice(priceAdjustment.getPrice());
                    priceAdjustRecord.setOriginalPrice(areaSkuDb.getPrice());
                    priceAdjustRecord.setLadderPrice(areaSku.getLadderPrice());
                    priceAdjustRecord.setOriginalLadderPrice(areaSkuDb.getLadderPrice());
                    priceAdjustRecord.setAutoFlagNew(priceAdjustment.getAutoFlag());
                    priceAdjustRecord.setInterestRateNew(priceAdjustment.getInterestRate());
                    priceAdjustRecord.setCreateAdminName(getAdminName());
                    InterestRateConfig rateConfig = null;
                    if (Objects.nonNull(priceAdjustment.getInterestRate())){
                        rateConfig = interestRateConfigMapper.selectByAreaNoAndSku(areaNo, priceAdjustment.getSku());
                    }
                    if (Objects.nonNull(rateConfig)){
                        priceAdjustRecord.setInterestRateOld(rateConfig.getInterestRate());
                        priceAdjustRecord.setAutoFlagOld(rateConfig.getAutoFlag());
                    }
                    adjustRecords.add(priceAdjustRecord);
                }
            }
        }
        // 走自动调价流程
        this.priceAdjust(areaSkuList,1);

        // 批量新增调价单的调价记录
        priceAdjustRecordMapper.insertBatch(adjustRecords);

        // 插入价格变动表price_adjustment的数据
        this.savePriceAdjustment(adjustRecords);
        return AjaxResult.getOK();
    }

    /**
     * 通过页面过来的请求允许覆盖掉阶梯价
     * @param areaSkuDb
     * @param deleteLadderPrice
     * @param ladderPrice
     * @return
     */
    private String getNewLadderPrice(AreaSku areaSkuDb, boolean deleteLadderPrice, String ladderPrice) {
        if (deleteLadderPrice) {
            return "[]";
        }
        // 如果没有覆盖标识，那么为空时取数据库的值
        return StringUtils.isNotBlank(ladderPrice) && !"[]".equals(ladderPrice)
                ? ladderPrice : areaSkuDb.getLadderPrice();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeMarketPrice(String sku, Integer areaNo, String updater) {
        if (areaNo == null){
            List<AreaSku> areaSkuList = areaSkuMapper.selectAllBySku(sku);
            for (AreaSku areaSkuData : areaSkuList) {
                areaSkuData.setUpdater(updater);
                this.changeMarketPrices(areaSkuData);
            }
        }else {
            AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
            if (areaSku == null){
                logger.info("该sku:{},在该城市:{}不存在",sku,areaNo);
                return;
            }
            areaSku.setUpdater(updater);
            this.changeMarketPrices(areaSku);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void bindSkuAutoUpdatePrice(long pdId, String sku, int areaNo, BigDecimal price) {
        // 查询绑定当前常规品的sku数据
        List<InventoryBind> inventoryBinds = inventoryBindMapper.selectByBindSku(pdId, sku);
        if(CollectionUtils.isEmpty(inventoryBinds)){
            logger.error("inventoryBinds null ,常规品sku:{},pdId:{}", sku,pdId);
            return;
        }
        List<ProductPriceAdjustment> productPriceAdjustmentList = new ArrayList<>();
        for (InventoryBind inventoryBind : inventoryBinds) {
            String priceAdjustSku = inventoryBind.getSku();
            InventoryVO inventoryVO = inventoryMapper.selectInventoryBySku(priceAdjustSku);
            if(inventoryVO == null){
                logger.error("与常规品绑定的商品不存在或该商品处于删除状态,常规品sku:{}", sku);
                continue;
            }
            BigDecimal newPrice;
            if(Objects.equals(InventoryExtTypeEnum.TEMPORARY_INSURANCE.type(), inventoryVO.getExtType())){
                // 临保品员联动改价为与绑定sku的原价相同
                newPrice = price;
            }else if(Objects.equals(InventoryExtTypeEnum.BROKEN_BAG.type(), inventoryVO.getExtType())){
                // 破袋联动改价为绑定sku的原价*0.95向上去整
                newPrice = price.multiply(BigDecimal.valueOf(0.95)).setScale(1,RoundingMode.UP);
            }else{
                logger.info("当前联动改价sku非临保/破袋类型,sku:{},extType{}", priceAdjustSku, inventoryVO.getExtType());
                return;
            }

            AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, priceAdjustSku);
            if(areaSku == null){
                logger.info("关联sku在运营区域不存在,bindSku:{}, sku:{}, areaNo:{}", sku, priceAdjustSku, areaNo);
                continue;
            }
            ProductPriceAdjustment productPriceAdjustment = new ProductPriceAdjustment();
            productPriceAdjustment.setPdName(inventoryVO.getPdName());
            productPriceAdjustment.setSku(priceAdjustSku);
            productPriceAdjustment.setWeight(inventoryVO.getWeight());
            productPriceAdjustment.setPrice(newPrice);
            productPriceAdjustment.setAreaNoList(Lists.newArrayList(areaNo));

            InterestRateConfig interestRateConfig = interestRateConfigMapper.selectByAreaNoAndSku(areaNo, priceAdjustSku);
            if(interestRateConfig != null){
                productPriceAdjustment.setAutoFlag(interestRateConfig.getAutoFlag());
                BigDecimal interestRate = interestRateConfig.getInterestRate();
                if(interestRate != null){
                    productPriceAdjustment.setInterestRate(interestRate.setScale(2, RoundingMode.HALF_UP));
                }
            }
            productPriceAdjustmentList.add(productPriceAdjustment);
        }
        if(CollectionUtils.isEmpty(productPriceAdjustmentList)){
            logger.info("常规品不存在需要调价的商品数据,sku:{}, areaNo:{}", sku, areaNo);
            return;
        }
        saveAdjustSheet(productPriceAdjustmentList);
    }

    @Override
    public void autoUpdatePrice(String sku, int areaNo, BigDecimal price, BigDecimal interestRate) {
        InventoryVO inventoryVO = inventoryMapper.selectInventoryBySku(sku);

        ProductPriceAdjustment productPriceAdjustment = new ProductPriceAdjustment();
        productPriceAdjustment.setPdName(inventoryVO.getPdName());
        productPriceAdjustment.setSku(sku);
        productPriceAdjustment.setWeight(inventoryVO.getWeight());
        productPriceAdjustment.setPrice(price);
        productPriceAdjustment.setAreaNoList(Lists.newArrayList(areaNo));
        productPriceAdjustment.setInterestRate (interestRate.setScale(4, RoundingMode.HALF_UP));
        productPriceAdjustment.setAutoFlag (InterestRateConfigAutoFlagEnum.OPEN.getAutoFlag ());
        List<ProductPriceAdjustment> productPriceAdjustmentList = new ArrayList<>();
        productPriceAdjustmentList.add(productPriceAdjustment);
        saveAdjustSheet(productPriceAdjustmentList);//NOSONAR
    }

    private void changeMarketPrices(AreaSku areaSku) {
/*        BigDecimal costPrice = cycleInventoryCostService.selectCostByAreaNo(areaSku.getSku(), areaSku.getAreaNo());
        //判断活动状态 todo 注意有无活动可能不需要改阶梯价
//        ActivitySku activitySku = activitySkuMapper.selectOne(areaSku.getAreaNo(), areaSku.getSku());

        CommonResult<ActivitySku> result = activityNewService.getActivitySku(
                areaSku.getSku(), areaSku.getAreaNo());
        ActivitySku activitySku = result.getData();
        if (activitySku == null) {
            // 没参加活动直接修改area_sku表阶梯价
            List<LadderPriceVO> ladderPriceVOS = JSON.parseArray(areaSku.getLadderPrice(), LadderPriceVO.class);
            if (CollectionUtils.isEmpty(ladderPriceVOS)){
                return;
            }
            for (LadderPriceVO ladderPriceVO : ladderPriceVOS) {
                if (ladderPriceVO.getAdjustType() == null || ladderPriceVO.getAmount() == null){
                    continue;
                }

                // 根据售卖价得出最后的阶梯价
                PriceStrategy strategy = new PriceStrategy();
                strategy.setAdjustType(ladderPriceVO.getAdjustType());
                strategy.setAmount(ladderPriceVO.getAmount());
                strategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                PriceStrategyAuditRecordVO vo = priceStrategyService.calcStrategyPrice(strategy, costPrice, areaSku.getPrice());
                BigDecimal resultPrice = vo.getNewPrice();
                ladderPriceVO.setPrice(resultPrice);
            }
            areaSku.setLadderPrice(JSON.toJSONString(ladderPriceVOS));
            areaSkuMapper.updateByAreaSku(areaSku);
        } else {
            // 有活动存在，更新activity_sku表阶梯价
            ActivitySku record = new ActivitySku();
            record.setId(activitySku.getId());
            if (StringUtils.isNotBlank(activitySku.getLadderPrice()) && !"[]".equals(activitySku.getLadderPrice())){
                List<LadderPriceVO> ladderPriceVOS = JSON.parseArray(activitySku.getLadderPrice(), LadderPriceVO.class);

                for (LadderPriceVO ladderPriceVO : ladderPriceVOS) {
                    if (ladderPriceVO.getAdjustType() == null || ladderPriceVO.getAmount() == null){
                        continue;
                    }
                    // 根据售卖价得出最后的阶梯价
                    PriceStrategy strategy = new PriceStrategy();
                    strategy.setAdjustType(ladderPriceVO.getAdjustType());
                    strategy.setAmount(ladderPriceVO.getAmount());
                    strategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                    PriceStrategyAuditRecordVO vo = priceStrategyService.calcStrategyPrice(strategy, costPrice, areaSku.getPrice());
                    BigDecimal resultPrice = vo.getNewPrice();
                    ladderPriceVO.setPrice(resultPrice);
                }
                record.setLadderPrice(JSON.toJSONString(ladderPriceVOS));
            }
        }*/
    }


    @NotNull
    private Map<String, List<ProductPriceAdjustment>> getSameSkuMap(List<ProductPriceAdjustment> productPriceAdjustmentList) {
        Map<String,List<ProductPriceAdjustment>> sameSkuMap = new HashMap<>();
        for (ProductPriceAdjustment priceAdjustment : productPriceAdjustmentList) {
            List<ProductPriceAdjustment> productPriceAdjustments = new ArrayList<>();
            if (sameSkuMap.get(priceAdjustment.getSku()) == null){
                productPriceAdjustments.add(priceAdjustment);
            }else {
                List<ProductPriceAdjustment> priceAdjustments = sameSkuMap.get(priceAdjustment.getSku());
                priceAdjustments.add(priceAdjustment);
                productPriceAdjustments.addAll(priceAdjustments);
            }
            sameSkuMap.put(priceAdjustment.getSku(),productPriceAdjustments);
        }
        return sameSkuMap;
    }

    /**
     * 封装价格变动表price_adjustment的数据
     * @param adjustRecords 调价单调价记录数据
     */
    private void savePriceAdjustment(List<PriceAdjustRecord> adjustRecords) {
        if (CollectionUtils.isEmpty(adjustRecords)){
            return;
        }
        List<Integer> areaNos = adjustRecords.stream().map(PriceAdjustRecord::getAreaNo).distinct().collect(Collectors.toList());
        List<Area> areaList = areaMapper.selectAreaNos(areaNos);
        Map<Integer,Area> areaMap = areaList.stream().collect(Collectors.toMap(Area::getAreaNo,Function.identity()));
        Map<String,List<PriceAdjustRecord>> skuGroupRecord = adjustRecords.stream().collect(Collectors.groupingBy(PriceAdjustRecord::getSku));
        for (Map.Entry<String, List<PriceAdjustRecord>> entry : skuGroupRecord.entrySet()){
            Map<Integer,List<PriceAdjustRecord>> adjustRecordMap = new HashMap<>();
            for (PriceAdjustRecord adjustRecord : entry.getValue()) {
                List<PriceAdjustRecord> tempAdjustRecordList= new ArrayList<>();
                Area area = areaMap.get(adjustRecord.getAreaNo());
                adjustRecord.setAreaName(Optional.ofNullable(area).map(Area::getAreaName).orElse(null));
                if (adjustRecordMap.get(area.getLargeAreaNo()) == null){
                    tempAdjustRecordList.add(adjustRecord);
                    adjustRecordMap.put(area.getLargeAreaNo(),tempAdjustRecordList);
                }else {
                    List<PriceAdjustRecord> priceAdjustRecords = adjustRecordMap.get(area.getLargeAreaNo());
                    priceAdjustRecords.add(adjustRecord);
                    adjustRecordMap.put(area.getLargeAreaNo(),priceAdjustRecords);
                }
            }
            for (Map.Entry<Integer, List<PriceAdjustRecord>> entryArea : adjustRecordMap.entrySet()) {
                PriceAdjustRecord priceAdjustRecord = entryArea.getValue().get(0);
                PriceAdjustment priceAdjustment = new PriceAdjustment();
                priceAdjustment.setAreaNo(entryArea.getKey());
                priceAdjustment.setSku(priceAdjustRecord.getSku());
                priceAdjustment.setPdName(priceAdjustRecord.getPdName());
                priceAdjustment.setAreaPriceAdjustment(JSON.toJSONString(entryArea.getValue()));
                priceAdjustment.setStatus(Byte.valueOf("4"));
                priceAdjustment.setUpTime(LocalDateTime.now());
                priceAdjustment.setCreateAdminName(priceAdjustRecord.getCreateAdminName());
                // 插入价格变动表数据
                logger.info("savePriceAdjustment :{}", JSON.toJSONString(priceAdjustment));
                priceAdjustmentMapper.insert(priceAdjustment);
            }
        }
    }

    /**
     * 调价单校验
     * @param productPriceAdjustmentList 调价集合对象
     */
    private void adjustSheetCheck(List<ProductPriceAdjustment> productPriceAdjustmentList) {
        logger.info("price adjust check start. productPriceAdjustmentList :{}", JSON.toJSONString(productPriceAdjustmentList));
        Map<String,List<Integer>> sameSkuRepeatAreaCheckMap = new HashMap<>();
        for (ProductPriceAdjustment productPriceAdjustment : productPriceAdjustmentList) {
            // 校验同个sku是否配置了相同城市
            List<Integer> areaNos = sameSkuRepeatAreaCheckMap.get(productPriceAdjustment.getSku());
            if (CollectionUtils.isEmpty(areaNos)){
                sameSkuRepeatAreaCheckMap.put(productPriceAdjustment.getSku(),productPriceAdjustment.getAreaNoList());
            }else {
                if (!Collections.disjoint(areaNos,productPriceAdjustment.getAreaNoList())){
                    throw new DefaultServiceException("同个sku不允许出现多条有重叠城市的调价记录"+productPriceAdjustment.getSku());
                }
            }
            if (productPriceAdjustment.getPrice() != null){
                // 校验售价
                String price = productPriceAdjustment.getPrice().toString();
                String integer;
                String decimal = null;
                if (price.contains(".")){
                    integer = price.substring(0, price.indexOf("."));
                    decimal = price.substring(price.indexOf(".") + 1);
                }else {
                    integer = price;
                }

                if (productPriceAdjustment.getPrice().compareTo(BigDecimal.ZERO) < 0 || integer.length() > 8 || (decimal != null && decimal.length()>2)){
                    throw new DefaultServiceException("售价有误");
                }
            }
            if (productPriceAdjustment.getInterestRate() != null){
                // 校验毛利率
                String ratePrice = productPriceAdjustment.getInterestRate().toString();
                String decimalRate = null;
                if (ratePrice.contains(".")){
                    decimalRate = ratePrice.substring(ratePrice.indexOf(".") + 1);
                }

                if (BigDecimal.ZERO.compareTo(productPriceAdjustment.getInterestRate()) > -1
                        || new BigDecimal(1).compareTo(productPriceAdjustment.getInterestRate()) < 1
                        || (decimalRate != null && decimalRate.length() > 4)){
                    throw new DefaultServiceException("毛利率有误");
                }
            }

            if (Objects.isNull(productPriceAdjustment.getPrice()) && Objects.isNull(productPriceAdjustment.getInterestRate())){
                throw new DefaultServiceException("售价和毛利率至少有一项不为空");
            }
            // 校验适用范围
            if (CollectionUtils.isEmpty(productPriceAdjustment.getAreaNoList())){
                throw new DefaultServiceException("适用范围不能为空");
            }

        }
    }

    private void selectInFlow(String sku, Integer areaNo) {
        int inFlow = priceAdjustmentMapper.selectInFlow(sku, areaNo);
        if (inFlow != 0) {
            throw new DefaultServiceException(ResultConstant.RECORD_EXIST);
        }
    }

    /**
     * 校验要更新的价格
     *
     * @param areaPriceAdjustment
     */
    private void checkPrice(String areaPriceAdjustment) {
        // 调用此数据校验位置 1 采购或运营发起调价审核 2运营同意或拒绝调价 ,都对对调价空集合数组过滤
        if (StringUtils.isBlank(areaPriceAdjustment) || Objects.equals("[]",areaPriceAdjustment)) {
            throw new DefaultServiceException("参数异常");
        }
        List<AreaSku> areaSkus = JSON.parseArray(areaPriceAdjustment, AreaSku.class);
        for (AreaSku areaSku : areaSkus) {
            //校验价格
            if (areaSku.getPrice() == null || BigDecimal.ZERO.compareTo(areaSku.getPrice()) == 1) {
                throw new DefaultServiceException(ResultConstant.PRICE_TOO_MIN);
            }
            //校验阶梯价
            // 不再處理階梯價
/*            String ladderPriceJson = areaSku.getLadderPrice();
            if (StringUtils.isNotBlank(ladderPriceJson)) {
                List<LadderPriceVO> ladderPrice = JSONObject.parseArray(ladderPriceJson, LadderPriceVO.class);
                for (LadderPriceVO vo : ladderPrice) {
                    if (vo.getUnit() == null || vo.getUnit() <= 0) {
                        throw new DefaultServiceException(ResultConstant.LADDER_ERROR);
                    }
                    if (vo.getPrice() == null || BigDecimal.ZERO.compareTo(vo.getPrice()) == 1) {
                        throw new DefaultServiceException(ResultConstant.PRICE_TOO_MIN);
                    }
                }
            }*/

        }
    }

    private Map<String, Map> auditResult(String areaPriceAdjustment) {
        List<AreaSku> skuList = JSONObject.parseArray(areaPriceAdjustment, AreaSku.class);

        Map<String, Map> result = new HashMap<>(skuList.size());
        for (AreaSku areaSku : skuList) {
            Map<String, String> areaMap = new HashMap<>(5);
            areaMap.put("areaName", areaSku.getAreaName());
            areaMap.put("activity", "0");
            areaMap.put("mj", "0");
            areaMap.put("mf", "0");
            areaMap.put("suit", areaSuitMapper.skuInSuit(areaSku.getAreaNo(), areaSku.getSku()) ? "1" : "0");

//            ActivitySku activitySku = activitySkuMapper.selectOne(areaSku.getAreaNo(), areaSku.getSku());
            CommonResult<ActivitySku> commonResult = activityNewService.getActivitySku(
                    areaSku.getSku(), areaSku.getAreaNo());
            ActivitySku activitySku = commonResult.getData();
            if (activitySku != null) {
                areaMap.put("activity", "1");
            }

            MarketRule mr = new MarketRule();
            mr.setAreaNo(areaSku.getAreaNo());
            mr.setSku(areaSku.getSku());
            List<MarketRule> marketRules = marketRuleMapper.selectWithout(mr);
            marketRules.forEach(el -> {
                if (el.getType() == 0) {
                    areaMap.put("mf", "1");
                } else if (el.getType() == 1) {
                    areaMap.put("mj", "1");
                }
            });

            result.put(areaSku.getAreaNo().toString(), areaMap);
        }

        return result;
    }

    @Override
    public BigDecimal findSkuMaxPriceInAllArea(String sku) {
        return areaSkuMapper.selectSkuMaxPriceInAllArea(sku);
    }

    public static void main(String[] args) throws InterruptedException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("任务1");

        Thread.sleep(1000);
        stopWatch.stop();
//        System.out.println(stopWatch.getLastTaskName()+":"+stopWatch.getLastTaskTimeMillis());
        stopWatch.start("任务2");
        Thread.sleep(3000);
        stopWatch.stop();
//        System.out.println(stopWatch.getLastTaskName()+":"+stopWatch.getLastTaskTimeMillis());
        System.out.println("总调用时间"+stopWatch.getTotalTimeMillis());
        System.out.println("总分布"+stopWatch.prettyPrint());
    }
}

