package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.cosfo.summerfarm.enums.CreateTypeEnums;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.hankcs.hanlp.HanLP;
import io.netty.util.internal.StringUtil;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelCheckResult;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.es.EsIndexContext;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.es.EsClientPoolUtil;
import net.summerfarm.facade.goods.GoodsFacade;
import net.summerfarm.goods.client.enums.ProductConfigEnum;
import net.summerfarm.goods.client.req.GoodsCodeInputReq;
import net.summerfarm.goods.client.resp.GoodsCodeResp;
import net.summerfarm.mapper.PurchaseProductWarehouseConfigMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.mapper.product.ProductConfigMapper;
import net.summerfarm.model.DTO.product.ProductInsertResponsibleMsgDTO;
import net.summerfarm.model.ProductConfig;
import net.summerfarm.model.bo.ProductSaveResultBO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.AreaSkuQuery;
import net.summerfarm.model.input.ProductQuery;
import net.summerfarm.model.input.ProductsSaveReq;
import net.summerfarm.model.param.PdNameSearchParam;
import net.summerfarm.model.vo.*;
import net.summerfarm.module.pms.facade.pms.PopAllCategoryGoodsMappingFacade;
import net.summerfarm.module.pms.facade.pms.req.PopAllCategoryGoodsMappingSaveFacadeReq;
import net.summerfarm.module.wms.infrastructure.facade.TenantQueryFacade;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.mq.constant.ProductMqConstant;
import net.summerfarm.service.*;
import net.summerfarm.service.item.command.PopProductCopyCommand;
import net.summerfarm.service.item.command.PopProductPublishCommand;
import net.summerfarm.service.item.convert.PopProductFactory;
import net.summerfarm.service.item.resp.PopProductCopyResp;
import net.summerfarm.service.item.resp.PopProductPublishResp;
import net.summerfarm.service.purchase.ProductWarehousePlannerHandler;
import net.summerfarm.validator.InventoryValidator;
import net.summerfarm.warehouse.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.xianmu.common.exception.BizException;
import net.xianmu.redis.support.lock.annotation.XmLock;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.MatchAllQueryBuilder;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static net.summerfarm.enums.ExcelErrorCode.*;
import static net.summerfarm.enums.InventorySubTypeEnum.CONSIGNMENT_NO_WAREHOUSE;
import static net.summerfarm.enums.InventorySubTypeEnum.findByName;

/**
 * @Package: net.summerfarm.service.impl
 * @Description: 商品业务类
 * @author: <EMAIL>
 * @Date: 2016/7/26
 */
@Service
public class ProductsServiceImpl extends BaseService implements ProductsService {
    @Resource
    private ProductLabelValueService productLabelValueService;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private GoodsLocationDetailMapper goodsLocationDetailMapper;
    @Resource
    private ProductsPropertyService productsPropertyService;
    @Resource
    private FrontCategoryToCategoryMapper frontCategoryToCategoryMapper;
    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private DataBaseHandleService dataBaseHandleService;
    @Resource
    private GoodsLocationMapper goodsLocationMapper;
    @Resource
    private JavaMailSenderImpl mailSender;
    @Resource
    private CategoryService categoryService;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private WarehouseStockExtService warehouseStockExtService;
    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;
    @Resource
    private PurchaseProductWarehouseConfigMapper productWarehouseConfigMapper;

    @Resource
    private AreaSkuMapper areaSkuMapper;

    private ProductsService selfService;

    @Resource
    MqProducer mqProducer;

    @Resource
    private TenantQueryFacade tenantQueryFacade;
    @Resource
    private InventoryValidator inventoryValidator;
    @Resource
    private ExternalProductMappingHandler externalProductMappingHandler;
    @Resource
    private PopAllCategoryGoodsMappingFacade popAllCategoryGoodsMappingFacade;

    @PostConstruct
    private void setSelf() {
        selfService = getContext().getBean(ProductsService.class);
    }

    @Resource
    private CrmBdConfigMapper crmBdConfigMapper;
    @Resource
    private TaxRateService taxRateService;
    @Resource
    private ProductWarehousePlannerHandler productWarehousePlannerHandler;
    @Resource
    private GoodsFacade goodsFacade;
    @Resource
    private PopProductFactory popProductFactory;
    @Resource
    private ProductConfigMapper productConfigMapper;

    private static final Logger logger = LoggerFactory.getLogger(Products.class);

    /**
     * 商品上传模板固定字段列数
     */
    private static final Integer FIXED_LEN = 30;
    //商品上传关键属性字段列数
    private static final Integer KEY_LEN = 8;
    //redis缓存key
    private static final String PRODUCTS_KEY = "manage:products:search-list";
    //redis缓存saaskey
    private static final String SAAS_PRODUCTS_KEY = "saas:products:search-list";
    //商品状态字段
    private static final String STATUS = "audit_status";
    //id字段
    private static final String ID = "pd_id";

    private static final Pattern EXT_PATTERN = Pattern.compile("\\(.*?\\)|（.*?）");

    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(2, 8,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new cn.hutool.core.thread.NamedThreadFactory("productOp-", false),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public AjaxResult select(int pageIndex, int pageSize, ProductsVO selectKeys) {
        PageHelper.startPage(pageIndex, pageSize);
        List<ProductsVO> products = productsMapper.select(selectKeys);
        return AjaxResult.getOK(new PageInfo<>(products));
    }

    @Override
    public AjaxResult selectPage(int pageIndex, int pageSize, ProductQuery productQuery) {
        PageHelper.startPage(pageIndex, pageSize);
        List<ProductListVO> products = productsMapper.selectPageByCondition(productQuery);
        for (ProductListVO product : products) {
            Iterator<InventoryDetailVO> iterator = product.getInventoryDetailVOS().iterator();
            while (iterator.hasNext()) {
                InventoryDetailVO inventoryDetailVO = iterator.next();
                if (productQuery.getOutdated() != null && inventoryDetailVO.getOutdated() != null && !productQuery.getOutdated().equals(inventoryDetailVO.getOutdated())) {
                    iterator.remove();
                    continue;
                } else if (productQuery.getType() != null && inventoryDetailVO.getType() != null && !productQuery.getType().equals(inventoryDetailVO.getType())) {
                    iterator.remove();
                    continue;
                } else if (productQuery.getSubType() != null && !ObjectUtil.equal(productQuery.getSubType(), inventoryDetailVO.getSubType())) {
                    iterator.remove();
                    continue;
                } else if (productQuery.getSku() != null && inventoryDetailVO.getSku() != null && !productQuery.getSku().equals(inventoryDetailVO.getSku())) {
                    iterator.remove();
                    continue;
                }
                String weight = inventoryDetailVO.getWeight();
                inventoryDetailVO.setWeight(StringUtils.isNotBlank(weight) ? weight.substring(weight.indexOf("_") + 1) : StringUtil.EMPTY_STRING);
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(products));
    }

    @Override
    public AjaxResult select(Integer brandId) {
        ProductsVO selectKeys = new ProductsVO();
        selectKeys.setBrandId(brandId);
        List<ProductsVO> products = productsMapper.select(selectKeys);
        return AjaxResult.getOK(products);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult save(ProductsSaveReq productsReq) {
        Integer categoryId = productsReq.getCategoryId();

        //挂靠的销售属性不可超过10个
        if (!CollectionUtils.isEmpty(productsReq.getSalePropertyList()) && productsReq.getSalePropertyList().size() > 10) {
            return AjaxResult.getErrorWithMsg("销售属性挂靠不可超过10个");
        }
        //校验关键属性值是否都填写
        List<ProductsProperty> effectKeyPropertyList = productsPropertyService.selectEffectKeyPropertyByCategoryId(categoryId);
        for (ProductsProperty property : effectKeyPropertyList) {
            boolean flag = productsReq.getKeyValueList()
                    .stream()
                    .noneMatch(val -> Objects.equals(property.getId(), val.getProductsPropertyId()));
            if (flag) {
                return AjaxResult.getErrorWithMsg("请填写关键属性：" + property.getName());
            }
        }
        //校验所有关键属性值格式是否正确
        List<ProductsProperty> wrongKeyPropertyList = productsPropertyService.checkValueFormat(productsReq.getKeyValueList());
        if (!CollectionUtils.isEmpty(wrongKeyPropertyList)) {
            return AjaxResult.getErrorWithMsg("关键属性填写格式错误：" + wrongKeyPropertyList.get(0).getName());
        }

        //生成商品编号
        Integer origin = productsReq.getOrigin();
        StringBuilder sb = new StringBuilder();
        sb.append(categoryId);
        if (origin != null) {
            sb.append(origin);
        }
        String pdNo = sb.append(StringUtils.getRandomNumber(5)).toString();
        productsReq.setPdNo(pdNo);
        productsReq.setCreateTime(new Date());

        int rs = productsMapper.insertSelective(productsReq);
        if (rs != 0) {
            //插入关键属性值
            productsPropertyService.addKeyPropertyValue(productsReq.getPdId(), productsReq.getKeyValueList());

            //插入销售属性映射
            Optional.ofNullable(productsReq.getSalePropertyList()).ifPresent(propertyList -> productsPropertyService.addSalePropertyMapping(productsReq.getPdId(), productsReq.getSalePropertyList()));

            return AjaxResult.getOK(productsReq.getPdId());
        }
        return AjaxResult.getError(ResultConstant.DEFAULT_FAILED);
    }


    /**
     * 自动新增采购负责人
     *
     * @param pdId
     */
    public void insertResponsible(Long pdId) {
        ProductsVO productsVO = productsMapper.selectByPdId(pdId);
        if (productsVO == null) {
            logger.info("插入采购负责人时查询商品:"+pdId+"为空");
            return;
        }
        Admin admin = adminMapper.selectByAid(productsVO.getCreator());
        if (admin != null) {
            ProductInsertResponsibleMsgDTO msgDTO = new ProductInsertResponsibleMsgDTO();
            msgDTO.setPdId(productsVO.getPdId());
            msgDTO.setCreateType(productsVO.getCreateType());
            msgDTO.setCreatorId(productsVO.getCreator());
            msgDTO.setCreatorName(admin.getRealname());
            msgDTO.setOperatorId(getAdminId());
            msgDTO.setOperatorName(getAdminName());
            msgDTO.setPdNo(productsVO.getPdNo());
            mqProducer.send(ProductMqConstant.TOPIC_PRODUCT_INSERT_RESPONSIBLE,ProductMqConstant.TAG_PRODUCT_UPDATE_RESPONSIBLE,JSON.toJSONString(msgDTO));
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(Long id, ProductsSaveReq productsReq) {
        //校验传入id合理性
        if (id <= 0) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        productsReq.setPdId(id);
        ProductsVO productsVO = productsMapper.selectByPdId(id);
        boolean categoryChanged = !Objects.equals(productsVO.getCategoryId(), productsReq.getCategoryId());
        if (categoryChanged) {
            productsPropertyService.deleteSpuProperty(id);
        }

        //校验所有关键属性值格式是否正确
        List<ProductsProperty> wrongKeyPropertyList = productsPropertyService.checkValueFormat(productsReq.getKeyValueList());
        if (!CollectionUtils.isEmpty(wrongKeyPropertyList)) {
            return AjaxResult.getErrorWithMsg("关键属性填写格式错误：" + wrongKeyPropertyList.get(0).getName());
        }

        //校验关键属性值是否都填写
        List<ProductsProperty> effectKeyPropertyList = productsPropertyService.selectEffectKeyPropertyByCategoryId(productsReq.getCategoryId());
        List<ProductsPropertyValueVO> keyValueList = productsPropertyService.selectKeyValueByPdId(id);
        for (ProductsProperty property : effectKeyPropertyList) {
            if (keyValueList.stream().anyMatch(fill -> Objects.equals(fill.getProductsPropertyId(), property.getId()))) {
                continue;
            }
            if (productsReq.getKeyValueList().stream().noneMatch(val -> Objects.equals(property.getId(), val.getProductsPropertyId()))) {
                return AjaxResult.getErrorWithMsg("请填写关键属性：" + property.getName());
            }
        }

        //校验新增销售属性值是否都填写 且 格式是否正确（修改时这里的参数只包含新增的销售属性）
        if (!CollectionUtils.isEmpty(productsReq.getSalePropertyList())) {
            List<Inventory> skuList = inventoryMapper.selectByPdId(productsReq.getPdId().intValue());
            if (!CollectionUtils.isEmpty(skuList)) {
                Map<String, List<ProductsPropertyValue>> saleValueMap = productsReq.getSaleValueList().stream().collect(Collectors.groupingBy(ProductsPropertyValue::getSku));
                for (List<ProductsPropertyValue> valueList : saleValueMap.values()) {
                    for (ProductsProperty property : productsReq.getSalePropertyList()) {
                        boolean flag = valueList.stream().noneMatch(el -> Objects.equals(property.getId(), el.getProductsPropertyId()));
                        if (flag) {
                            return AjaxResult.getErrorWithMsg("请填写销售属性：" + property.getName());
                        }
                    }
                    List<ProductsProperty> wrongSalePropertyList = productsPropertyService.updateCheckValueFormat(valueList);
                    if (!CollectionUtils.isEmpty(wrongSalePropertyList)) {
                        logger.warn("销售属性填写格式错误:{}", wrongSalePropertyList);
                        return AjaxResult.getErrorWithMsg("销售属性填写格式错误：" + wrongSalePropertyList.get(0).getName());
                    }
                }
            }
        }

        //修改温区时校验
        if (!Objects.equals(productsVO.getStorageLocation(), productsReq.getStorageLocation()) && productsReq.getStorageLocation() != null) {
            List<Inventory> inventories = inventoryMapper.selectByPdId(Math.toIntExact(id));
            inventories.forEach(x -> {
                List<GoodsLocationDetailVO> query = goodsLocationDetailMapper.selectBySku(x.getSku());
                if (!CollectionUtils.isEmpty(query)) {
                    GoodsLocationDetailVO detailVO = query.get(NumberUtils.INTEGER_ZERO);
                    String glNo = detailVO.getGlNo();
                    Integer storeNo = detailVO.getStoreNo();
                    GoodsLocation goodsLocation = goodsLocationMapper.selectByGlNo(storeNo, glNo);
                    if (goodsLocation != null && !Objects.equals(productsReq.getStorageLocation(), goodsLocation.getTemperature())) {
                        throw new DefaultServiceException("存在有货位的sku,无法更改温区");
                    }

                }
            });
        }

        //判断是否是上新审核
        if (productsReq.getAuditFlag() != null) {
            //校验
            AjaxResult result = operateCheck(productsReq);
            if (result != null) {
                return result;
            }
            AjaxResult result1 = commonCheck(productsReq);
            if (result1 != null) {
                return result1;
            }
            //校验sku信息
            List<InventoryVO> skuList = productsReq.getSkuList();
            for (InventoryVO sku : skuList) {
                if (StringUtils.isBlank(sku.getUnit())) {
                    return AjaxResult.getErrorWithMsg("请填写SKU包装");
                }
                if (sku.getType() == null) {
                    return AjaxResult.getErrorWithMsg("请填写SKU性质");
                }
                if (StringUtils.isBlank(sku.getVolume())) {
                    return AjaxResult.getErrorWithMsg("请填写SKU体积");
                }
                if (StringUtils.isBlank(sku.getWeightNum())) {
                    return AjaxResult.getErrorWithMsg("请填写SKU重量");
                }
            }

            //是否能够审核
            boolean outFlag = ProductsEnum.Outdated.CREATING.getCode().equals(productsVO.getOutdated());
            boolean auditFlag = ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal() == productsVO.getAuditStatus();

            if (!(outFlag && auditFlag)) {
                return AjaxResult.getErrorWithMsg("当前商品不可审核上线");
            }
            productsReq.setAuditTime(LocalDateTime.now());
            productsReq.setAuditor(getAdminId());
            if (productsReq.getAuditFlag()) {
                productsReq.setOutdated(ProductsEnum.Outdated.VALID.getCode());
                productsReq.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                //上新审核成功插入采购负责人
                insertResponsible(productsReq.getPdId());
            } else {
                productsReq.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
            }

            //处理sku上新
            Inventory query = new Inventory();
            query.setPdId(productsReq.getPdId());
            query.setOutdated(ProductsEnum.Outdated.CREATING.getCode());
            query.setAuditStatus(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal());
            List<Inventory> auditSkuList = inventoryMapper.selectList(query);
            for (Inventory auditSku : auditSkuList) {
                auditSku.setAuditTime(LocalDateTime.now());
                if (productsReq.getAuditFlag()) {
                    auditSku.setOutdated(ProductsEnum.Outdated.VALID.getCode());
                    auditSku.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                } else {
                    auditSku.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
                }
                logger.info("updateSpuAndSku Operator:{},:{}",getAdminOperator(),JSON.toJSONString(auditSku));
                inventoryMapper.update(auditSku);
            }

        } else {
            productsReq.setAuditStatus(null);
        }

        int rs = productsMapper.update(productsReq);
        if (rs != 0) {
            //更新关键属性值
            productsPropertyService.addKeyPropertyValue(productsReq.getPdId(), productsReq.getKeyValueList());

            //更新销售属性映射
            Optional.ofNullable(productsReq.getSalePropertyList()).ifPresent(propertyList -> productsPropertyService.addSalePropertyMapping(productsReq.getPdId(), productsReq.getSalePropertyList()));

            //添加销售属性值
            Optional.ofNullable(productsReq.getSaleValueList()).ifPresent(o -> {
                Map<String, List<ProductsPropertyValue>> saleValueMap = o.stream().collect(Collectors.groupingBy(ProductsPropertyValue::getSku));
                for (Map.Entry<String, List<ProductsPropertyValue>> entry : saleValueMap.entrySet()) {
                    productsPropertyService.addSalePropertyValue(entry.getKey(), entry.getValue());
                }
            });

            return AjaxResult.getOK();
        }

        return AjaxResult.getError(ResultConstant.DEFAULT_FAILED);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult delete(Long[] ids) {
        // 校验临时账号权限
        inventoryValidator.checkPopSpuPermission(Arrays.asList(ids));
        List<Inventory> inventoryList = inventoryMapper.listByPdIdsNoCareOutdated(Sets.newHashSet(ids));
        if (productsMapper.deleteAll(ids) > 0) {
            if (CollectionUtil.isEmpty(inventoryList)) {
                logger.warn("不存在需要处理的sku,ids:{}", ids);
                return AjaxResult.getOK();
            }
            inventoryMapper.updateByPdId(ids, true);
            //同时将areaSku全都置为下架状态
            List<String> skus = inventoryList.stream().map(Inventory::getSku)
                    .collect(Collectors.toList());
            areaSkuMapper.updateOffSaleBatch(null, skus, getAdminName());
            return AjaxResult.getOK();
        } else {
            return AjaxResult.getError(ResultConstant.DELETE_NOT_EXIST);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult recovery(Long pdId) {
        ProductsVO productsVO = productsMapper.selectByPdId(pdId);
        if (productsVO.getCategoryId() == null) {
            return AjaxResult.getErrorWithMsg("类目缺失，请先编辑完善类目信息");
        }
        //恢复spu
        Products updateKeys = new Products();
        updateKeys.setPdId(pdId);
        updateKeys.setOutdated(ProductsEnum.Outdated.VALID.getCode());
        int spuResult = productsMapper.updateByPrimaryKeySelective(updateKeys);
        if (spuResult != 1) {
            throw new DefaultServiceException(ResultConstant.UPDATE_FAILED);
        }
        //恢复所有sku，并将所有sku下架，以防止直接恢复带来问题
        Long[] pdIds = {pdId};
        List<Inventory> inventoryList = inventoryMapper.listByPdIdsNoCareOutdated(Sets.newHashSet(pdIds));
        if (CollectionUtil.isEmpty(inventoryList)) {
            logger.warn("不存在需要处理的sku,pdId:{}", pdId);
            return AjaxResult.getOK();
        }
        List<String> skus = inventoryList.stream().map(Inventory::getSku)
                .collect(Collectors.toList());
        int rs = inventoryMapper.updateByPdId(pdIds, false);
        areaSkuMapper.updateOffSaleBatch(null, skus, getAdminName());
        logger.info("恢复spu:{},同时恢复其下sku共{}个", pdId, rs);

        //全量更新es数据
        esDataSyncOrInit();

        return AjaxResult.getOK(rs);
    }

    @Async
    @Override
    public void esDataSyncOrInit() {
        RestHighLevelClient client;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            e.printStackTrace();
            throw new DefaultServiceException("es连接异常");
        }

        //删除原数据
        DeleteByQueryRequest deleteRequest = new DeleteByQueryRequest(EsIndexContext.INDEX_PRODUCT);
        deleteRequest.setQuery(new MatchAllQueryBuilder());
        try {
            client.deleteByQuery(deleteRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            logger.error("es索引{}数据清空失败", EsIndexContext.INDEX_PRODUCT, e);
            return;
        }

        BulkRequest bulkRequest = new BulkRequest(EsIndexContext.INDEX_PRODUCT);

        int startPage = 1;
        int pageSize = 500;
        while (true) {
            List<Products> productsList = productsMapper.queryEsProductsInfo((startPage - 1) * pageSize, pageSize);
            productsList.forEach(el -> {
                IndexRequest indexRequest = new IndexRequest(EsIndexContext.INDEX_PRODUCT);
                indexRequest.id(el.getPdId().toString());
                indexRequest.source(createEsProductJson(el), XContentType.JSON);
                bulkRequest.add(indexRequest);
            });

            try {
                client.bulk(bulkRequest, RequestOptions.DEFAULT);
                bulkRequest.requests().clear();
            } catch (IOException e) {
                logger.error("es数据全量同步失败", e);
                break;
            }
            if (productsList.size() < pageSize) {
                break;
            }
            startPage++;
        }
        EsClientPoolUtil.returnClient(client);
    }

    @Override
    @XmLock(waitTime = 1000 * 60, key = "(ProductService.createProductBatch)")
    public AjaxResult createProductBatch(Integer type, MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        logger.info("批量商品导入执行开始>>>>>>>>>>");
        Workbook workbook = null;
        int failCount = 0;
        int totalCount = 0;
        try {
            Integer restoreId = null;
            if (Objects.equals(1, type)) {
                restoreId = dataBaseHandleService.createRestore("批量修改商品还原点_" + file.getOriginalFilename());
            }
            workbook = WorkbookFactory.create(file.getInputStream());
            Iterator<Sheet> sheetIterator = workbook.sheetIterator();
            Sheet sheet = null;
            while (sheetIterator.hasNext()) {
                try {
                    sheet = sheetIterator.next();
                    //留存关键属性值下标
                    Map<Integer, List<Integer>> indexMap = new HashMap<>();
                    //spu、sku数据解析
                    List<ProductsCheckVO> productsList;
                    if (Objects.equals(NumberUtils.INTEGER_ZERO, type)) {

                        //处理类目数据
                        String categoryName = sheet.getRow(2).getCell(0).getStringCellValue();

                        Integer cId = handleCategory(categoryName);
                        //关键属性解析
                        List<ProductsProperty> keyPropertyList = handleKeyPropertyList(indexMap, cId, sheet.getRow(1));

                        productsList = readOriginProductList(cId, indexMap, type, keyPropertyList, sheet);

                    } else {
                        //处理类目数据
                        Integer cId = null;
                        Row row = sheet.getRow(2);
                        if (row != null) {
                            Cell cell = row.getCell(1);
                            if (cell != null) {
                                String spu = sheet.getRow(2).getCell(1).getStringCellValue();
                                cId = productsMapper.selectCidByPdNo(spu);
                            }
                        }
                        List<ProductsProperty> keyPropertyList = handleKeyPropertyList(indexMap, cId, sheet.getRow(1));
                        productsList = readOriginProductList(null, indexMap, type, keyPropertyList, sheet);
                    }

                    //处理商品数据
                    for (ProductsCheckVO checkVO : productsList) {
                        if (!checkVO.getCheckPass()) {
                            continue;
                        }
                        try {
                            if (Objects.equals(0, type)) {
                                selfService.insertSpuAndSku(checkVO);
                            } else {
                                selfService.updateSpuAndSku(restoreId, checkVO);
                            }
                        } catch (Exception e) {
                            logger.error("商品数据处理异常", e);
                        }
                    }

                    //处理统计数据和报表
                    totalCount += productsList.size();
                    failCount += handleExport(sheet, productsList, KEY_LEN);
                } catch (Exception e) {
                    failCount++;
                    totalCount++;
                    sheet.createRow(2).createCell(0).setCellValue("新增模板类目名必填！！");
                    logger.error("商品数据处理异常", e);
                }
            }
            //统一更新es数据
            EXECUTOR_SERVICE.execute(this::esDataSyncOrInit);
        } catch (Exception e) {
            throw new DefaultServiceException("模板数据上传失败");
        } finally {
            IOUtils.closeQuietly(workbook);
        }

        //数据失败，生成报告
        if (failCount != 0) {
            String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")); // NOSONAR
            String fileName = "REPORT_SPU_SKU_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtils.NUMBER_DATE_FORMAT)) + fileType;
            result.put("reportId", fileName);

            //生成导入报告
            CommonFileUtils.generateExcelFile(Global.REPORT_DIR, fileName, workbook);
        }
        logger.info("批量商品导入执行结束>>>>>>>>>>");

        result.put("successCount", totalCount - failCount);
        result.put("failCount", failCount);
        return AjaxResult.getOK(result);
    }

    /**
     * 处理新增报表数据
     *
     * @param sheet
     * @param productsList
     * @return
     */
    private int handleExport(Sheet sheet, List<ProductsCheckVO> productsList, int size) {
        CellRangeAddress region = new CellRangeAddress(0, 0, sheet.getRow(0).getLastCellNum() + 1, sheet.getRow(0).getLastCellNum() + 3);
        sheet.addMergedRegion(region);

        int last = FIXED_LEN + size;
        sheet.getRow(0).createCell(last).setCellValue("错误信息");
        sheet.getRow(1).createCell(last).setCellValue("错误位置");
        sheet.getRow(1).createCell(last + 1).setCellValue("错误码");
        sheet.getRow(1).createCell(last + 2).setCellValue("错误详情");

        int failCount = 0;
        for (int i = 2; i <= sheet.getLastRowNum() && !CollectionUtils.isEmpty(productsList); i++) {
            ProductsCheckVO vo = productsList.get(i - 2);
            if (vo.getCheckPass()) {
                continue;
            }

            Row row = sheet.getRow(i);
            String col = ExcelUtils.excelColIndexToStr(vo.getColIndex() + 1);
            row.createCell(last).setCellValue(col + (i + 1));
            row.createCell(last + 1).setCellValue(vo.getErrorCode().getCode());
            row.createCell(last + 2).setCellValue(vo.getErrorCode().getDesc());

            failCount++;
        }
        return failCount;
    }

    /**
     * 数据校验、合并相同spu下的sku
     *
     * @param type
     * @param productsList
     * @return
     */
    private List<ProductsVO> dataCheckAndMerge(Integer type, List<ProductsVO> productsList) {
        List<ProductsVO> result = new ArrayList<>();

        Set<String> existSpu = new HashSet<>();
        Map<String, Integer> sameSpu = new HashMap<>();
        for (ProductsVO productsVO : productsList) {
            if (productsVO.getErrorCode() == null) {
                productsVO.setInsertFlag(true);
            } else {
                productsVO.setInsertFlag(false);
                continue;
            }

            //新增校验
            if (Objects.equals(0, type)) {
                //仅新增sku
                if (StringUtils.isNotBlank(productsVO.getPdNo())) {
                    productsVO.setInsertFlag(false);
                } else {
                    //校验必填字段
                    if (StringUtils.isBlank(productsVO.getPdName(), productsVO.getStorageLocation(), productsVO.getQualityTime())) {
                        productsVO.setErrorCode(UN_FILL);
                        productsVO.setErrorDesc("SPU缺失必填数据");
                        continue;
                    }

                    if (productsVO.getKeyValueList().stream().anyMatch(el -> StringUtils.isBlank(el.getProductsPropertyValue()))) {
                        productsVO.setErrorCode(UN_FILL);
                        productsVO.setErrorDesc("缺失关键属性数据");
                        continue;
                    }

                    //字段长度校验
                    if (StringUtils.isNotBlank(productsVO.getSlogan()) && productsVO.getSlogan().length() > 30) {
                        productsVO.setErrorCode(ExcelErrorCode.CONFIG_ERROR);
                        productsVO.setErrorDesc("商品介绍限制30字");
                        continue;
                    }
                }

                InventoryVO skuVO = CollectionUtils.isEmpty(productsVO.getSkuList()) ? null : productsVO.getSkuList().get(0);
                if (skuVO == null || StringUtils.isBlank(skuVO.getType(), skuVO.getWeight(), skuVO.getBaseSaleUnit(), skuVO.getBaseSaleQuantity(), skuVO.getUnit(), skuVO.getAfterSaleUnit(), skuVO.getAfterSaleQuantity())) {
                    productsVO.setErrorCode(UN_FILL);
                    productsVO.setErrorDesc("SKU缺失必填数据");
                    continue;
                }

                //限制仅支持自营
                if (!Objects.equals(skuVO.getType(), 0)) {
                    productsVO.setErrorCode(INFO_ERROR);
                    productsVO.setErrorDesc("目前仅支持上传自营商品");
                    continue;
                }

                //校验同类目下是否已存在同名spu
                String skuMd5 = saleValue2MD5(skuVO.getSaleValueList());
                List<Products> oldProducts = productsMapper.selectByCIdAndPdName(productsVO.getCategoryId(), productsVO.getPdName(), null);
                if (existSpu.contains(productsVO.getPdName()) || !CollectionUtils.isEmpty(oldProducts)) {
                    existSpu.add(productsVO.getPdName());
                    productsVO.setInsertFlag(false);

                    //校验sku是否完全相同
                    boolean flag = false;
                    for (Products old : oldProducts) {
                        List<Inventory> oldSkuList = inventoryMapper.selectByPdId(old.getPdId().intValue());
                        flag = oldSkuList.stream()
                                .filter(el -> Objects.equals(false, el.getOutdated()))
                                .anyMatch(el -> {
                                    String md5 = saleValue2MD5(productsPropertyService.selectSaleValueBySku(el.getSku()));
                                    return Objects.equals(skuMd5, md5);
                                });
                        if (flag) {
                            break;
                        }
                    }

                    if (flag) {
                        productsVO.setErrorCode(ExcelErrorCode.EXIST_ERROR);
                        productsVO.setErrorDesc("该类目下存在同名商品且销售属性相同");
                        continue;
                    }
                }

                //相同spu则合并sku
                if (sameSpu.containsKey(productsVO.getPdName())) {
                    List<InventoryVO> skuList = result.get(sameSpu.get(productsVO.getPdName())).getSkuList();

                    //校验导入数据是否有相同sku
                    boolean flag = false;
                    for (InventoryVO importSku : skuList) {
                        String md5 = saleValue2MD5(importSku.getSaleValueList());
                        flag = Objects.equals(skuMd5, md5);
                    }

                    if (flag) {
                        productsVO.setErrorCode(ExcelErrorCode.EXIST_ERROR);
                        productsVO.setErrorDesc("导入数据中存在相同SKU");
                        continue;
                    }
                    skuList.addAll(productsVO.getSkuList());
                } else {
                    result.add(productsVO);
                    sameSpu.put(productsVO.getPdName(), result.size() - 1);
                }
            } //修改校验
            else {

            }
        }

        return result;
    }

    /**
     * 解析类目数据
     *
     * @param categoryName 形如：pName-cName
     * @return
     */
    private Integer handleCategory(String categoryName) {
        Category category = categoryMapper.selectByName(null, null, categoryName);
        return category.getId();
    }

    /**
     * 处理关键属性
     *
     * @param row 行数据
     * @return
     */
    private List<ProductsProperty> handleKeyPropertyList(Map<Integer, List<Integer>> indexMap, Integer cId, Row row) {
        List<ProductsProperty> result = new ArrayList<>();

        List<ProductsProperty> keyPropertyList = productsPropertyService.selectKeyPropertyByCategoryId(cId);
        List<Integer> indexList = new ArrayList<>();
        for (int i = 8; i < row.getLastCellNum() - 22; i++) {
            String title = ExcelUtils.getCellStringValue(row.getCell(i));
            for (ProductsProperty property : keyPropertyList) {
                if (title.startsWith(property.getName())) {
                    result.add(property);
                    indexList.add(i);
                    break;
                }
            }
        }
        indexMap.put(cId, indexList);
        return result;
    }

    /**
     * 处理商品数据
     *
     * @param type            类型：0、新增 1.修改
     * @param keyPropertyList 关键属性
     * @param sheet           sheet
     * @return
     */
    private List<ProductsCheckVO> readOriginProductList(Integer cId, Map<Integer, List<Integer>> indexMap, Integer type, List<ProductsProperty> keyPropertyList, Sheet sheet) {

        //初始化校验
        importCheckFunInstance(indexMap);

        //所有销售属性
        Map<String, Integer> salePropertyMap = new HashMap<>();
        List<ProductsProperty> allSaleProperty = productsPropertyService.selectAllProperty(1);
        for (ProductsProperty property : allSaleProperty) {
            salePropertyMap.put(property.getName(), property.getId());
        }

        int lastIndex = FIXED_LEN + KEY_LEN;
        List<ProductsCheckVO> result = new ArrayList<>();
        for (int i = 2; i <= sheet.getLastRowNum(); i++) {
            ProductsCheckVO importVO = new ProductsCheckVO();
            result.add(importVO);

            try {
                //读取原始值
                String[] originStringData = getRowOriginTrimStringData(lastIndex, sheet.getRow(i), type);
                logger.info(JSONObject.toJSONString(originStringData));

                //判断处理类型
                judgeHandleType(type, KEY_LEN, importVO, originStringData);
                //测试使用需清理
                logger.info("看看importVO：" + JSON.toJSONString(importVO));
                //数据校验
                importVO.setCheckPass(true);
                ExcelCheckResult checkResult = originDataCheck(type, importVO.getProductsHandleFlag(), importVO.getInventoryHandleFlag(), originStringData);
                if (checkResult != null) {
                    importVO.setCheckPass(false);
                    importVO.setColIndex(checkResult.getColIndex());
                    importVO.setErrorCode(checkResult.getErrorCode());
                    continue;
                }

                //数据封装
                importVO.setProducts(originDataToProduct(cId, type, originStringData, lastIndex));
                importVO.setKeyPropertyValue(originDataToKeyProperty(indexMap, originStringData, keyPropertyList, keyPropertyList, sheet));
                importVO.setInventory(originDataToInventory(originStringData, lastIndex));
                importVO.setSalePropertyValue(originDataToSaleProperty(originStringData, lastIndex, salePropertyMap));
            } catch (Exception e) {
                importVO.setErrorCode(ExcelErrorCode.FORMAT_ERROR);
                logger.error("第{}行数据解析异常：", i + 1, e);
            }
        }
        return result;
    }

    /**
     * @param type             上传类型
     * @param originStringData 原始数据
     * @param lastIndex        row last index
     * @return 生成spu信息
     */
    private Products originDataToProduct(Integer cId, Integer type, String[] originStringData, int lastIndex) {
        Products products = new Products();
        if (Objects.equals(0, type)) {
            products.setCategoryId(cId);
            products.setCreateTime(new Date());
            products.setAfterSaleType("商品数量不符;包装问题;商品品质问题;其他");
            products.setRefundType("拍多/拍错/不想要;缺货;其他");
            products.setAfterSaleTime(48);
        }
        //pdNo
        products.setPdNo(originStringData[1]);
        //商品名
        products.setPdName(originStringData[2]);
        //商品介绍改名副标题
        products.setPddetail(originStringData[3]);
        //实物名称
        products.setRealName(originStringData[4]);
        //贮存区域
        String location = originStringData[5];
        products.setStorageLocation(StorageLocation.getIdByType(location));
        //保质期时长（目前仅支持月）1yue
        String quantityTime = originStringData[6];
        if (StringUtils.isNotBlank(quantityTime)) {
            if (quantityTime.endsWith("月")) {
                products.setQualityTimeUnit("month");
            } else {
                products.setQualityTimeUnit("day");
            }
            products.setQualityTime(Integer.valueOf(quantityTime.replace("天", "").replace("月", "")));
        }
        //到期预警
        String warnTime = originStringData[7];
        if (StringUtils.isNotBlank(warnTime)) {
            products.setWarnTime(Integer.valueOf(warnTime));
        }
        //其它介绍
        products.setOtherSlogan(originStringData[lastIndex - 23]);

        return products;
    }

    /**
     * @param originStringData  原始数据
     * @param keyPropertyList   关键属性
     * @param effectKeyProperty 类目挂靠关键属性
     * @return 生成关键属性
     */
    private List<ProductsPropertyValue> originDataToKeyProperty(Map<Integer, List<Integer>> indexMap, String[] originStringData, List<ProductsProperty> keyPropertyList, List<ProductsProperty> effectKeyProperty, Sheet sheet) {
        if (CollectionUtils.isEmpty(keyPropertyList)) {
            return null;
        }

        //关键属性值（根据挂靠数据填值，未填值数据填空，未挂靠数据丢弃）
        Map<Integer, String> keyPropertyValueMap = new HashMap<>();
        Row row = sheet.getRow(1);
        Iterator<Map.Entry<Integer, List<Integer>>> iterator = indexMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<Integer>> entry = iterator.next();
            List<Integer> indexList = entry.getValue();
            for (Integer index : indexList) {
                String title = ExcelUtils.getCellStringValue(row.getCell(index));
                String value = originStringData[index];
                for (ProductsProperty productsProperty : keyPropertyList) {
                    if (StringUtils.isNotBlank(keyPropertyValueMap.get(productsProperty.getId()))) {
                        continue;
                    }
                    if (title.startsWith(productsProperty.getName()) && StringUtils.isNotBlank(value)) {
                        keyPropertyValueMap.put(productsProperty.getId(), value);
                        break;
                    }
                }
            }
        }

        return effectKeyProperty.stream()
                .map(el -> {
                    ProductsPropertyValue value = new ProductsPropertyValue();
                    value.setProductsPropertyId(el.getId());
                    value.setProductsPropertyValue(keyPropertyValueMap.get(el.getId()));
                    return value;
                }).collect(Collectors.toList());
    }

    /**
     * @param originStringData 原始数据
     * @param lastIndex        row last index
     * @return 生成sku信息
     */
    private Inventory originDataToInventory(String[] originStringData, int lastIndex) {
        Inventory inventory = new Inventory();
        String sku = originStringData[lastIndex - 21];
        if (StringUtils.isNotBlank(sku)) {
            //SKU编码 去空格
            inventory.setSku(sku.trim());
        }

        //性质
        String productType = originStringData[lastIndex - 20];
        if (StringUtils.isNotBlank(productType)) {
            inventory.setType(productType.startsWith(InventoryTypeEnum.NORMAL.getDesc()) ? 0 : 1);
            List<String> split = StrUtil.split(productType, CharPool.DASHED);
            InventorySubTypeEnum subType = CONSIGNMENT_NO_WAREHOUSE;
            if (split.size() > 1) {
                subType = findByName(split.get(1));
            }
            inventory.setSubType(subType.subType);
        }

        //代仓所属
        String replaceBelong = originStringData[lastIndex - 19];
        if (StringUtils.isNotBlank(replaceBelong)) {
            inventory.setRealName(replaceBelong);
        }


        String extType = originStringData[lastIndex - 18];
        inventory.setExtType(InventoryExtTypeEnum.getType(extType));

        //SKU进口/国产
        String isDomestic = originStringData[lastIndex - 17];
        if (StringUtils.isNotBlank(isDomestic)) {
            if (isDomestic.equals("国产")) {
                inventory.setIsDomestic(1);
            } else if (isDomestic.equals("进口")) {
                inventory.setIsDomestic(0);
            }
        }

        //展示平均值
        String avg = originStringData[lastIndex - 16];
        if (StringUtils.isNotBlank(avg)) {
            inventory.setAveragePriceFlag(Objects.equals("是", avg) ? CommonNumbersEnum.ONE.getNumber() : CommonNumbersEnum.ZERO.getNumber());
        }

        //最小起售量
        String baseSaleQuantity = originStringData[lastIndex - 15];
        if (StringUtils.isNotBlank(baseSaleQuantity)) {
            inventory.setBaseSaleQuantity(Integer.valueOf(baseSaleQuantity));
        } else {
            inventory.setBaseSaleQuantity(1);
        }
        //起售规格
        String baseSaleUnit = originStringData[lastIndex - 14];
        if (StringUtils.isNotBlank(baseSaleUnit)) {
            inventory.setBaseSaleUnit(Integer.valueOf(baseSaleUnit));
        } else {
            inventory.setBaseSaleUnit(1);
        }
        //包装
        String unit = originStringData[lastIndex - 13];
        if (StringUtils.isNotBlank(unit)) {
            inventory.setUnit(originStringData[lastIndex - 13]);
        }
        //体积
        if (StringUtils.isNotBlank(originStringData[lastIndex - 12])) {
            inventory.setVolume(originStringData[lastIndex - 12]);
        }
        //重量
        String weightNum = originStringData[lastIndex - 11];
        if (StringUtils.isNotBlank(weightNum)) {
            inventory.setWeightNum(BigDecimal.valueOf(Double.parseDouble(weightNum)));
        }
        //售后单位
        if (StringUtils.isNotBlank(originStringData[lastIndex - 2])) {
            inventory.setAfterSaleUnit(originStringData[lastIndex - 2]);
        }
        //最大售后量
        String afterSaleQuantity = originStringData[lastIndex - 1];
        if (StringUtils.isNotBlank(afterSaleQuantity)) {
            inventory.setAfterSaleQuantity(Integer.valueOf(afterSaleQuantity));
        }
        return inventory;
    }

    /**
     * @param originStringData 原始数据
     * @param lastIndex        row last index
     * @param salePropertyMap  销售属性
     * @return 生成销售属性
     */
    private List<ProductsPropertyValue> originDataToSaleProperty(String[] originStringData, int lastIndex, Map<String, Integer> salePropertyMap) {
        //销售属性
        List<ProductsPropertyValue> saleValueList = new ArrayList<>();
        //规格-首区间
        String firstSection = originStringData[lastIndex - 10];
        //规格-尾区间
        String endSection = originStringData[lastIndex - 9];
        if (!StringUtils.isEmpty(firstSection) && !StringUtils.isEmpty(endSection)) {
            ProductsPropertyValue weight = new ProductsPropertyValue();
            weight.setProductsPropertyId(salePropertyMap.get("规格"));
            if (StringUtils.isNotBlank(firstSection) && StringUtils.isNotBlank(endSection)) {
                weight.setProductsPropertyValue(firstSection + "-" + endSection);
            }
            saleValueList.add(weight);
        } else {
            //规格-容量 规格-数量
            String w1 = originStringData[lastIndex - 8];
            String w2 = originStringData[lastIndex - 7];
            ProductsPropertyValue weight = new ProductsPropertyValue();
            weight.setProductsPropertyId(salePropertyMap.get("规格"));
            if (StringUtils.isNotBlank(w1) && StringUtils.isNotBlank(w2)) {
                weight.setProductsPropertyValue(w1 + "*" + w2);
            }
            saleValueList.add(weight);
        }
        //果规
        String fruit = originStringData[lastIndex - 6];
        if (!StringUtils.isEmpty(fruit)) {
            ProductsPropertyValue fruitValue = new ProductsPropertyValue();
            fruitValue.setProductsPropertyId(salePropertyMap.get("果规"));
            fruitValue.setProductsPropertyValue(fruit);
            saleValueList.add(fruitValue);
        }
        //尺寸
        String size = originStringData[lastIndex - 5];
        if (!StringUtils.isEmpty(size)) {
            ProductsPropertyValue sizeValue = new ProductsPropertyValue();
            sizeValue.setProductsPropertyId(salePropertyMap.get("尺寸"));
            sizeValue.setProductsPropertyValue(fruit);
            saleValueList.add(sizeValue);
        }
        // 级别
        String level = originStringData[lastIndex - 4];
        if (!StringUtils.isEmpty(level)) {
            ProductsPropertyValue levelValue = new ProductsPropertyValue();
            levelValue.setProductsPropertyId(salePropertyMap.get("级别"));
            levelValue.setProductsPropertyValue(level);
            saleValueList.add(levelValue);
        }
        // 口味
        String toast = originStringData[lastIndex - 3];
        if (!StringUtils.isEmpty(toast)) {
            ProductsPropertyValue toastValue = new ProductsPropertyValue();
            toastValue.setProductsPropertyId(salePropertyMap.get("口味"));
            toastValue.setProductsPropertyValue(toast);
            saleValueList.add(toastValue);
        }

        return saleValueList;
    }

    /**
     * 数据校验
     *
     * @param type
     * @param spuHandle
     * @param skuHandle
     * @param originStringData
     * @return
     */
    private ExcelCheckResult originDataCheck(Integer type, boolean spuHandle, boolean skuHandle, String[] originStringData) {
        Map<Integer, List<Function<String, ExcelErrorCode>>> checkMap = new HashMap<>();
        if (Objects.equals(0, type)) {
            if (spuHandle) {
                checkMap.putAll(SPU_ADD_CHECK);
            }
            if (skuHandle) {
                checkMap.putAll(SKU_ADD_CHECK);
            }
        } else {
            if (spuHandle) {
                checkMap.putAll(SPU_UPDATE_CHECK);
            }
            if (skuHandle) {
                checkMap.putAll(SKU_UPDATE_CHECK);
            }
        }

        for (int i = 0; i < originStringData.length; i++) {
            //清空临时校验
            if (i == 0) {
                SALE_PROPERTY_MAPPING_LIST = null;
            }

            if (checkMap.containsKey(i)) {
                for (Function<String, ExcelErrorCode> function : checkMap.get(i)) {
                    ExcelErrorCode code = function.apply(originStringData[i]);
                    if (code != null) {
                        ExcelCheckResult result = new ExcelCheckResult(code);
                        result.setColIndex(i);
                        return result;
                    }
                }
            }
            if (Objects.equals(1, i) && StringUtils.isNotBlank(originStringData[i])) {
                String pdNo = originStringData[i];
                Products products = productsMapper.selectPdNo(pdNo);
                if (Objects.nonNull(products)) {
                    if (Objects.equals(products.getCreateType(), ProductsEnum.CreateType.FAN_TAI_AGENT.ordinal())) {
                        ExcelCheckResult result = new ExcelCheckResult(FAN_TAI_AGENT_NOT_ALLOW_NO_UPDATE);
                        result.setColIndex(i);
                        return result;
                    }
                }
            }
            if (Objects.equals(28, i)) {
                if (StringUtils.isBlank(originStringData[i])) {
                    if (Objects.equals(0, type) && StringUtils.isBlank(originStringData[30]) && StringUtils.isBlank(originStringData[31])) {
                        ExcelCheckResult result = new ExcelCheckResult(UN_FILL);
                        result.setColIndex(i);
                        return result;
                    }
                }
            }
            if (Objects.equals(29, i)) {
                if (!StringUtils.isBlank(originStringData[28])) {
                    if (StringUtils.isBlank(originStringData[i])) {
                        ExcelCheckResult result = new ExcelCheckResult(UN_FILL);
                        result.setColIndex(i);
                        return result;
                    }
                }
            }
            if (Objects.equals(30, i)) {
                if (Objects.equals(0, type) && StringUtils.isBlank(originStringData[28]) && StringUtils.isBlank(originStringData[29])) {
                    if (StringUtils.isBlank(originStringData[i])) {
                        ExcelCheckResult result = new ExcelCheckResult(UN_FILL);
                        result.setColIndex(i);
                        return result;
                    }
                }
            }
            if (Objects.equals(31, i)) {
                if (!StringUtils.isBlank(originStringData[30])) {
                    if (StringUtils.isBlank(originStringData[i])) {
                        ExcelCheckResult result = new ExcelCheckResult(UN_FILL);
                        result.setColIndex(i);
                        return result;
                    }
                }
            }
            // 如果性质是代仓并且所属是空的情况下返回异常
            if (Objects.equals(19, i)) {
                if (!StringUtils.isBlank(originStringData[18]) && Objects.equals("代仓-代仓", originStringData[18])) {
                    if (StringUtils.isEmpty(originStringData[19])) {
                        ExcelCheckResult result = new ExcelCheckResult(UN_FILL);
                        result.setColIndex(i);
                        return result;
                    } else {
                        Admin admin = adminMapper.selectByRealName(originStringData[19]);
                        if (admin == null) {
                            ExcelCheckResult result = new ExcelCheckResult(INFO_ERROR);
                            result.setColIndex(i);
                            return result;
                        }
                    }
                }
            }
            //如果修改类型，且进口国产不为空
            if (Objects.equals(21, i) && type == 1) {
                if (StringUtils.isNotBlank(originStringData[21])) {
                    ExcelCheckResult result = new ExcelCheckResult(INFO_ERROR);
                    result.setColIndex(i);
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 判断处理方式
     *
     * @param type
     * @param importVO
     * @param originStringData
     */
    private void judgeHandleType(Integer type, int keyPropertySize, ProductsCheckVO importVO, String[] originStringData) {
        importVO.setProductsHandleFlag(false);
        importVO.setInventoryHandleFlag(false);
        //新增上传
        if (Objects.equals(0, type)) {
            //spu编号为空，新增SPU
            if (StringUtils.isBlank(originStringData[1])) {
                importVO.setProductsHandleFlag(true);

                //sku字段信息不全为空，新增SKU
                for (int j = FIXED_LEN + keyPropertySize - 19; j < originStringData.length; j++) {
                    if (StringUtils.isNotBlank(originStringData[j])) {
                        importVO.setInventoryHandleFlag(true);
                        break;
                    }
                }
            } //spu编号不为空 -> 新增SKU
            else {
                importVO.setInventoryHandleFlag(true);
            }
        } // 修改上传
        else {
            //是否需要修改SPU
            for (int j = 1; j <= FIXED_LEN + keyPropertySize - 19; j++) {
                if (StringUtils.isNotBlank(originStringData[j])) {
                    importVO.setProductsHandleFlag(true);
                    break;
                }
            }
            //是否需要修改SKU
            for (int j = FIXED_LEN + keyPropertySize - 18; j < originStringData.length; j++) {
                if (StringUtils.isNotBlank(originStringData[j])) {
                    importVO.setInventoryHandleFlag(true);
                    break;
                }
            }
        }
    }

    /**
     * 读取原始数据string数组
     *
     * @param row
     * @return
     */
    private String[] getRowOriginTrimStringData(int size, Row row, Integer type) {
        String[] result = new String[size + NumberUtils.INTEGER_ONE];
        for (int i = 0; i < result.length; i++) {
            Cell cell = row.getCell(i);
            if (cell != null) {
                result[i] = ExcelUtils.getCellStringValue(cell).trim();
            }
        }

        //最后一位存类目id
        Category category = categoryMapper.selectBySku(result[17]);
        if (null != category) {
            result[size] = category.getId().toString();
        }

        //填充pdNo
        InventoryVO select = inventoryMapper.selectInventoryVOBySku(result[17]);
        if (null != select) {
            result[1] = select.getPdNo();
        }

        if (Objects.equals(NumberUtils.INTEGER_ZERO, type)) {
            //默认性质
            if (StringUtils.isBlank(result[18])) {
                result[18] = "自营";
            }
            //默认展示平均值
            if (StringUtils.isBlank(result[22])) {
                result[22] = "否";
            }
            //默认最小起售量
            if (StringUtils.isBlank(result[23])) {
                result[23] = "1";
            }
            //默认起售规格
            if (StringUtils.isBlank(result[24])) {
                result[24] = "1";
            }
        }
        return result;
    }

    /**
     * 插入商品数据
     *
     * @param checkVO 商品信息
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertSpuAndSku(ProductsCheckVO checkVO) {
        //处理spu
        if (checkVO.getProductsHandleFlag()) {
            String pdNo = null;
            int repeat = 1;
            while (repeat > 0) {
                pdNo = checkVO.getProducts().getCategoryId() + StringUtils.getRandomNumber(6);
                repeat = productsMapper.count(Integer.valueOf(pdNo));
            }
            checkVO.getProducts().setPdNo(pdNo);
            productsMapper.insertSelective(checkVO.getProducts());

            //关键属性值
            productsPropertyService.addKeyPropertyValue(checkVO.getProducts().getPdId(), checkVO.getKeyPropertyValue());
        } else {
            Long pdId = productsMapper.selectPdIdByPdNo(checkVO.getProducts().getPdNo());
            checkVO.getProducts().setPdId(pdId);
        }

        //sku信息
        if (checkVO.getInventoryHandleFlag()) {
            String sku = null;
            Map<String, Object> countKey = new HashMap<>();
            Inventory inventory = checkVO.getInventory();
            inventory.setPdId(checkVO.getProducts().getPdId());
            int repeat = 1;
            while (repeat > 0) {
                sku = checkVO.getProducts().getPdNo() + StringUtils.getRandomNumber(3);
                countKey.put("sku", sku);
                repeat = inventoryMapper.count(countKey);
            }
            if (inventory.getType() == 1) {
                Admin admin = adminMapper.selectByRealName(inventory.getRealName());
                inventory.setAdminId(admin.getAdminId());
            }
            inventory.setSku(sku);
            inventory.setVideoInfo(inventory.getVideoUrl (),getAdminName ());
            inventoryMapper.insertSelective(inventory);

            //sku-仓信息初始化
            areaStoreMapper.initAfterCreateSku(sku, BaseConstant.XIANMU_TENANT_ID);
            warehouseStockExtService.initWarehouseStockExt(sku);

            //销售属性
            if (!CollectionUtils.isEmpty(checkVO.getSalePropertyValue())) {
                productsPropertyService.addSalePropertyValue(sku, checkVO.getSalePropertyValue());
                List<ProductsProperty> salePropertyList = checkVO.getSalePropertyValue().stream().map(el -> {
                    ProductsProperty pp = new ProductsProperty();
                    pp.setId(el.getProductsPropertyId());
                    return pp;
                }).collect(Collectors.toList());
                productsPropertyService.addSalePropertyMapping(checkVO.getProducts().getPdId(), salePropertyList);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateSpuAndSku(Integer restoreId, ProductsCheckVO checkVO) {
        //spu
        if (checkVO.getProductsHandleFlag()) {
            long pdId = productsMapper.selectPdIdByPdNo(checkVO.getProducts().getPdNo());
            checkVO.getProducts().setPdId(pdId);

            //记录原始值
            Products oldProduct = productsMapper.selectByPrimaryKey(pdId);

            // 将不关心的字段丢入excludeNames
            List<String> excludeNames = new ArrayList<>();
            excludeNames.add("pdId");
            excludeNames.add("pdNo");

            try {
                //判对象为空
                if (!isAllFieldNull(checkVO.getProducts(), excludeNames)) {
                    //更新
                    productsMapper.updateByPrimaryKeySelective(checkVO.getProducts());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //创建还原点
            dataBaseHandleService.addRestoreData(restoreId, oldProduct, checkVO.getProducts(), Products.class, "pdId",
                    "pdName", "storageLocation", "qualityTime", "qualityTimeUnit", "warnTime", "pddetail", "otherSlogan");

            //记录原始值
            List<ProductsPropertyValueVO> oldKeyPPVO = productsPropertyService.selectKeyValueByPdId(pdId);
            List<ProductsPropertyValue> oldKeyPPV = oldKeyPPVO.stream().map(el -> (ProductsPropertyValue) el).collect(Collectors.toList());
            //更新
            if (!CollectionUtils.isEmpty(checkVO.getKeyPropertyValue())) {
                productsPropertyService.addKeyPropertyValue(checkVO.getProducts().getPdId(), checkVO.getKeyPropertyValue());
                //创建还原点
                dataBaseHandleService.addRestoreData(restoreId, oldKeyPPV, checkVO.getKeyPropertyValue(), ProductsPropertyValue.class, "id", "productsPropertyValue");
            }
        }

        //sku
        if (checkVO.getInventoryHandleFlag()) {
            //记录原始值
            Inventory query = new Inventory();
            query.setSku(checkVO.getInventory().getSku());
            Inventory oldSku = inventoryMapper.selectOne(query);
            Inventory inventory = checkVO.getInventory();
            String weight = inventory.getWeight();
            if (StringUtils.isNotBlank(weight) && oldSku.getWeight().contains("(")) {
                String weightDesc = oldSku.getWeight().substring(oldSku.getWeight().lastIndexOf("("));
                weight += weightDesc;
                inventory.setWeight(weight);
            }
            if (!ProductsEnum.Outdated.CREATING.getCode().equals(oldSku.getOutdated())
                    &&Objects.nonNull(oldSku.getAdminId())){
                Admin admin = adminMapper.selectByRealName(inventory.getRealName());
                if (Objects.nonNull(admin) && !oldSku.getAdminId().equals(admin.getAdminId())){
                    logger.error("sku:{}代仓所属不允许修改.",oldSku.getSku());
                    throw new RuntimeException("代仓所属不允许修改");
                }
                List<TenantResp> tenantRespList = tenantQueryFacade.listSaasTenant();
                List<Long> saasAdminIdList = tenantRespList.stream().map(TenantResp::getAdminId).collect(Collectors.toList());
                // 帆台所属代仓商品不允许修改
                if (saasAdminIdList.contains(oldSku.getAdminId().longValue())) {
                    logger.error("sku:{}帆台所属代仓商品不允许修改.",oldSku.getSku());
                    throw new RuntimeException("帆台所属代仓商品不允许修改,sku:"+oldSku.getSku());
                }
            }else {
                if (inventory.getType() != null && inventory.getType() == 1) {
                    Admin admin = adminMapper.selectByRealName(inventory.getRealName());
                    inventory.setAdminId(admin.getAdminId());
                }
            }

            if (inventory.getIsDomestic() != null) {
                throw new RuntimeException("修改时不可更改进口/国产");
            }
            logger.info("updateSpuAndSku Operator:{},:{}",getAdminOperator(),JSON.toJSONString(inventory));
            //更新
            inventoryMapper.update(inventory);
            //创建还原点
            dataBaseHandleService.addRestoreData(restoreId, oldSku, checkVO.getInventory(), Inventory.class, "invId",
                    "type", "baseSaleQuantity", "baseSaleUnit", "unit", "weightNum", "afterSaleUnit");

            //记录原始值
            List<ProductsPropertyValueVO> oldSalePPVO = productsPropertyService.selectSaleValueBySku(checkVO.getInventory().getSku());
            List<ProductsPropertyValue> oldSalePPV = oldSalePPVO.stream().map(el -> (ProductsPropertyValue) el).collect(Collectors.toList());
            //更新
            productsPropertyService.addSalePropertyValue(checkVO.getInventory().getSku(), checkVO.getSalePropertyValue());
            //创建还原点
            dataBaseHandleService.addRestoreData(restoreId, oldSalePPV, checkVO.getSalePropertyValue(), ProductsPropertyValue.class, "id", "productsPropertyValue");
        }
    }


    private boolean isAllFieldNull(Object obj, List<String> excludeNames) throws Exception {
        // 取到obj的class, 并取到所有属性
        Field[] fs = obj.getClass().getDeclaredFields();
        // 定义一个flag, 标记是否所有属性值为空
        boolean flag = true;
        // 遍历所有属性
        for (Field f : fs) {
            f.setAccessible(true); // 设置私有属性也是可以访问的
            // 1.排除不包括的属性名, 2.属性值不为空, 3.属性值转换成String不为""
            if (!excludeNames.contains(f.getName()) && f.get(obj) != null && !"".equals(f.get(obj).toString())) {
                // 有属性满足3个条件的话, 那么说明对象属性不全为空
                flag = false;
                break;
            }
        }
        return flag;

    }

    @Override
    public void getTemplateOrReport(Integer type, String reportFile) {
        //下载报告
        if (StringUtils.isNotBlank(reportFile)) {
            if (reportFile.startsWith("填写指南")) {
                CommonFileUtils.exportFile(RequestHolder.getResponse(), Global.REPORT_DIR, reportFile);
                return;
            }

            CommonFileUtils.exportFile(RequestHolder.getResponse(), Global.REPORT_DIR, reportFile, Objects.equals(0, 1) ? "【上传报告】批量新增商品信息.xls" : "【上传报告】批量修改商品信息.xls");
            return;
        }

        //下载模板文件
        Workbook template = generateTemplate(type);


        try {
            ExcelUtils.outputExcel(template, Objects.equals(1, type) ? "批量修改模板.xls" : "批量新增模板.xls", RequestHolder.getResponse());
        } catch (IOException e) {
            throw new DefaultServiceException("模板导出失败", e);
        }
    }

    @Override
    public AjaxResult getSpuSkuTemplateOrReport(Integer type) {
        //新增与修改模板暂时相同
        if (type == 0) {
            return AjaxResult.getOK(configMapper.selectOne(Global.SPU_SKU_INSERT_TEMPLATE));
        } else {
            return AjaxResult.getOK(configMapper.selectOne(Global.SPU_SKU_UPDATE_TEMPLATE));
        }
    }

    /**
     * 商品上新邮件通知
     */
    @Override
    public void freshProductsNotice() {
        Config mailAddress = configMapper.selectOne(ConfigValueEnum.PRODUCT_MESSAGE_MAIL.getKey());
        if (Objects.isNull(mailAddress)) {
            throw new DefaultServiceException("未配置邮件接收人地址");
        }
        String[] to = mailAddress.getValue().split(Global.SEPARATING_SYMBOL);
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage);
        LocalDate now = LocalDate.now();
        LocalDate yesterday = now.plusDays(-1);
        LocalDateTime startTime = yesterday.atStartOfDay().withHour(10);
        LocalDateTime endTime = now.atStartOfDay().withHour(10);
        List<ProductsVO> products = productsMapper.selectByStart(startTime, endTime);
        if (Objects.equals(0, products.size())) {
            return;
        }
        String all = number(products);
        if (StringUtils.isBlank(all)) {
            return;
        }

        String homeDepot = text(all);

        try {
            mimeMessageHelper.setTo(to);
            mimeMessageHelper.setFrom(Objects.requireNonNull(mailSender.getUsername()));
            mimeMessageHelper.setSubject("【" + now + " 商品上新报告" + "】" + "昨天又上新了" + products.size() + "件商品，快来瞧瞧！");
            mimeMessageHelper.setText("<html><body>各位<br/><br/>新品可能因尚未入库导致暂时无法在商城交易，具体上架时间以所在区域仓库是否到货为准。<br/><br/>" + homeDepot + "<br/><br/><br/>谢谢！</body></html>", true);
            //发送邮件
            mailSender.send(mimeMessage);

        } catch (MessagingException e) {
            logger.error(Global.collectExceptionStackMsg(e));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult purchaseAddProduct(ProductsSaveReq req) {

        //数据校验
        if (StringUtils.isBlank(req.getCreateRemark())) {
            return AjaxResult.getErrorWithMsg("请填写上新备注");
        }
        if (CollectionUtils.isEmpty(req.getSkuList())) {
            return AjaxResult.getErrorWithMsg("请添加sku信息");
        }
        if (StringUtils.isBlank(req.getSkuList().stream().map(Inventory::getCreateRemark))) {
            return AjaxResult.getErrorWithMsg("请填写上新备注");
        }
        Long tenantId = req.getTenantId();
        if (Objects.nonNull(tenantId) && tenantId > BaseConstant.XIANMU_TENANT_ID) {
            // 根据createType判断是否需要审核
            return createProduct(req, this.supportSpuAutoAudit(req));
        } else if(req.isAutoAudit()) {
            return createProduct(req, true);
        }else {
            return createProduct(req, false);
        }

    }

    private boolean supportSpuAutoAudit(ProductsSaveReq req) {
        boolean flag = false;
        List<InventoryVO> skuList = req.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            return flag;
        }
        // 有包含代仓则需要审核
        List<String> createTypeList = skuList.stream().map(InventoryVO::getCreateType)
                .filter(Objects::nonNull).collect(Collectors.toList());
        return !createTypeList.contains(CreateTypeEnums.SELF_AND_AGENT.getType().toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult operateAddProduct(ProductsSaveReq req) {
        //数据校验
        AjaxResult result = operateCheck(req);
        if (result != null) {
            return result;
        }

        //添加数据
        return createProduct(req, true);
    }

    @Override
    public void createProductReport(Integer hours) {
        Config dingUrl = configMapper.selectOne("create_product_audit");
        if (dingUrl == null) {
            throw new DefaultServiceException("商品上新审核报告机器人地址未配置");
        }
        //第一个人平台上新通知，第二个人大客户上新
//        Config operator = configMapper.selectOne("create_product_auditor");
//        String[] operatorArr = operator.getValue().split(",");
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusHours(hours);
        int normalCount = productsMapper.countUnAudit(ProductsEnum.CreateType.PLATFORM.ordinal(), start, end);
        if (normalCount > 0) {
            String title = "【商品上新】平台";
            StringBuilder text = new StringBuilder("#### " + title + "\n");
            text.append("#### 新增了").append(normalCount).append("条商品新增申请，请尽快处理。 ");
            try {
                DingTalkRobotUtil.sendMarkDownMsg(dingUrl.getValue(), () -> {
                    Map<String, String> md = new HashMap<>();
                    md.put("title", title);
                    md.put("text", text.toString());
                    return md;
                }, null);
            } catch (Exception e) {
                logger.error("【商品上新】平台 通知消息异常,:{},e:{}",JSON.toJSONString(text),e);
            }
        }

        int kaCount = productsMapper.countUnAudit(ProductsEnum.CreateType.KA.ordinal(), start, end);
        if (kaCount > 0) {
            String title = "【商品上新】大客户";
            StringBuilder text = new StringBuilder("#### " + title + "\n");
            text.append("#### 新增了").append(kaCount).append("条商品新增申请，请尽快处理。 ");
            try {
                DingTalkRobotUtil.sendMarkDownMsg(dingUrl.getValue(), () -> {
                    Map<String, String> md = new HashMap<>();
                    md.put("title", title);
                    md.put("text", text.toString());
                    return md;
                }, null);
            } catch (Exception e) {
                logger.error("【商品上新】大客户 通知消息异常,:{},e:{}",JSON.toJSONString(text),e);

            }
        }

        // 帆台代仓客户
        int agentCount = productsMapper.countUnAudit(ProductsEnum.CreateType.FAN_TAI_AGENT.ordinal(), start, end);
        if (agentCount > 0) {
            String title = "【商品上新】帆台代仓客户";
            StringBuilder text = new StringBuilder("#### " + title + "\n");
            text.append("#### 新增了").append(agentCount).append("条商品新增申请，请及时处理");
            try {
                DingTalkRobotUtil.sendMarkDownMsg(dingUrl.getValue(), () -> {
                    Map<String, String> md = new HashMap<>();
                    md.put("title", title);
                    md.put("text", text.toString());
                    return md;
                }, null);
            } catch (Exception e) {
                logger.error("【商品上新】帆台代仓客户 通知消息异常,:{},e:{}",JSON.toJSONString(text),e);
            }
        }
    }

    @Override
    public AjaxResult selectCreate(int pageIndex, int pageSize, ProductsCreateQuery query) {
        PageInfo<ProductsVO> pageInfo = PageInfoHelper.createPageInfo(pageIndex, pageSize, () -> productsMapper.selectCreate(query));
        pageInfo.getList().forEach(el -> {
            if (Objects.equals(-1, el.getOutdated()) && Objects.equals(0, el.getAuditStatus())) {
                el.setCreateStatus(0);
            } else if (Objects.equals(0, el.getOutdated()) && Objects.equals(1, el.getAuditStatus())) {
                el.setCreateStatus(1);
            } else {
                el.setCreateStatus(2);
            }
            el.setSubTypeList(Arrays.stream(el.getAllSubType().split(",")).map(Integer::parseInt).distinct().collect(Collectors.toList()));
        });

        return AjaxResult.getOK(pageInfo);
    }

    @Override
    public AjaxResult auditFail(ProductsVO productsVO) {
        Long pdId = productsVO.getPdId();
        Products products = productsMapper.selectByPrimaryKey(pdId);
        if (products == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        if (!ProductsEnum.Outdated.CREATING.getCode().equals(products.getOutdated())) {
            return AjaxResult.getErrorWithMsg("当前商品上新不可操作忽略");
        }

        //处理sku上新
        Inventory query = new Inventory();
        query.setPdId(pdId);
        query.setOutdated(ProductsEnum.Outdated.CREATING.getCode());
        query.setAuditStatus(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal());
        List<Inventory> auditSkuList = inventoryMapper.selectList(query);
        if (!CollectionUtils.isEmpty(auditSkuList)) {
            for (Inventory auditSku : auditSkuList) {
                auditSku.setRefuseReason(productsVO.getRefuseReason());
                auditSku.setAuditTime(LocalDateTime.now());
                auditSku.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
                logger.info("updateSpuAndSku Operator:{},:{}",getAdminOperator(),JSON.toJSONString(auditSku));
                inventoryMapper.update(auditSku);
            }
        }

        //更新spu
        Products update = new Products();
        update.setPdId(pdId);
        update.setAuditTime(LocalDateTime.now());
        update.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
        update.setAuditor(getAdminId());
        productsMapper.updateByPrimaryKeySelective(update);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult pdNameSearch(String pdName) {
        if (pdName == null) {
            return AjaxResult.getOK();
        }

        List<Products> products = productsMapper.selectSearchData();
        if (CollectionUtils.isEmpty(products)) {
            return AjaxResult.getOK();
        }

        List<Products> result = new ArrayList<>();
        pdName = pdName.trim();
        for (Products product : products) {
            if (product.getPdName().contains(pdName)) {
                result.add(product);
                if (result.size() > 20) {
                    break;
                }
            }
        }

        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult saasPdNameSearch(String pdName) {
        if (pdName == null) {
            return AjaxResult.getOK();
        }

        List<Products> products;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(SAAS_PRODUCTS_KEY + SaasThreadLocalUtil.getAdminId()))) {
            String productStr = (String) redisTemplate.opsForValue().get(SAAS_PRODUCTS_KEY + SaasThreadLocalUtil.getAdminId());
            products = JSONObject.parseArray(productStr, Products.class);
        } else {
            products = productsMapper.selectSaasData(SaasThreadLocalUtil.getAdminId());
            redisTemplate.opsForValue().set(SAAS_PRODUCTS_KEY + SaasThreadLocalUtil.getAdminId(), JSONObject.toJSONString(products), 30, TimeUnit.MINUTES);
        }
        if (products == null) {
            return AjaxResult.getOK();
        }

        List<Products> result = new ArrayList<>();
        pdName = pdName.trim();
        for (Products product : products) {
            if (product.getPdName().contains(pdName)) {
                result.add(product);
                if (result.size() > 20) {
                    break;
                }
            }
        }

        return AjaxResult.getOK(result);
    }

    @Override
    public List<Products> saasPdNameSearch(PdNameSearchParam param) {
        String pdName = param.getPdName();
        List<Integer> createTypeList = param.getCreateTypeList();
        if (pdName == null){
            return Lists.newArrayList();
        }
        String keySuffix = MapUtil.generateKey(createTypeList);

        List<Products> products;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(SAAS_PRODUCTS_KEY + SaasThreadLocalUtil.getAdminId() + "-" + keySuffix))){
            String productStr = (String) redisTemplate.opsForValue().get(SAAS_PRODUCTS_KEY + SaasThreadLocalUtil.getAdminId() + "-" + keySuffix);
            products = JSONObject.parseArray(productStr, Products.class);
        } else {
            products = productsMapper.selectSaasDataByCreateTypeList(SaasThreadLocalUtil.getAdminId(), createTypeList);
            redisTemplate.opsForValue().set(SAAS_PRODUCTS_KEY + SaasThreadLocalUtil.getAdminId() + "-" + keySuffix, JSONObject.toJSONString(products), 30, TimeUnit.MINUTES);
        }
        if (products == null){
            return Lists.newArrayList();
        }

        List<Products> result = new ArrayList<>();
        pdName = pdName.trim();
        for (Products product : products) {
            if(product.getPdName().contains(pdName)){
                result.add(product);
                if(result.size() > 20){
                    break;
                }
            }
        }

        return result;
    }

    /**
     * 新建spu、sku
     *
     * @param req       spu、sku
     * @param autoAudit 自动审核标识
     * @return 创建结果
     */
    private AjaxResult createProduct(ProductsSaveReq req, boolean autoAudit) {

        logger.info("createProduct autoAudit :{},req:{}" ,autoAudit,JSON.toJSONString(req));
        //参数校验
        AjaxResult result = commonCheck(req);
        if (result != null) {
            return result;
        }

        //校验sku信息
        List<InventoryVO> skuList = req.getSkuList();
        for (InventoryVO sku : skuList) {
            if (StringUtils.isBlank(sku.getUnit())) {
                return AjaxResult.getErrorWithMsg("请填写SKU包装");
            }
            if (sku.getType() == null) {
                return AjaxResult.getErrorWithMsg("请填写SKU性质");
            }
            if (StringUtils.isBlank(sku.getVolume())) {
                return AjaxResult.getErrorWithMsg("请填写SKU体积");
            }
            if (StringUtils.isBlank(sku.getWeightNum())) {
                return AjaxResult.getErrorWithMsg("请填写SKU重量");
            }
        }
        // pop类型商品信息校验
        inventoryValidator.checkPopBuyer(skuList);
        inventoryValidator.checkNetWeight(skuList);

        //插入信息
//        String pdNo = createPdNo(req.getCategoryId());
        String pdNo = req.getPdNo();
        if (StringUtils.isEmpty(pdNo)) {
            pdNo = goodsFacade.takeSkuCode(GoodsCodeInputReq.builder()
                    .categoryId(Long.valueOf(req.getCategoryId())).onlyTakeSpu(true).build()).getSpu();
        }
        req.setPdNo(pdNo);
        req.setCreator(Objects.isNull(req.getCreator()) ? getAdminId() : req.getCreator());
        Long tenantId = req.getTenantId();
        if (autoAudit) {
            req.setOutdated(ProductsEnum.Outdated.VALID.getCode());
            req.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
        } else {
            req.setAuditStatus(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal());
            req.setOutdated(ProductsEnum.Outdated.CREATING.getCode());
        }
        productsMapper.insertSelective(req);
        if (Objects.equals(req.getOutdated(), ProductsEnum.Outdated.VALID.getCode())) {
            // 上新成功插入负责人配置
            logger.info("上新成功插入负责人配置");
            insertResponsible(req.getPdId());
        }

        //插入关键属性值
        productsPropertyService.addKeyPropertyValue(req.getPdId(), req.getKeyValueList());


        //插入销售属性映射
        productsPropertyService.addSalePropertyMapping(req.getPdId(), req.getSalePropertyList());

        //判断sku税率相关插入or更新
        TaxRateConfig taxRateConfig = new TaxRateConfig();
        taxRateConfig.setTaxRateCode(req.getTaxRateCode());
        taxRateConfig.setTaxRateValue(req.getTaxRateValue());
        taxRateConfig.setPdId(req.getPdId());
        taxRateService.save(taxRateConfig);

        HashMap<String, String> outerMappingMap = new HashMap<>(10);
        List<String> createSkuList = Lists.newArrayList();
        for (InventoryVO sku : skuList) {
//            String skuNo = createSkuNo(pdNo);
            if (StringUtils.isEmpty(sku.getSkuCode())) {
                sku.setSku(goodsFacade.takeSkuCode(GoodsCodeInputReq.builder()
                        .spu(pdNo).build()).getSku());
            }else {
                sku.setSku(sku.getSkuCode());
            }
            String skuNo = sku.getSku();
            sku.setPdId(req.getPdId());
            sku.setCreator(Objects.isNull(sku.getCreator()) ? getAdminId() : sku.getCreator());
            sku.setTaskType(0);
            // 自营品不需要审核
            if (this.supportSkuAutoAudit(sku.getCreateType())) {
                sku.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                sku.setOutdated(ProductsEnum.Outdated.VALID.getCode());
            } else if(autoAudit) {
                sku.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                sku.setOutdated(ProductsEnum.Outdated.VALID.getCode());
            } else {
                sku.setAuditStatus(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal());
                sku.setOutdated(ProductsEnum.Outdated.CREATING.getCode());
            }
            AjaxResult ajaxResult = SpringUtil.getBean(InventoryService.class).checkSubType(sku.getSubType());
            if (!ajaxResult.isSuccess()) {
                throw new BizException(ajaxResult.getMsg());
            }
            //插入sku信息
            sku.setVideoInfo(sku.getVideoUrl (),getAdminName ());
            inventoryMapper.insertSelective(sku);
            //插入标签信息
            productLabelValueService.correctedData(sku.getProductLabelValueVos(), skuNo);

            //插入销售属性
            productsPropertyService.addSalePropertyValue(skuNo, sku.getSaleValueList());

            //添加默认库存信息
            // areaStoreMapper.initAfterCreateSku(skuNo);
            // areaStoreMapper.initAfterCreateSaasSku(skuNo, sku.getTenantId());
            if (Objects.nonNull(tenantId) && tenantId > BaseConstant.XIANMU_TENANT_ID) {
                if (CreateTypeEnums.SELF.getType().toString().equals(sku.getCreateType())) {
                    areaStoreMapper.initAfterCreateSaasSelfSku(skuNo, sku.getTenantId());
                } else if (CreateTypeEnums.SELF_AND_AGENT.getType().toString().equals(sku.getCreateType())) {
                    areaStoreMapper.initAfterCreateSaasSelfSku(skuNo, sku.getTenantId());
                    areaStoreMapper.initAfterApplySaasAgentSku(skuNo, BaseConstant.XIANMU_TENANT_ID);
                } else {
                    return AjaxResult.getError(String.format("saas自营货品不支持的crateType:%s", sku.getCreateType()));
                }
            } else {
                // 鲜沐初始化
                areaStoreMapper.initAfterApplySaasAgentSku(skuNo, BaseConstant.XIANMU_TENANT_ID);
            }
            warehouseStockExtService.initWarehouseStockExt(skuNo);

            //装载id返回
            outerMappingMap.put(Global.APPLICATION_ITEM_KEY + sku.getOuterApplicationItemId(), String.valueOf(sku.getInvId()));
            outerMappingMap.put(Global.APPLICATION_ITEMCODE_KEY + sku.getOuterApplicationItemId(), skuNo);
            createSkuList.add(skuNo);
        }
        outerMappingMap.put(Global.APPLICATION_KEY + req.getOuterApplicationId(), String.valueOf(req.getPdId()));
        outerMappingMap.put(Global.APPLICATION_SPU_CODE + req.getOuterApplicationId(), pdNo);
        // 对外返回商品保存结果对象
        ProductSaveResultBO productSaveResultBo = new ProductSaveResultBO();
        productSaveResultBo.setOuterApplicationMap(outerMappingMap);
        productSaveResultBo.setPdId(req.getPdId());
        productSaveResultBo.setPdNo(pdNo);
        productSaveResultBo.setSkuList(createSkuList);
        return AjaxResult.getOK(productSaveResultBo);
    }

    private boolean supportSkuAutoAudit(String createType) {
        return CreateTypeEnums.SELF.getType().toString().equals(createType);
    }

    private String number(List<ProductsVO> products) {
        String countTxt = "";
        // 获取已上架的运营区域
        AreaSkuQuery areaSkuQuery = new AreaSkuQuery();
        areaSkuQuery.setOnSale(Boolean.TRUE);

        for (ProductsVO product : products) {

            // 查询sku信息
            List<InventoryDetailVO> voList = productsMapper.selectInventoryByPdId(product.getPdId());
            List<String> skuList = voList.stream().map(InventoryDetailVO::getSku).collect(Collectors.toList());
            areaSkuQuery.setSkuList(skuList);
            List<AreaSkuVO> areaSkuVOList = areaSkuMapper.selectByKeys(areaSkuQuery);
            if (CollectionUtil.isEmpty(areaSkuVOList)) {
                continue;
            }

            // 查询存在的库存仓
            StringBuilder warehouseNameStr = new StringBuilder("");
            for (AreaSkuVO areaSkuVO : areaSkuVOList) {
                WarehouseStorageCenter warehouseStorageCenter = warehouseStorageCenterMapper.selectByWarehouseNo(areaSkuVO.getParentNo());
                warehouseStorageCenter = Optional.ofNullable(warehouseStorageCenter).orElse(new WarehouseStorageCenter());
                warehouseNameStr.append(warehouseStorageCenter.getWarehouseName())
                        .append(".");
            }

            Integer categoryId = product.getCategoryId();
            if (StringUtils.isEmpty(product.getPddetail())) {
                product.setPddetail("-");
            }
            if (StringUtils.isEmpty(product.getSlogan())) {
                product.setSlogan("-");
            }
            if (StringUtils.isEmpty(product.getProductIntroduction())) {
                product.setProductIntroduction("-");
            }
            Map<String, String> category = categoryService.getThreeLevelCategory(categoryId);
            countTxt = countTxt +
                    "<tr style=\"color:#4F4F4F\" height = \"30\">\n" +
                    "        <td style=\"padding-left:6px;width:125px\">" + category.get("oneLevel") + "/" + category.get("twoLevel") + "/" + category.get("threeLevel") + "</td>" +
                    "        <td style=\"padding-left:6px;width:125px\">" + product.getPdId() + "</td>" +
                    "        <td style=\"padding-left:6px;width:125px\">" + product.getPdName() + "</td>" +
                    "        <td style=\"padding-left:6px;width:125px\">" + getPicturePath(product) + "</td>" +
                    "        <td style=\"padding-left:6px;width:125px\">" + product.getCreateAdmin() + "</td>" +
                    "        <td style=\"padding-left:6px;width:125px\">" + product.getProductIntroduction() + "</td>" +
                    "        <td style=\"padding-left:6px;width:125px\">" + warehouseNameStr + "</td>" +
                    "</tr>\n";
        }

        return countTxt;
    }

    @NotNull
    private String getPicturePath(Products product) {
        if (StringUtils.isEmpty(product.getPicturePath())) {
            return "-";
        }
        return "<img src=https://azure." + Global.TOP_DOMAIN_NAME + "/" + product.getPicturePath() + " height=95px width=95px>";
    }

    /**
     * 根据类目生成模板
     *
     * @return
     */
    private Workbook generateTemplate(Integer type) {
        Workbook workbook = new HSSFWorkbook();

        //设置全局默认文本格式
        CellStyle bookStyle = workbook.createCellStyle();
        bookStyle.setAlignment(HorizontalAlignment.CENTER);
        bookStyle.setDataFormat(workbook.createDataFormat().getFormat("@"));


        //sheet名称
        Sheet sheet = workbook.createSheet();

        //关键属性数
        int keyPropertySum = 8;
        //统一设置样式
        for (int i = 0; i < FIXED_LEN + keyPropertySum; i++) {
            sheet.setDefaultColumnStyle(i, bookStyle);
            sheet.setColumnWidth(i, 10 * 256);
        }
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(2, 16 * 256);
        sheet.setColumnWidth(4, 16 * 256);
        sheet.setColumnWidth(5, 16 * 256);
        sheet.setColumnWidth(6, 28 * 256);
        sheet.setColumnWidth(7, 22 * 256);
        sheet.setColumnWidth(14, 16 * 256);
        sheet.setColumnWidth(15, 16 * 256);
        sheet.setColumnWidth(16, 14 * 256);
        sheet.setColumnWidth(FIXED_LEN + keyPropertySum - 18, 18 * 256);
        sheet.setColumnWidth(FIXED_LEN + keyPropertySum - 17, 20 * 256);
        sheet.setColumnWidth(FIXED_LEN + keyPropertySum - 16, 20 * 256);
        sheet.setColumnWidth(FIXED_LEN + keyPropertySum - 12, 16 * 256);
        sheet.setColumnWidth(FIXED_LEN + keyPropertySum - 11, 14 * 256);
        sheet.setColumnWidth(FIXED_LEN + keyPropertySum - 10, 30 * 256);
        sheet.setColumnWidth(FIXED_LEN + keyPropertySum - 9, 28 * 256);

        //表头信息
        Map<String, List<String>> title = new LinkedHashMap<>();
        title.put("所属类目", Arrays.asList("三级类目"));
        title.put("SPU-基础信息", Arrays.asList("SPU编码", "商品名称（必填）", "副标题", "实物名称（必填）", "贮存区域（必填）", "保质期时长（月/天）（必填）", "到期预警x天（选填）"));
        title.put("SPU-关键属性", Arrays.asList("产地", "品牌", "储藏温度", "面筋含量", "品种", "熟度", "每100g含蛋白质", "每100g乳脂含量"));
        title.put("SPU-图文描述", Arrays.asList("其它介绍"));
        if (Objects.equals(0, type)) {
            title.put("SKU-基础信息", Arrays.asList("SKU编码", "性质（自营-代销不入仓、 自营-代销入仓、 自营-经销、 代仓-代仓）", "代仓所属", "类型（常规/拆包/不卖）", "展示平均值（是/否）", "最小起售量", "起售规格", "包装(必填)", "体积（长*宽*高）", "重量（千克）"));
        } else {
            title.put("SKU-基础信息", Arrays.asList("SKU编码", "性质（自营-代销不入仓、 自营-代销入仓、 自营-经销、 代仓-代仓）", "代仓所属", "类型（常规/临保/拆包/不卖/破袋）", "展示平均值（是/否）", "最小起售量", "起售规格", "包装(必填)", "体积（长*宽*高）", "重量（千克）"));
        }

        title.put("SKU-销售属性", Arrays.asList("规格-区间（净重/毛重+最低重量）", "规格-区间2（最高重量+单位）", "规格-容量", "规格-数量", "果规", "尺寸", "级别", "口味"));
        title.put("SKU-售后信息", Arrays.asList("售后单位", "最大售后量"));

        //生成表格
        int lastIndex = 0;
        Row t1 = sheet.createRow(0);
        Row t2 = sheet.createRow(1);


        sheet.createFreezePane(0, 1);
        sheet.createFreezePane(0, 2);
        for (Map.Entry<String, List<String>> entry : title.entrySet()) {
            CellRangeAddress region;
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                if (entry.getValue().size() < 2) {
                    Cell c1 = t1.createCell(lastIndex);
                    c1.setCellValue(entry.getKey());

                    for (String str : entry.getValue()) {
                        Cell c2 = t2.createCell(lastIndex++);
                        c2.setCellValue(str);
                    }
                    continue;
                }
                region = new CellRangeAddress(0, 0, lastIndex, lastIndex + entry.getValue().size() - 1);

                sheet.addMergedRegion(region);
            }

            Cell c1 = t1.createCell(lastIndex);
            c1.setCellValue(entry.getKey());

            for (String str : entry.getValue()) {
                Cell c2 = t2.createCell(lastIndex++);
                c2.setCellValue(str);
            }
        }

        return workbook;
    }

    /**
     * 去除销售属性完全相同的sku
     *
     * @param pdId       pdId
     * @param addSkuList skuList
     * @return
     */
    private List<InventoryVO> removeDuplicate(Long pdId, List<InventoryVO> addSkuList) {
        List<Inventory> oldSkuList = inventoryMapper.selectByPdId(pdId.intValue());
        Set<String> existSet = oldSkuList.stream()
                .filter(el -> Objects.equals(false, el.getOutdated()))
                .map(inventory -> productsPropertyService.selectSaleValueBySku(inventory.getSku()))
                .map(this::saleValue2MD5)
                .collect(Collectors.toSet());

        return addSkuList.stream()
                .filter(el -> {
                    String valueMD5 = saleValue2MD5(el.getSaleValueList());
                    if (existSet.contains(valueMD5)) {
                        return false;
                    }

                    existSet.add(valueMD5);
                    return true;
                }).collect(Collectors.toList());
    }


    private String saleValue2MD5(List<ProductsPropertyValueVO> voList) {
        Object[] keyStringArr = voList.stream()
                .sorted(Comparator.comparing(ProductsPropertyValueVO::getProductsPropertyId))
                .map(el -> el.getProductsPropertyId() + ":" + el.getProductsPropertyValue())
                .toArray();
        return MD5Util.string2MD5(ArrayUtil.join(keyStringArr, "&"));
    }

    private String text(String tx) {

        return "<table border=\"1\" cellspacing=\"0\" bordercolor=\"#E0E0E0\" width=\"0\" height = \"50\" >\n" +
                "    <tr bgcolor=\"#3C3C3C\" style=\"color:#FFFF80\" >\n" +
                "    <td style=\"padding-left:6px;width:125px;\" > 所属类目</td>\n" +
                "    <td  style=\"padding-left:6px;width:125px\">SPU编码</td>\n" +
                "    <td  style=\"padding-left:6px;width:125px\"> 商品名称</td>\n" +
                "    <td  style=\"padding-left:6px;width:125px\"> 商品图片</td>\n" +
                "    <td  style=\"padding-left:6px;width:125px\"> 采购负责人</td>\n" +
                "    <td  style=\"padding-left:6px;width:125px\"> 商品介绍</td>\n" +
                "    <td  style=\"padding-left:6px;width:125px\"> 库存仓</td>\n" +
                "    </tr>\n" + tx + "</table> ";
    }


    /**
     * 生成es json数据
     *
     * @param products 必须包含 pdId、pdNo、pdName
     * @return
     */
    private String createEsProductJson(Products products) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pd_id", products.getPdId());
        jsonObject.put("pd_no", products.getPdNo());
        jsonObject.put("pd_name", products.getPdName());

        //商品名称括号内容
        if (products.getPdName().matches(EXT_PATTERN.pattern())) {
            String[] ext = EXT_PATTERN.split(products.getPdName());
            jsonObject.put("pd_name_ext", ext);
            String pure = EXT_PATTERN.matcher(products.getPdName()).replaceAll("");
            jsonObject.put("pd_name_pure", pure);
        } else {
            jsonObject.put("pd_name_pure", products.getPdName());
        }

        //商品名称关键名次提取
        jsonObject.put("pd_name_key", HanLP.extractKeyword(products.getPdName(), 1));

        //关键属性
        List<ProductsPropertyValueVO> valueList = productsPropertyService.selectKeyValueByPdId(products.getPdId());
        if (!CollectionUtils.isEmpty(valueList)) {
            List<String> propertyArr = valueList
                    .stream()
                    .filter(el -> {
                        if (Objects.equals(2, el.getProductsPropertyId())) {
                            jsonObject.put("brand", el.getProductsPropertyValue());
                            return false;
                        }
                        return true;
                    })
                    .map(ProductsPropertyValue::getProductsPropertyValue)
                    .collect(Collectors.toList());
            jsonObject.put("property_value", propertyArr);
        }

        //前台类目
        List<EsFrontCategoryVO> esList = frontCategoryToCategoryMapper.selectFrontCategory(products.getPdId());
        if (!CollectionUtils.isEmpty(esList)) {
            List<Map<String, String>> voList = esList.stream().map(et -> {
                Map<String, String> map = new HashMap<>(2);
                map.put("name", et.getName());
                map.put("parent_name", et.getParentName());
                return map;
            }).collect(Collectors.toList());
            jsonObject.put("category", voList);
        }
        return jsonObject.toJSONString();
    }

    //商品导入校验
    private static List<String> SALE_PROPERTY_MAPPING_LIST = null;
    private static Map<Integer, List<Function<String, ExcelErrorCode>>> SPU_ADD_CHECK;
    private static Map<Integer, List<Function<String, ExcelErrorCode>>> SKU_ADD_CHECK;
    private static Map<Integer, List<Function<String, ExcelErrorCode>>> SPU_UPDATE_CHECK;
    private static Map<Integer, List<Function<String, ExcelErrorCode>>> SKU_UPDATE_CHECK;

    private void importCheckFunInstance(Map<Integer, List<Integer>> indexMap) {
        SPU_ADD_CHECK = new HashMap<>();
        SKU_ADD_CHECK = new HashMap<>();
        SPU_UPDATE_CHECK = new HashMap<>();
        SKU_UPDATE_CHECK = new HashMap<>();

        Function<String, ExcelErrorCode> require = o -> {
            if (StringUtils.isBlank(o)) {
                return UN_FILL;
            }
            return null;
        };
        Function<String, ExcelErrorCode> empty = o -> {
            if (StringUtils.isNotBlank(o)) {
                return ExcelErrorCode.MORE_FILL;
            }
            return null;
        };
        Function<String, ExcelErrorCode> isInt = o -> {
            if (StringUtils.isNotBlank(o) && !StringUtils.isInteger(o)) {
                return ExcelErrorCode.FORMAT_ERROR;
            }
            return null;
        };
        Function<String, ExcelErrorCode> isNum = o -> {
            if (StringUtils.isNotBlank(o) && !com.alibaba.druid.util.StringUtils.isNumber(o)) {
                return ExcelErrorCode.FORMAT_ERROR;
            }
            return null;
        };
        Function<Integer, Function<String, ExcelErrorCode>> strLen = o -> (Function<String, ExcelErrorCode>) s -> {
            if (StringUtils.isNotBlank(s) && s.length() > o) {
                return ExcelErrorCode.OTHER;
            }
            return null;
        };
        Function<List<String>, Function<String, ExcelErrorCode>> strRange = list -> (Function<String, ExcelErrorCode>) o -> {
            if (StringUtils.isNotBlank(o) && !list.contains(o)) {
                return INFO_ERROR;
            }
            return null;
        };
        Function<String, Function<String, ExcelErrorCode>> saleMapping = name -> (Function<String, ExcelErrorCode>) o -> {
            if (StringUtils.isNotBlank(o) && SALE_PROPERTY_MAPPING_LIST != null && !SALE_PROPERTY_MAPPING_LIST.contains(name)) {
                return ExcelErrorCode.MORE_FILL;
            }
            return null;
        };
        /**
         * 新增spu校验
         */
        {
            //类目必填
            addCheckFunction(SPU_ADD_CHECK, 0, require);
            //spu编码不填
            addCheckFunction(SPU_ADD_CHECK, 1, empty);
            //商品名称必填
            addCheckFunction(SPU_ADD_CHECK, 2, require);
            //商品介绍：非必填、30字(副标题)
            addCheckFunction(SPU_ADD_CHECK, 3, o -> strLen.apply(30).apply(o));
            //实物名称必填
            addCheckFunction(SPU_ADD_CHECK, 4, require);
            //贮存区域必填、取值："冷冻", "冷藏", "常温"
            addCheckFunction(SPU_ADD_CHECK, 5, require);
            addCheckFunction(SPU_ADD_CHECK, 5, o -> strRange.apply(Arrays.asList("冷冻", "冷藏", "常温")).apply(o));

            //保质期时长必填，形如：xx天、xx月
            addCheckFunction(SPU_ADD_CHECK, 6, require);
            addCheckFunction(SPU_ADD_CHECK, 6, o -> {
                if (!(o.endsWith("月") || o.endsWith("天"))
                        || !com.alibaba.druid.util.StringUtils.isNumber(o.replace("天", "").replace("月", ""))) {
                    return ExcelErrorCode.FORMAT_ERROR;
                }
                return null;
            });
            //到期预警选填，数字格式
            addCheckFunction(SPU_ADD_CHECK, 7, isInt);
            //关键属性校验：必填
            Iterator<Map.Entry<Integer, List<Integer>>> iterator = indexMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, List<Integer>> entry = iterator.next();
                entry.getValue().forEach(value -> {
                    addCheckFunction(SPU_ADD_CHECK, value, require);
                });
            }

        }

        /**
         * 修改spu校验
         */
        {
            //spu编码非必填、校验库里是否存在
//            addCheckFunction(SPU_UPDATE_CHECK, 1, require);
            addCheckFunction(SPU_UPDATE_CHECK, 1, o -> {
                if (StringUtils.isNotBlank(o)) {
                    Long pdId = productsMapper.selectPdIdByPdNo(o);
                    if (pdId == null) {
                        return INFO_ERROR;
                    }
                }
                return null;
            });
//            //商品名称必填
//            addCheckFunction(SPU_UPDATE_CHECK, 2, require);
            //商品介绍：非必填、30字(副标题)
            addCheckFunction(SPU_UPDATE_CHECK, 3, o -> strLen.apply(30).apply(o));
            //贮存区域非必填、取值："冷冻", "冷藏", "常温"
            addCheckFunction(SPU_UPDATE_CHECK, 5, o -> strRange.apply(Arrays.asList("冷冻", "冷藏", "常温")).apply(o));
            //保质期时长非必填、限制填写格式
            addCheckFunction(SPU_UPDATE_CHECK, 6, o -> {
                if (StringUtils.isNotBlank(o)) {
                    if (!(o.endsWith("月") || o.endsWith("天"))
                            || !com.alibaba.druid.util.StringUtils.isNumber(o.replace("天", "").replace("月", ""))) {
                        return ExcelErrorCode.FORMAT_ERROR;
                    }
                }
                return null;
            });
            //到期预警天数非必填、限制填写数字
            addCheckFunction(SPU_UPDATE_CHECK, 7, isInt);
        }

        /**
         * 新增sku校验
         */
        {
            //SKU为空
            addCheckFunction(SKU_ADD_CHECK, 17, empty);
            //性质：必填、"自营"
            addCheckFunction(SKU_ADD_CHECK, 18, require);
//            addCheckFunction(SKU_ADD_CHECK, 18, o -> strRange.apply(Collections.singletonList("自营")).apply(o));
            addCheckFunction(SKU_ADD_CHECK, 18, o -> strRange.apply(Arrays.asList("自营-代销不入仓", "自营-代销入仓", "自营-经销", "代仓-代仓")).apply(o));

            //类型：“临保”、“常规”、"拆包","不卖","破袋"
            addCheckFunction(SKU_ADD_CHECK, 20, o -> strRange.apply(Arrays.asList("常规", "拆包", "不卖")).apply(o));
            //进口国产
            addCheckFunction(SKU_ADD_CHECK, 21, o -> strRange.apply(Arrays.asList("进口", "国产")).apply(o));
            //展示平均值：必填、是否
            addCheckFunction(SKU_ADD_CHECK, 22, require);
            addCheckFunction(SKU_ADD_CHECK, 22, o -> strRange.apply(Arrays.asList("是", "否")).apply(o));
            //最小起售量：必填、数字
            addCheckFunction(SKU_ADD_CHECK, 23, require);
            addCheckFunction(SKU_ADD_CHECK, 23, isInt);
            //起售规格：必填数字
            addCheckFunction(SKU_ADD_CHECK, 24, require);
            addCheckFunction(SKU_ADD_CHECK, 24, isInt);
            //包装：必填
            addCheckFunction(SKU_ADD_CHECK, 25, require);
            //体积（长*宽*高）：必填
            addCheckFunction(SKU_ADD_CHECK, 26, require);
            addCheckFunction(SKU_ADD_CHECK, 26, o -> {
                if (StringUtils.isNotBlank(o)) {
                    String character = "*";
                    int count;
                    int origialLength = o.length();
                    o = o.replace(character, "");
                    int newLength = o.length();

                    count = origialLength - newLength;
                    if (count != 2) {
                        return ExcelErrorCode.FORMAT_ERROR;
                    }

                }
                return null;
            });
            //重量：非必填、数字
            addCheckFunction(SKU_ADD_CHECK, 27, isNum);
            //规格-区间（净重/毛重+最低重量）：区间与容量数量选其一填写
            addCheckFunction(SKU_ADD_CHECK, 28, o -> {
                if (StringUtils.isNotBlank(o)) {
                    if (!o.startsWith("净重") && !o.startsWith("毛重")) {
                        return ExcelErrorCode.FORMAT_ERROR;
                    }
                }
                return null;
            });
            //规格-区间2（最高重量+单位）:区间与容量数量选其一填写
            addCheckFunction(SKU_ADD_CHECK, 29, o -> {
                if (StringUtils.isNotBlank(o)) {
                    String unit = o.replaceAll("\\d+", "");
                    if (StringUtils.isBlank(unit)) {
                        strRange.apply(Arrays.asList("G", "KG", "斤")).apply(unit);
                        return ExcelErrorCode.FORMAT_ERROR;
                    }
                }
                return null;
            });
            //级别
            addCheckFunction(SKU_ADD_CHECK, 34, o -> strRange.apply(Arrays.asList("一级", "二级", "三级", "精品", "普通", "其他")).apply(o));

            //最大售后量
            addCheckFunction(SKU_ADD_CHECK, 37, isInt);
        }

        /**
         * 修改sku校验
         */
        {
            //sku编码必填
            addCheckFunction(SKU_UPDATE_CHECK, 17, require);
            addCheckFunction(SKU_UPDATE_CHECK, 17, o -> {
                int count = inventoryMapper.count(Collections.singletonMap("sku", o));
                if (count == 0) {
                    return INFO_ERROR;
                }
                Inventory query = new Inventory();
                query.setSku(o);
                Inventory inventory = inventoryMapper.selectOne(query);
                SALE_PROPERTY_MAPPING_LIST = productsPropertyService.selectSalePropertyByPdId(inventory.getPdId()).stream().map(ProductsProperty::getName).collect(Collectors.toList());
                return null;
            });
            //性质：非必填、自营
            addCheckFunction(SKU_UPDATE_CHECK, 18, o -> strRange.apply(Arrays.asList("自营-代销不入仓", "自营-代销入仓", "自营-经销", "代仓-代仓）")).apply(o));

            //类型：“临保”、“常规”、“拆包”、“不卖”、“破袋”
            addCheckFunction(SKU_UPDATE_CHECK, 20, o -> strRange.apply(Arrays.asList("临保", "常规", "拆包", "不卖", "破袋")).apply(o));
            //进口/国产
            addCheckFunction(SKU_ADD_CHECK, 21, o -> strRange.apply(Arrays.asList("进口", "国产")).apply(o));
            //展示平均值：非必填、是否
            addCheckFunction(SKU_UPDATE_CHECK, 22, o -> strRange.apply(Arrays.asList("是", "否")).apply(o));
            //最小起售量：非必填、数字
            addCheckFunction(SKU_UPDATE_CHECK, 23, isInt);
            //起售规格：非必填、数字
            addCheckFunction(SKU_UPDATE_CHECK, 24, isInt);
            //体积（长*宽*高）：非必填
            addCheckFunction(SKU_UPDATE_CHECK, 26, o -> {
                if (StringUtils.isNotBlank(o)) {
                    String character = "*";
                    int count;
                    int origialLength = o.length();
                    o = o.replace(character, "");
                    int newLength = o.length();

                    count = origialLength - newLength;
                    if (count != 2) {
                        return ExcelErrorCode.FORMAT_ERROR;
                    }
                }
                return null;
            });
            //重量：非必填、数字
            addCheckFunction(SKU_UPDATE_CHECK, 27, isNum);
            //规格-区间（净重/毛重+最低重量）：区间与容量数量选其一填写
            addCheckFunction(SKU_UPDATE_CHECK, 28, o -> {
                if (StringUtils.isNotBlank(o)) {
                    if (!o.startsWith("净重") && !o.startsWith("毛重")) {
                        return ExcelErrorCode.FORMAT_ERROR;
                    }
                }
                return null;
            });
            //规格-区间2（最高重量+单位）:区间与容量数量选其一填写
            addCheckFunction(SKU_UPDATE_CHECK, 29, o -> {
                if (StringUtils.isNotBlank(o)) {
                    String unit = o.replaceAll("\\d+", "");
                    if (StringUtils.isBlank(unit)) {
                        strRange.apply(Arrays.asList("G", "KG", "斤")).apply(unit);
                        return ExcelErrorCode.FORMAT_ERROR;
                    }
                }
                return null;
            });
            //规格-容量
            addCheckFunction(SKU_UPDATE_CHECK, 30, o -> saleMapping.apply("规格").apply(o));
            //规格-数量
            addCheckFunction(SKU_UPDATE_CHECK, 31, o -> saleMapping.apply("规格").apply(o));
            //级别：非必填
            addCheckFunction(SKU_UPDATE_CHECK, 34, o -> saleMapping.apply("级别").apply(o));
            addCheckFunction(SKU_UPDATE_CHECK, 34, o -> strRange.apply(Arrays.asList("一级", "二级", "三级", "精品", "普通", "其他")).apply(o));
            //口味
            addCheckFunction(SKU_UPDATE_CHECK, 35, o -> saleMapping.apply("口味").apply(o));
            //最大售后量
            addCheckFunction(SKU_UPDATE_CHECK, 37, isInt);
        }
    }

    private void addCheckFunction(Map<Integer, List<Function<String, ExcelErrorCode>>> map, Integer key, Function<String, ExcelErrorCode> value) {
        if (!map.containsKey(key)) {
            map.put(key, new ArrayList<>());
        }
        map.get(key).add(value);
    }

    /**
     * @param categoryId 类目id
     * @return pdNo
     * 货品中心收口生成逻辑
     */
    private String createPdNo(Integer categoryId) {
        String pdNo = null;
        int repeat = 1;
        while (repeat > 0) {
            pdNo = categoryId + StringUtils.getRandomNumber(6);
            repeat = productsMapper.count(Integer.valueOf(pdNo));
        }
        return pdNo;
    }


    /**
     * @param pdNo pdNo
     * @return sku编号
     * 生成规则由货品中心收口
     */
    private String createSkuNo(String pdNo) {
        String sku = pdNo + StringUtils.getRandomNumber(3);
        //sku编号查重
        Map countKey = new HashMap();
        int repeat = 1;
        while (repeat > 0) {
            sku = pdNo + StringUtils.getRandomNumber(3);
            countKey.put("sku", sku);
            repeat = inventoryMapper.count(countKey);
        }
        return sku;
    }

    /**
     * 发送审核通知消息
     *
     * @param auditFlag   审核标识
     * @param auditor     审核人
     * @param productsReq spu信息
     */
    private void sendAuditMsg(boolean auditFlag, String auditor, Products productsReq) {
        EXECUTOR_SERVICE.execute(() -> {

            Integer createAdminId = productsReq.getCreator();
            if (createAdminId == null) {
                return;
            }
            /*AdminAuthExtend authExtend = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), createAdminId);
            if (authExtend == null) {
                logger.warn("上新审核结果通知失败，无钉钉授权信息，adminId：{}", createAdminId);
                return;
            }*/

            String title = "【商品上新结果】";
            String content = "###### " + title + "\n" +
                    "> ###### ：" + (auditFlag ? "恭喜！" : "抱歉！") + "您提交的 " + productsReq.getRealName()
                    + (auditFlag ? " 上新完成；" : (" 上新失败；请联系 @" + auditor + " 处理"))+ "\n" +
                    "> ###### 商品名称：" + productsReq.getPdName() + "\n" +
                    "> ###### 实物名称：" + productsReq.getRealName() + "\n" +
                    "> ###### SPU编码：" + productsReq.getPdNo() + "\n" +
                    "> ###### 处理人：" + auditor + "\n";
            
            DingTalkMsgReceiverIdBO dingTalkMsgReceiverIdBO = new DingTalkMsgReceiverIdBO();
            dingTalkMsgReceiverIdBO.setReceiverIdList(Collections.singletonList(createAdminId.longValue()));
            dingTalkMsgReceiverIdBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
            dingTalkMsgReceiverIdBO.setTitle(title);
            dingTalkMsgReceiverIdBO.setText(content.toString());
            try {
                dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgReceiverIdBO);
            } catch (Exception e) {
                logger.info("【商品上新结果】发送飞书消息失败,客户名:{},处理人:{},e:{}",auditor,JSON.toJSONString(dingTalkMsgReceiverIdBO),e);
            }
        });
    }

    /**
     * 运营必填项校验
     *
     * @param req
     * @return
     */
    public AjaxResult operateCheck(ProductsSaveReq req) {
        if (StringUtils.isBlank(req.getPdName())) {
            return AjaxResult.getErrorWithMsg("请填写商品名称");
        }
        if (req.getWarnTime() == null) {
            return AjaxResult.getErrorWithMsg("请填写到期预警");
        }
//        if (StringUtils.isBlank(req.getPicturePath())) {
//            return AjaxResult.getErrorWithMsg("请上传橱窗图");
//        }
//        if (StringUtils.isBlank(req.getDetailPicture())) {
//            return AjaxResult.getErrorWithMsg("请上传详情图");
//        }
        if (StringUtils.isBlank(req.getAfterSaleTime(), req.getAfterSaleType(), req.getRefundType())) {
            return AjaxResult.getErrorWithMsg("请填写售后信息");
        }
        if (CollectionUtils.isEmpty(req.getSkuList())) {
            return AjaxResult.getErrorWithMsg("请添加sku信息");
        }
        for (Inventory sku : req.getSkuList()) {
            if (sku.getBaseSaleQuantity() == null) {
                return AjaxResult.getErrorWithMsg("请填写最小起售量");
            }
            if (sku.getBaseSaleUnit() == null) {
                return AjaxResult.getErrorWithMsg("请填写最小起售规格");
            }
            if (sku.getExtType() == null) {
                return AjaxResult.getErrorWithMsg("请填写SKU商品性质");
            }
        }
        return null;
    }

    /**
     * 商品必填项校验
     *
     * @param req
     * @return
     */
    public AjaxResult commonCheck(ProductsSaveReq req) {
        if (req.getCategoryId() == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        if (StringUtils.isBlank(req.getRealName())) {
            return AjaxResult.getErrorWithMsg("请填写实物名称");
        }
        if (StringUtils.isBlank(req.getStorageLocation())) {
            return AjaxResult.getErrorWithMsg("请填写贮存区域");
        }
        if (req.getQualityTime() == null || StringUtils.isBlank(req.getQualityTimeUnit())) {
            return AjaxResult.getErrorWithMsg("请填写保质期时长");
        }
        Long tenantId = req.getTenantId();
        List<ProductsProperty> propertyList = productsPropertyService.selectEffectKeyPropertyByCategoryId(req.getCategoryId());
        for (ProductsProperty property : propertyList) {
            if (req.getKeyValueList().stream().noneMatch(el -> Objects.equals(property.getId(), el.getProductsPropertyId()))) {
                return AjaxResult.getErrorWithMsg("请填写关键属性：" + property.getName());
            }
        }
        if (CollectionUtils.isEmpty(req.getSalePropertyList())) {
            return AjaxResult.getErrorWithMsg("请勾选销售属性");
        }
        if (Objects.nonNull(tenantId) && tenantId > BaseConstant.XIANMU_TENANT_ID) {
            //校验是否存在相同关键属性spu
            List<String> pdNoList = productsPropertyService.equalsSpuByKeyProperty(req.getPdNo(), req.getCategoryId(), req.getCreateType(), req.getKeyValueList());
            if (!CollectionUtils.isEmpty(pdNoList)) {
                return AjaxResult.getErrorWithMsg("有相同SPU，SPU编码为：" + pdNoList.get(0));
            }
        } else {
            //校验是否存在相同关键属性spu
            List<String> pdNoList = productsPropertyService.equalsSpuByKeyProperty(req.getPdNo(), req.getCategoryId(), req.getCreateType(), req.getKeyValueList());
            int i = productsMapper.selectByPdNameCount(req.getRealName());
            if (!CollectionUtils.isEmpty(pdNoList) && i > 0) {
                return AjaxResult.getErrorWithMsg("有相同SPU，SPU编码为：" + pdNoList.get(0));
            }
        }


        return null;
    }

    @Override
    public void sendNewMessage(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, STATUS, ID);
            if (Objects.isNull(id)) {
                continue;
            }
            Long pdId = Long.parseLong(id);
            ProductsVO productsVO = productsMapper.selectByPdId(pdId);
            Integer auditorAdminId = productsVO.getAuditor();
            Admin auditor = adminMapper.selectByPrimaryKey(auditorAdminId);
            if (Objects.isNull(auditor)) {
                return;
            }
            sendAuditMsg(Objects.equals(productsVO.getAuditStatus(), ProductsEnum.AuditStatus.SUCCESS.ordinal()) ? Boolean.TRUE : Boolean.FALSE, auditor.getRealname(), productsVO);
        }
    }

    @Override
    public AjaxResult queryProductName(String name, Integer sku) {
        List<ValuesVo> info = crmBdConfigMapper.queryProductName(name, sku);
        return AjaxResult.getOK(info);
    }

    @Override
    public AjaxResult getCompletionGuide() {

        return AjaxResult.getOK(configMapper.selectOne(Global.COMPLETION_GUIDE));
    }

    @Override
    public AjaxResult citySalesInformationTemplate() {
        return AjaxResult.getOK(configMapper.selectOne(Global.CITY_SALES_INFORMATION_TEMPLATE));
    }

    @Override
    public Products selectByPrimaryKey(Long id) {
        return productsMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<Products> selectListByIdList(List<Long> pdIdList) {
        if (CollectionUtils.isEmpty(pdIdList)){
            return new ArrayList<>();
        }
        return productsMapper.selectListByIdList(pdIdList);
    }


    @Override
    public List<ProductConfig> listAfterSaleRuleConfig(Long secondCategoryId) {
        return productConfigMapper.selectListByTypeAndKey(ProductConfigEnum.ProductConfigTypeEnum.AFTER_SALE_RULE_CONFIG.getValue(), secondCategoryId);
    }

}
