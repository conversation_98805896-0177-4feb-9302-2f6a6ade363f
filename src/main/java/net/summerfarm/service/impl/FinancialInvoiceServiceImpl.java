package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.biz.finance.bo.*;
import net.summerfarm.biz.finance.bo.digital.*;
import net.summerfarm.biz.finance.builder.BwFinancialInvoiceBuilder;
import net.summerfarm.biz.finance.config.FinanceInvoiceConfig;
import net.summerfarm.biz.finance.config.FinanceTaxRateConfig;
import net.summerfarm.biz.finance.constant.BwInvoiceConstant;
import net.summerfarm.biz.finance.constant.FinancialInvoiceConstant;
import net.summerfarm.biz.finance.dto.FinancialInvoiceOrderDTO;
import net.summerfarm.biz.finance.dto.FinancialInvoiceOrderItemDTO;
import net.summerfarm.biz.finance.dto.FinancialInvoiceSellerInfoDTO;
import net.summerfarm.biz.finance.dto.FinancialInvoiceUserDTO;
import net.summerfarm.biz.finance.enums.FinanceInvoiceRedisKeyEnum;
import net.summerfarm.biz.finance.enums.FinancialInvoiceAsyncTaskEnum;
import net.summerfarm.biz.finance.enums.FinancialInvoiceEnum;
import net.summerfarm.biz.finance.enums.InvoiceUrlEnum;
import net.summerfarm.biz.finance.handler.FinancialTransaction;
import net.summerfarm.biz.finance.input.FinancialFailedInvoiceQuery;
import net.summerfarm.biz.finance.input.FinancialInvoiceInput;
import net.summerfarm.biz.finance.util.BwFinancialInvoiceRequestUtil;
import net.summerfarm.biz.finance.util.FinancialInvoiceDataHandleUtil;
import net.summerfarm.biz.finance.vo.FinancialInvoiceDetailVO;
import net.summerfarm.biz.finance.vo.FinancialInvoiceMoneyVO;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.enums.finance.FinanceConstants;
import net.summerfarm.facade.bms.fms.SellingEntityFacade;
import net.summerfarm.mapper.BigFinancialInvoiceSkuMapper;
import net.summerfarm.mapper.FinancialInvoiceAsyncTaskMapper;
import net.summerfarm.mapper.OrderRelationMapper;
import net.summerfarm.mapper.TaxRateCodeNameMappingMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.mapper.offline.FinanceBillAfterSaleDetailsMapper;
import net.summerfarm.model.DTO.FinancialInvoiceImportDTO;
import net.summerfarm.model.DTO.OrderAndItemDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.offline.FinanceBillAfterSaleDetails;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.constant.MessageBusiness;
import net.summerfarm.mq.constant.MessageType;
import net.summerfarm.service.FinancialInvoiceService;
import net.summerfarm.service.InvoiceEmailOverrideService;
import net.summerfarm.service.finance.FinancialSellingEntityQueryService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.redis.support.lock.service.XmLockTemplate;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.ProviderException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.Global.ODERA_ADD_TIMD_SKU;
import static net.summerfarm.contexts.Global.TIME_FRAME_FEE_SKU;
import static net.summerfarm.enums.DownloadBizTypeEnum.FINANCIAL_INVOICE_DOWNLOAD;
import static net.summerfarm.enums.DownloadBizTypeEnum.FINANCIAL_INVOICE_IMPORT_RESULT;
import static net.summerfarm.enums.InvoiceResultEnum.invoiceResult.*;

/**
 * @Description: 财务票据实现
 * @Date: 2021/1/21 17:46
 * @Author: <EMAIL>
 */
@Service
public class FinancialInvoiceServiceImpl extends BaseService implements FinancialInvoiceService {
    static final BigDecimal limit = new BigDecimal("100000");
    static final Long XIAO_TINT_ID= 11349L;
    @Resource
    private FinancialInvoiceMapper financialInvoiceMapper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private InvoiceConfigMapper invoiceConfigMapper;

    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;

    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private AdminMapper adminMapper;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Resource
    private FinancialInvoiceOrdernoRelationMapper financialInvoiceOrdernoRelationMapper;

    @Resource
    private FinanceAccountingStoreDetailMapper financeAccountingStoreDetailMapper;


    @Resource
    private FinanceAccountingPeriodOrderMapper financeAccountingPeriodOrderMapper;

    @Resource
    private FinancialTransaction financialTransaction;

    @Resource
    private FinanceInvoiceExpandMapper financeInvoiceExpandMapper;

    @Resource
    private FinanceAccountingPeriodOrderMapper periodOrderMapper;
    @Resource
    private AdjustmentMapper adjustmentMapper;

    @Resource
    private AdjustmentDetailMapper adjustmentDetailMapper;

    @Resource
    private OrdersCouponMapper ordersCouponMapper;

    @Resource
    private TaxRateConfigMapper taxRateConfigMapper;

    @Resource
    private Executor asyncServiceExecutor;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private BigFinancialInvoiceSkuMapper bigFinancialInvoiceSkuMapper;

    @Autowired
    MqProducer mqProducer;

    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;
    @Resource
    private OrderRelationMapper orderRelationMapper;
    @Resource
    private OrderPreferentialMapper orderPreferentialMapper;
    @Resource
    private FinancialInvoiceAsyncTaskMapper financialInvoiceAsyncTaskMapper;
    @Resource
    private FinanceTaxRateConfig financeTaxRateConfig;
    @Resource
    private TaxRateCodeNameMappingMapper taxRateCodeNameMappingMapper;
    @Resource
    private XmLockTemplate xmLockTemplate;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Resource
    private FinanceInvoiceConfig financeInvoiceConfig;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private FinanceBillAfterSaleDetailsMapper afterSaleDetailsMapper;
    @Resource
    private SellingEntityFacade sellingEntityFacade;
    @Resource
    private FinancialSellingEntityQueryService sellingEntityQueryService;
    @Resource
    private InvoiceEmailOverrideService invoiceEmailOverrideService;
    /**
     * 开票状态字段
     */
    private static final String INVOICE_RESULT = "invoice_result";

    /**
     * 快递单号字段
     */
    private static final String EXPRESS = "express";

    /**
     * id字段
     */
    private static final String ID = "id";

    public static final String SUCCESS = "SUCCESS";

    @Override
    public AjaxResult selectOrderList(FinancialOrderQuery financialOrderQuery) {
        // 默认查询最近7内的订单
        boolean isDefaultTime = Objects.isNull(financialOrderQuery.getStartTime())
                && Objects.isNull(financialOrderQuery.getDeliveryStartTime())
                && Objects.isNull(financialOrderQuery.getBillNumber());
        if(isDefaultTime){
            financialOrderQuery.setStartTime(LocalDateTime.now().minusDays(7));
            financialOrderQuery.setEndTime(LocalDateTime.now());
        }

        Integer pageIndex = financialOrderQuery.getPageIndex();
        Integer pageSize = financialOrderQuery.getPageSize();
        if (pageSize > 10000) {
            logger.error("pageSize is too large, financialOrderQuery:{}", financialOrderQuery,
                    new RuntimeException("pageSize is too large:" + pageSize));
        }
        financialOrderQuery.setInvoiceStatus(InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal());
        PageInfo<FinancialOrderVO> financialPageInfo = PageHelper.startPage(pageIndex, pageSize)
                .doSelectPageInfo(() -> ordersMapper.selectFinancialGroupByOrders(financialOrderQuery));
        // List<FinancialOrderVO> financialOrderVOList = ordersMapper.selectFinancialOrders(financialOrderQuery);
        if (null == financialPageInfo || CollectionUtils.isEmpty(financialPageInfo.getList())) {
            return AjaxResult.getOK("当前筛选项下,未找到待开票订单");
        }
        List<FinancialOrderVO> orderList = financialPageInfo.getList();
        List<String> orderNos = orderList.stream().map(FinancialOrderVO::getOrderNo).collect(Collectors.toList());
        FinancialOrderQuery query = new FinancialOrderQuery();
        query.setOrderNoList(orderNos);
        List<FinancialOrderVO> financialOrderVOList = ordersMapper.selectFinancialOrders(query);
        financialPageInfo.setList(financialOrderVOList);

        // 精准送金额
        List<String> odernNoList = financialOrderVOList.stream().map(FinancialOrderVO::getOrderNo)
                .collect(Collectors.toList());
        Map<String, OrderRelation> relationMap = orderRelationMapper.listActualPaidPrecision(odernNoList).stream()
                .collect(Collectors.toMap(OrderRelation::getOrderNo, Function.identity()));
        for (FinancialOrderVO item : financialOrderVOList) {
            BigDecimal timeFrameFee = BigDecimal.ZERO;
            if (relationMap.containsKey(item.getOrderNo())) {
                OrderRelation orderRelation = relationMap.get(item.getOrderNo());
                timeFrameFee = orderRelation.getPrecisionDeliveryFee();
            }
            item.setTimeFrameFee(timeFrameFee);
            this.dealWithFinancialOrderVO(financialOrderQuery.isCheckDeliveryDate(), item);
        }

        // 账单跨期售后处理
        processAcrossPeriodWhenQuery(financialOrderQuery.getBillNumber(), financialOrderVOList);
        return AjaxResult.getOK(financialPageInfo);
    }

    private void processAcrossPeriodWhenQuery(String billNumber, List<FinancialOrderVO> financialOrderVOList) {
        // 1、按周期查询出所有的售后单
        List<Long> mIds = financialOrderVOList.stream().map(FinancialOrderVO::getMerchantId).collect(Collectors.toList());
        Pair<LocalDate, LocalDate> billCyclePair = getBillCyclePair(billNumber);
        if (billCyclePair == null) {
            return;
        }
        List<FinanceBillAfterSaleDetails> financeBillAfterSaleDetails = afterSaleDetailsMapper.selectAfterSaleByMids(mIds, billCyclePair.getKey(), billCyclePair.getValue());
        // 2、匹配出跨期售后
        Set<String> currentPeriodOrderAfterSaleNoSet = financialOrderVOList.stream()
                .flatMap(el -> el.getAfterSaleOrderList().stream())
                .map(AfterSaleOrderVO::getAfterSaleOrderNo)
                .collect(Collectors.toSet());
        List<FinanceBillAfterSaleDetails> acrossPeriodOrderAfterSales = financeBillAfterSaleDetails.stream().filter(el -> !currentPeriodOrderAfterSaleNoSet.contains(el.getAfterSaleOrderId())).collect(Collectors.toList());
        // 3、金额无脑摊在订单上
        List<FinancialOrderItemVO> allOrderItems = financialOrderVOList.stream()
                .flatMap(el -> el.getFinancialOrderItemList().stream())
                .sorted(Comparator.comparing(FinancialOrderItemVO::getLeftFee).reversed())
                .collect(Collectors.toList());
        Map<String, BigDecimal> orderDimensionApportionsMap = new HashMap<>();
        for (FinanceBillAfterSaleDetails billAfterSaleDetails : acrossPeriodOrderAfterSales) {
            BigDecimal afterSaleAmt = billAfterSaleDetails.getAfterSaleAmt();
            for (FinancialOrderItemVO financialOrderItemVO : allOrderItems) {
                BigDecimal leftFee = financialOrderItemVO.getLeftFee();
                if (leftFee.compareTo(afterSaleAmt) >= 0) {
                    financialOrderItemVO.setLeftFee(leftFee.subtract(afterSaleAmt));
                    populateOrderDimensionApportionsMap(orderDimensionApportionsMap, financialOrderItemVO.getOrderNo(), afterSaleAmt);
                    afterSaleAmt = BigDecimal.ZERO;
                    break;
                } else {
                    afterSaleAmt = afterSaleAmt.subtract(leftFee);
                    populateOrderDimensionApportionsMap(orderDimensionApportionsMap, financialOrderItemVO.getOrderNo(), leftFee);
                    financialOrderItemVO.setLeftFee(BigDecimal.ZERO);
                }
            }
            if (afterSaleAmt.compareTo(BigDecimal.ZERO) > 0) {
                throw new BizException("跨期售后金额大于订单金额,无法发起开票");
            }
        }

        for (FinancialOrderVO financialOrderVO : financialOrderVOList) {
            if (orderDimensionApportionsMap.containsKey(financialOrderVO.getOrderNo())) {
                BigDecimal orderAfterSaleAmt = orderDimensionApportionsMap.get(financialOrderVO.getOrderNo());
                financialOrderVO.setTotalPrice(financialOrderVO.getTotalPrice().subtract(orderAfterSaleAmt));
            }
        }
    }

    private void populateOrderDimensionApportionsMap(Map<String, BigDecimal> orderDimensionApportionsMap, String orderNo, BigDecimal afterSaleAmt) {
        // 如果map中包含orderNo则累加，否则put
        orderDimensionApportionsMap.merge(orderNo, afterSaleAmt, BigDecimal::add);
    }

    /**
     * 处理发票订单,是否可勾选,订单及明细金额
     * @param financialOrderVO 发票订单对象
     */
    private void dealWithFinancialOrderVO(boolean checkDeliveryDate, FinancialOrderVO financialOrderVO){
        // 设置可勾选条件,根据订单状态
        FinancialInvoiceDataHandleUtil.setCheckFlagByFinancialOrder(financialOrderVO);
        // 配送订单时效（短期限制）
        if (checkDeliveryDate) {
            checkDeliveryDate(financialOrderVO);
        }
        // 设置可勾选条件,根据售后状态
        List<AfterSaleOrderVO> afterSaleOrderList = afterSaleOrderMapper.queryByOrderNo(financialOrderVO.getOrderNo());
        // 过滤出非账单周期的售后单
        afterSaleOrderList = filterInBillCycleAfterSaleOrderByBillNumber(financialOrderVO.getBillNumber(), afterSaleOrderList);
        financialOrderVO.setAfterSaleOrderList(afterSaleOrderList);

        FinancialInvoiceDataHandleUtil.setCheckFlagByAfterOrder(financialOrderVO,afterSaleOrderList);
        // 设置可勾选条件,根据账单
        this.setCheckFlagByFinancialBill(financialOrderVO);
        // 设置分摊信息
        this.setAverageFlagByFinancialAverage(financialOrderVO);
        // 将可计算的sku的 退款后金额
        this.calculateInvoiceOrderItemMoney(financialOrderVO,afterSaleOrderList);
    }

    private List<AfterSaleOrderVO> filterInBillCycleAfterSaleOrderByBillNumber(String billNumber, List<AfterSaleOrderVO> afterSaleOrderList) {
        // 如果是账单 获取起止日
        if (StringUtils.isBlank(billNumber)) {
            return afterSaleOrderList;
        }
        Pair<LocalDate, LocalDate> billCyclePair = getBillCyclePair(billNumber);
        if (billCyclePair == null) {
            return afterSaleOrderList;
        }
        LocalDate start = billCyclePair.getKey();
        LocalDate end = billCyclePair.getValue();
        return filterInBillCycleAfterSaleOrder(start, end, afterSaleOrderList);
    }

    private List<AfterSaleOrderVO> filterInBillCycleAfterSaleOrder(LocalDate start, LocalDate end, List<AfterSaleOrderVO> afterSaleOrderList) {
        // 使用 Stream 过滤符合条件的日期
        // 过滤 afterSaleOrderList 中 updatetime 在账单周期范围内的售后单
        List<AfterSaleOrderVO> filteredList = afterSaleOrderList.stream()
                .filter(order -> {
                    LocalDateTime updatetime = order.getUpdatetime();
                    // 将 LocalDateTime 转换为 LocalDate 进行比较
                    LocalDate updateDate = updatetime.toLocalDate();
                    return !updateDate.isBefore(start) && !updateDate.isAfter(end);  // 在周期内
                })
                .collect(Collectors.toList());

        List<String> filteredListOutCycle = afterSaleOrderList.stream()
                .filter(order -> {
                    LocalDateTime updatetime = order.getUpdatetime();
                    LocalDate updateDate = updatetime.toLocalDate();
                    return updateDate.isBefore(start) || updateDate.isAfter(end);  // 不在周期内
                }).map(AfterSaleOrderVO::getAfterSaleOrderNo)
                .collect(Collectors.toList());
        logger.info("账单周期外售后单:{}",filteredListOutCycle);
        return filteredList;
    }

    private Pair<LocalDate, LocalDate> getBillCyclePair(String billNumber) {
        if (StringUtils.isBlank(billNumber)) {
            return null;
        }
        FinanceAccountingPeriodOrder query = new FinanceAccountingPeriodOrder();
        query.setBillNumber(billNumber);
        FinanceAccountingPeriodOrder periodOrder = periodOrderMapper.selectOne(query);
        // 日期范围字符串
        String billCycle = periodOrder.getBillCycle();
        // 使用 split 方法拆分字符串
        String[] parts = billCycle.split("-");
        // 直接解析起始日期和结束日期
        LocalDate startDate = LocalDate.of(
                Integer.parseInt(parts[0]),  // 年
                Integer.parseInt(parts[1]),  // 月
                Integer.parseInt(parts[2])   // 日
        );

        LocalDate endDate = LocalDate.of(
                Integer.parseInt(parts[3]),  // 年
                Integer.parseInt(parts[4]),  // 月
                Integer.parseInt(parts[5])   // 日
        );
        return Pair.of(startDate, endDate);
    }

    private void checkDeliveryDate(FinancialOrderVO financialOrderVO) {
        LocalDateTime deliveryTime = financialOrderVO.getDeliveryTime();
        if (deliveryTime == null) {
            return;
        }
        Integer checkDeliveryDays = financeInvoiceConfig.getCheckDeliveryDays();
        LocalDateTime createInvoiceUsableTime = deliveryTime.plusDays(checkDeliveryDays);
        // 可开票时间晚于当前时间设置false
        financialOrderVO.setCheckFlag(LocalDateTime.now().isAfter(createInvoiceUsableTime));
    }

    /**
     * 未确定账单或者没出账单的订单不可以进行开票
     * @param item 开票订单对象
     */
    private void setCheckFlagByFinancialBill(FinancialOrderVO item) {
        String billNumber = item.getBillNumber();
        // 账期方式下单,但是未生成账单的
        if(Objects.isNull(billNumber)){
            if(Objects.equals(DirectEnum.ACCOUNTING_PERIOD.ordinal(),item.getDirect())){
                item.setCheckFlag(Boolean.FALSE);
            }
            return;
        }
        FinanceAccountingPeriodOrder query = new FinanceAccountingPeriodOrder();
        query.setBillNumber(billNumber);
        FinanceAccountingPeriodOrder periodOrder = periodOrderMapper.selectOne(query);
        if (Objects.isNull(periodOrder)) {
            item.setCheckFlag(Boolean.FALSE);
            return;
        }
        if (Objects.equals(periodOrder.getType(), FinancialInvoiceConstant.NOT_CONFIRM)) {
            item.setCheckFlag(Boolean.FALSE);
        }
    }

    /**
     * 如果有未分摊的明细的不可以进行开票
     * @param item 开票订单对象
     */
    private void setAverageFlagByFinancialAverage(FinancialOrderVO item) {
        if (Objects.isNull(item.getBillNumber())){
            item.setAverageFlag(AdjustmentAverageStatusEnum.NOT_HAS_ADJUSTMENT.ordinal());
            return;
        }
        AdjustmentInput input = new AdjustmentInput();
        input.setBillNumber(item.getBillNumber());
        input.setStatus(AdjustmentStatus.SUCCESS.ordinal());
        List<Adjustment> adjustmentList = adjustmentMapper.select(input);
        if (CollectionUtils.isEmpty(adjustmentList)) {
            item.setAverageFlag(AdjustmentAverageStatusEnum.NOT_HAS_ADJUSTMENT.ordinal());
            return;
        }

        boolean notAverageFlag = adjustmentList.stream().anyMatch(el -> el.getAverageFlag() == AdjustmentAverageStatusEnum.NOT_AVERAGE.ordinal());
        if(notAverageFlag){
            item.setAverageFlag(AdjustmentAverageStatusEnum.NOT_AVERAGE.ordinal());
            item.setCheckFlag(Boolean.FALSE);
            return;
        }
        item.setAverageFlag(AdjustmentAverageStatusEnum.HAS_AVERAGE.ordinal());
    }

    /**
     * 设置开票订单明细可开票金额
     * @param item 开票订单对象
     * @param afterSaleOrderVOList 售后信息对象
     */
    private void calculateInvoiceOrderItemMoney(FinancialOrderVO item,List<AfterSaleOrderVO> afterSaleOrderVOList){
        List<FinancialOrderItemVO> orderItemList = item.getFinancialOrderItemList();
        // 订单退款总金额
        BigDecimal backFeeTotal = BigDecimal.ZERO;
        // 订单调整总金额
        BigDecimal totalAdjustment = BigDecimal.ZERO;
        for (FinancialOrderItemVO itemVO : orderItemList) {
            itemVO.setAverageFlag(item.getAverageFlag());

            // sku对应的售后订单且为成功申请后的
            List<AfterSaleOrderVO> skuAfterSaleOrderList= afterSaleOrderVOList.stream()
                    .filter(el -> Objects.deepEquals(el.getSku(), itemVO.getSku()))
                    .filter(el -> Objects.deepEquals(el.getStatus(),AfterSaleOrderStatus.SUCCESS.getStatus()))
                    .filter(el -> (el.getHandleType() >= AfterSaleHandleType.REFUND.getType())
                            && (el.getHandleType() <= AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType()))
                    .collect(Collectors.toList());

            // 考虑是否为申请了退款且成功的sku：成功退款金额累加：
            BigDecimal backFee = BigDecimal.ZERO;
            if(CollectionUtil.isNotEmpty(skuAfterSaleOrderList)){
                backFee = skuAfterSaleOrderList.stream().map(AfterSaleOrderVO::getHandleNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 计算明细税率
            TaxRateConfig taxRateConfig = this.getTaxRateConfig(itemVO.getSku(), itemVO.getCategoryId());
            itemVO.setTaxRateValue(taxRateConfig.getTaxRateValue());
            itemVO.setTaxRateCode(taxRateConfig.getTaxRateCode());

            // 订单明细调整金额
            BigDecimal adjustmentPrice = Optional.ofNullable(adjustmentDetailMapper.selectSuccessNumByOrderItem(itemVO.getOrderItemId())).
                    orElse(BigDecimal.ZERO);
            totalAdjustment = totalAdjustment.add(adjustmentPrice);

            // 设置退款后金额, 实付金额 - 退款金额 + 调整金额
            itemVO.setLeftFee(itemVO.getTotalPrice().subtract(backFee).add(adjustmentPrice));
            backFeeTotal = backFeeTotal.add(backFee);
        }

        BigDecimal deliveryFee = item.getDeliveryFee();
        // 考虑是否退运费
        List<AfterSaleOrderVO> deliveryAfterSaleOrderList= afterSaleOrderVOList.stream()
                .filter(el -> Objects.deepEquals(el.getStatus(),AfterSaleOrderStatus.SUCCESS.getStatus()))
                .filter(el -> AfterSaleHandleType.RETURN_SHIPPING.getType().equals(el.getHandleType())
                        ||AfterSaleHandleType.RETURN_SHIPPING_BILL.getType().equals(el.getHandleType()))
                .collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(deliveryAfterSaleOrderList)){
            BigDecimal backDeliveryFee = deliveryAfterSaleOrderList.stream().map(AfterSaleOrderVO::getHandleNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            deliveryFee = deliveryFee.subtract(backDeliveryFee);

            backFeeTotal = backFeeTotal.add(backDeliveryFee);
        }

        // 考虑是否调整运费
        BigDecimal adjustmentDeliveryPrice = adjustmentDetailMapper.selectSuccessNumByOrderNo(item.getOrderNo());
        totalAdjustment = totalAdjustment.add(adjustmentDeliveryPrice);
        deliveryFee = deliveryFee.add(adjustmentDeliveryPrice);

        // 考虑运费优惠金额,仅做展示使用
        BigDecimal deliveryDiscountMoney = this.calculateDeliveryDiscountMoney(item.getOrderNo());
        deliveryFee = deliveryFee.add(deliveryDiscountMoney);
        item.setDeliveryFee(deliveryFee);

        // 订单金额总和 = 订单实付金额  - sku售后金额 - 运费售后金额 + sku调整金额 + 运费调整金额
        BigDecimal totalPrice = item.getTotalPrice();
        BigDecimal feeAmount = totalPrice.subtract(backFeeTotal).add(totalAdjustment);
        item.setFeeAmount(feeAmount);
        item.setTotalAdjustment(totalAdjustment);
    }

    /**
     * 获取配送费优惠金额
     * @param orderNo 订单号
     * @return 配送费优惠金额，返回值为负数
     */
    private BigDecimal calculateDeliveryDiscountMoney(String orderNo){
        // 运费券
        BigDecimal deliveryDiscountAmount = orderPreferentialMapper.selectDeliveryFeeAmountByOrderNo(orderNo);
        if (deliveryDiscountAmount != null && deliveryDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
            return deliveryDiscountAmount.negate();
        }
        List<MerchantCouponVO> merchantCouponVOList = ordersCouponMapper.select(orderNo);
        merchantCouponVOList = Optional.ofNullable(merchantCouponVOList).orElse(new ArrayList<>());
        List<MerchantCouponVO> deliveryFeeCouponList = merchantCouponVOList.stream()
                .filter(c -> Objects.equals(c.getAgioType(), CouponEnum.AgioType.FREIGHT.ordinal()))
                .collect(Collectors.toList());

        // 取反操作，优惠金额为负数，而优惠券操作是正数
        BigDecimal deliveryDiscountMoney = BigDecimal.ZERO;
        if(CollectionUtil.isNotEmpty(deliveryFeeCouponList)){
            deliveryDiscountMoney = deliveryFeeCouponList.stream()
                    .map(MerchantCouponVO::getMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .negate();
        }
        return deliveryDiscountMoney;
    }

    /**
     * 获取sku的税率,税号
     * @param sku sku
     * @param categoryId 所属品类
     * @return 税率
     */
    public TaxRateConfig getTaxRateConfig(String sku,Integer categoryId){
        TaxRateConfigBO config = financeTaxRateConfig.getConfig(FinanceTaxRateConfig.BizType.DIGITAL_TIME_FRAME);
        TaxRateConfig query = new TaxRateConfig();
        query.setSku(sku);
        // 精准送使用默认税率税号
        if(Objects.equals(TIME_FRAME_FEE_SKU,sku)){
            query.setTaxRateCode(config.getTaxRateCode());
            query.setTaxRateValue(new BigDecimal(config.getTaxRate()));
//            query.setTaxRateCode("3010102020100000000");
//            query.setTaxRateValue(new BigDecimal("0.13"));
            return query;
        }
        TaxRateConfig taxRateConfig = taxRateConfigMapper.selectByPdIdAndCategoryId(query);
        // spu维度无税率,使用类目维度税率
        if(Objects.isNull(taxRateConfig)){
            query.setSku(null);
            query.setCategoryId(categoryId);
            taxRateConfig = taxRateConfigMapper.selectByPdIdAndCategoryId(query);
        }
        taxRateConfig = Optional.ofNullable(taxRateConfig).orElse(new TaxRateConfig());
        return taxRateConfig;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult invoiceSave(FinancialInvoiceInput financialInvoiceInput) {
        if (financialInvoiceInput.getBelongType() == null){
            financialInvoiceInput.setBelongType(0);
        }
        // 校验抬头
        InvoiceConfig invoiceConfig = initInvoiceConfig(financialInvoiceInput);
        if (Objects.isNull(invoiceConfig)) {
            return AjaxResult.getError("不存在该抬头信息,请重新选择");
        }
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        FinancialInvoiceUserDTO dutyFreeGoodUserDTO = new FinancialInvoiceUserDTO();
        AjaxResult ajaxResult = FinancialInvoiceDataHandleUtil.financialInvoiceOrderHandle(financialInvoiceInput, financialInvoiceUserDTO, dutyFreeGoodUserDTO);
        if(!ajaxResult.isSuccess()){
            return ajaxResult;
        }
        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
        String mailAddress = invoiceConfig.getMailAddress();
        financialInvoiceUserDTO.setMailAddress(mailAddress);
        dutyFreeGoodUserDTO.setMailAddress(mailAddress);

        // 校验前置
        boolean isRepeatInvoice = this.checkRepeatInvoice(financialInvoiceUserDTO);
        if(isRepeatInvoice){
            return AjaxResult.getErrorWithMsg("勾选订单sku已开票,请勿重复提交");
        }
        isRepeatInvoice = this.checkRepeatInvoice(dutyFreeGoodUserDTO);
        if(isRepeatInvoice){
            return AjaxResult.getErrorWithMsg("勾选订单sku已开票,请勿重复提交");
        }

        // 普通发票
        AjaxResult result = AjaxResult.getOK();
        if(CollectionUtil.isNotEmpty(financialInvoiceUserDTO.getOrderNoList())){
            financialInvoiceUserDTO.setBelongType(financialInvoiceInput.getBelongType());
            result = this.invoiceSave(financialInvoiceUserDTO);
            if (!result.isSuccess()) {
                throw new net.xianmu.common.exception.BizException(result.getMsg());
            }
        }

        // 免税品发票
        if(CollectionUtil.isNotEmpty(dutyFreeGoodUserDTO.getOrderItemIdList())){
            dutyFreeGoodUserDTO.setInvoiceConfig(invoiceConfig);
            dutyFreeGoodUserDTO.setBelongType(financialInvoiceInput.getBelongType());
            result = this.invoiceSave(dutyFreeGoodUserDTO);
            if (!result.isSuccess()) {
                throw new net.xianmu.common.exception.BizException(result.getMsg());
            }
        }

        return result;
    }

    private InvoiceConfig initInvoiceConfig(FinancialInvoiceInput financialInvoiceInput) {
        Integer belongType = financialInvoiceInput.getBelongType();
        if (belongType == 0) {
            return invoiceConfigMapper.selectByPrimaryKey(financialInvoiceInput.getInvoiceId());
        }
        List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = financialInvoiceInput.getFinancialInvoiceOrderDTOList();
        String orderNo = financialInvoiceOrderDTOList.get(0).getOrderNo();
        OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
        if (orderVO == null) {
            return null;
        }
        Long mId = orderVO.getmId();
        InvoiceConfigVO queryInvoiceConfigVO = new InvoiceConfigVO();
        //找到店铺id
        queryInvoiceConfigVO.setMerchantId(mId);
        queryInvoiceConfigVO.setValidStatus(0);
        queryInvoiceConfigVO.setType(3);
        queryInvoiceConfigVO.setInvoiceTitle(financialInvoiceInput.getPersonName());
        queryInvoiceConfigVO.setMailAddress(financialInvoiceInput.getPersonMail());

        List<InvoiceConfigVO> invoiceConfigVOS = invoiceConfigMapper.selectByKeys(queryInvoiceConfigVO);
        queryInvoiceConfigVO.setCompanyReceiver(financialInvoiceInput.getPersonName());
        //查找店铺寻找adminId
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        Integer adminId = merchant.getAdminId();
        if (adminId!=null && adminId > 0) {
            queryInvoiceConfigVO.setAdminId(merchant.getAdminId());
        }
        if (CollectionUtils.isEmpty(invoiceConfigVOS)){
            //插入
            invoiceConfigMapper.insert(queryInvoiceConfigVO);
            financialInvoiceInput.setInvoiceId(queryInvoiceConfigVO.getId());
            return queryInvoiceConfigVO;
        }
        financialInvoiceInput.setInvoiceId(invoiceConfigVOS.get(0).getId());
        return invoiceConfigVOS.get(0);
    }

    private AjaxResult invoiceSave(FinancialInvoiceUserDTO financialInvoiceUserDTO){
        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
        // 生成单据信息
        AjaxResult invoiceResult = this.creatInvoice(financialInvoiceUserDTO, bwInvoiceBO, InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING);
        if (!invoiceResult.isSuccess()) {
            return invoiceResult;
        }

        if (FinancialInvoiceEnum.invoiceType.isDigital(financialInvoiceUserDTO.getInvoiceType())) {
            // 数电发票任务参数组装
            this.generateInvoiceAsyncTask(financialInvoiceUserDTO, bwInvoiceBO, FinancialInvoiceAsyncTaskEnum.TaskType.BLUE);
        }

        if (FinancialInvoiceEnum.invoiceType.isDigital(financialInvoiceUserDTO.getInvoiceType())) {
            // 数电发票任务参数组装
            return AjaxResult.getOK(financialInvoiceUserDTO.getFinancialInvoiceId());
        }

        // 发送mq消息
        InvoiceUrlEnum invoiceUrlEnum = Objects.equals(FinancialInvoiceEnum.invoiceType.VAT.ordinal(), financialInvoiceUserDTO.getInvoiceType()) ?
                InvoiceUrlEnum.VAT_INVOICE : InvoiceUrlEnum.ELECTRONIC_INVOICE;
        this.sendFinancialInvoiceMessage(bwInvoiceBO,financialInvoiceUserDTO.getFinancialInvoiceId(),
                invoiceUrlEnum,MessageType.INITIATE_INVOICING);

        return AjaxResult.getOK(financialInvoiceUserDTO.getFinancialInvoiceId());
    }

    /**
     * 创建开票对象
     * @param financialInvoiceUserDTO 票据信息
     * @param bwInvoiceBO 百旺开票对象
     * @param bwInvoiceBO 订单状态
     * @return ok
     */
    private AjaxResult creatInvoice(FinancialInvoiceUserDTO financialInvoiceUserDTO,BwInvoiceBO bwInvoiceBO,
                                    InvoiceResultEnum.orderInvoiceStatus orderInvoiceStatus) {
        // 金额信息
        FinancialInvoiceMoneyVO financialInvoiceMoney = new FinancialInvoiceMoneyVO();
        this.getFinancialInvoiceMoney(financialInvoiceUserDTO,financialInvoiceMoney);

        // 获取订单信息
        List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();
        AjaxResult orderInfoResult = this.getInvoiceProductInfo(financialInvoiceUserDTO, financialInvoiceMoney, bwInvoiceProductList);
        if(!orderInfoResult.isSuccess()){
            return orderInfoResult;
        }
        if(CollectionUtil.isEmpty(bwInvoiceProductList)){
            return AjaxResult.getErrorWithMsg("订单内无有效商品可开票");
        }
        // 专票校验是否包含免税品
        if(FinancialInvoiceEnum.invoiceType.isSpecial(financialInvoiceUserDTO.getInvoiceType())) {
            List<BwInvoiceProduct> zeroTaxValueProduct = bwInvoiceProductList.stream()
                    .filter(p -> Objects.equals(p.getSl(), BigDecimal.ZERO.toString()))
                    .collect(Collectors.toList());

            if(CollectionUtil.isNotEmpty(zeroTaxValueProduct)){
                return AjaxResult.getErrorWithMsg("勾选订单sku包含免税品,免税品不可开增值税专用发票,请刷新后重试");
            }
        }
        boolean isDigitalInvoice = FinancialInvoiceEnum.invoiceType.isDigital(financialInvoiceUserDTO.getInvoiceType());
        // 数电票不限制金额
        if (!isDigitalInvoice) {
            // 开票金额不能大于10万,公司票额限制
            BigDecimal bigDecimal = new BigDecimal("100000");
            if(bigDecimal.compareTo(financialInvoiceUserDTO.getAllMoney()) <= 0){
                return AjaxResult.getErrorWithMsg("开票订单超过十万，请在后台手动勾选订单进行开票");
            }
        }

        if (isDigitalInvoice) {
            // 数电票限制数量2000条以内 可配
            Long maxInvoiceProductCnt = financeInvoiceConfig.getMaxInvoiceProductCnt();
            if (bwInvoiceProductList.size() > maxInvoiceProductCnt) {
                String errorMsg = String.format("因服务商要求，单张发票商品行不得超过%s行，请重新选择订单分次开票", maxInvoiceProductCnt);
                return AjaxResult.getErrorWithMsg(errorMsg);
            }
        }

        // 数电票商品简称校验
        if (isDigitalInvoice) {
            String noTaxRateCodeNameMappingProducts = bwInvoiceProductList.stream()
                    .filter(el -> Objects.isNull(el.getShbmjc()))
                    .map(BwInvoiceProduct::getSpmc)
                    .distinct()
                    .collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(noTaxRateCodeNameMappingProducts)) {
                return AjaxResult.getErrorWithMsg("以下商品税收编码简称未配置:" + noTaxRateCodeNameMappingProducts + ",请联系管理员进行配置");
            }
        }
        // 生成发票信息
        financialTransaction.creatInvoice(financialInvoiceUserDTO,orderInvoiceStatus);

        // 拼装数据
        if(Objects.nonNull(bwInvoiceBO)){
            financialInvoiceUserDTO.setAdminName(getAdminName());
            // 获取销售主体的消息
            FinancialInvoiceSellerInfoDTO sellerInfoDTO = prepareSellingInfoDTO(financialInvoiceUserDTO.getSellingEntityName());
            FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBOFacade(isDigitalInvoice, bwInvoiceProductList, financialInvoiceUserDTO, bwInvoiceBO, sellerInfoDTO);
        }
        return AjaxResult.getOK();
    }

    private FinancialInvoiceSellerInfoDTO prepareSellingInfoDTO(String sellingEntityName) {
        Map<String, FinancialInvoiceSellerInfoDTO> sellingEntityInfo = sellingEntityQueryService.getSellingEntityInfo(Collections.singleton(sellingEntityName));
        return sellingEntityInfo.get(sellingEntityName);
    }

    /**
     * 数电发票任务参数组装
     * @param financialInvoiceUserDTO
     * @param bwInvoiceBO
     */
    private void generateInvoiceAsyncTask(FinancialInvoiceUserDTO financialInvoiceUserDTO, BwInvoiceBO bwInvoiceBO, FinancialInvoiceAsyncTaskEnum.TaskType financialInvoiceAsyncTaskEnum) {
        // 组装任务的执行参数（调用百旺接口）
        FinancialInvoiceAsyncTask task = new FinancialInvoiceAsyncTask();
        task.setType(financialInvoiceAsyncTaskEnum.ordinal());
        task.setInvoiceId(financialInvoiceUserDTO.getFinancialInvoiceId());
        task.setTaskResult(FinancialInvoiceAsyncTaskEnum.Status.INIT.getResult());
        task.setInvokeCount(0);
        task.setInvokeParams(JSON.toJSONString(bwInvoiceBO));
        financialInvoiceAsyncTaskMapper.insert(task);
        logger.info("数电发票任务插入完毕，发票id:{}", financialInvoiceUserDTO.getFinancialInvoiceId());
    }


    /**
     * 创建开票对象
     * @param financialInvoiceUserDTO 票据信息
     * @return ok
     */
    private List<BigFinancialSkuVO> showFinancial(FinancialInvoiceUserDTO financialInvoiceUserDTO, FinancialInvoiceMoneyVO financialInvoiceMoney,
                                                  String orderNo,FinancialInvoiceInput financialInvoiceInput, FinancialInvoiceEnum.dutyFreeGood financialInvoiceEnum) {
        // 获取订单信息
        List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();
        // 专票校验是否包含免税品
        if (FinancialInvoiceEnum.invoiceType.isSpecial(financialInvoiceUserDTO.getInvoiceType())) {
            List<BwInvoiceProduct> zeroTaxValueProduct = bwInvoiceProductList.stream()
                    .filter(p -> Objects.equals(p.getSl(), BigDecimal.ZERO.toString()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(zeroTaxValueProduct)) {
                throw new DefaultServiceException("勾选订单sku包含免税品,免税品不可开增值税专用发票,请刷新后重试");
            }
        }
        List<BigFinancialSkuVO> outs = new ArrayList<>();

        List<FinancialInvoiceOrderItemDTO> orderItemResultList = financialInvoiceUserDTO.getOrderItemResultList();
        //检查sku
        Set<String> skuSet = orderItemResultList.stream().map(FinancialInvoiceOrderItemDTO::getSku).collect(Collectors.toSet());
        Set<String> errorSkuSet = new HashSet<>(skuSet.size());
        //按照 sku 和 价格去排序
        List<FinancialInvoiceOrderItemDTO> itemDTOS = orderItemResultList.stream().sorted(Comparator.comparing(FinancialInvoiceOrderItemDTO::getSku)
                .thenComparing(FinancialInvoiceOrderItemDTO::getPrice)).collect(Collectors.toList());
        //根据单价拆平
        List<FinancialInvoiceOrderItemDTO> allList = new ArrayList<>();
        TaxRateConfigBO config = financeTaxRateConfig.getConfig(FinanceTaxRateConfig.BizType.DIGITAL_TIME_FRAME);
        itemDTOS.forEach(
                it -> {
                    Integer number = it.getAmount();
                    String sku = it.getSku();
                    it.setSort(1);
                    if (TIME_FRAME_FEE_SKU.equals(sku)) {
                        it.setPdName(config.getProductName());
                        it.setTaxRateCode(config.getTaxRateCode());
                        it.setTaxRateValue(new BigDecimal(config.getTaxRate()));
//                        it.setPdName("运输服务费-精准送");
//                        it.setTaxRateCode("3010102020100000000");
//                        it.setTaxRateValue(new BigDecimal("0.13"));
                    } else {
                        // 其他sku税率和税收编码
                        TaxRateConfig taxRateConfig = this.getTaxRateConfig(sku, it.getCategoryId());
                        if (taxRateConfig == null || Objects.isNull(taxRateConfig.getTaxRateValue()) || Objects.isNull(taxRateConfig.getTaxRateCode())) {
                            logger.error("没有税率和税收编码:{},{}", sku, it.getCategoryId());
                            skuSet.add(sku);
                        }else {
                            it.setTaxRateCode(taxRateConfig.getTaxRateCode());
                            it.setTaxRateValue(taxRateConfig.getTaxRateValue());
                        }
                    }
                    for (int i = 0; i < number; i++) {
                        FinancialInvoiceOrderItemDTO itemDTO = JSONUtil.toBean(JSONUtil.toJsonStr(it), FinancialInvoiceOrderItemDTO.class);
                        itemDTO.setAmount(1);
                        allList.add(itemDTO);
                    }
                }
        );
        if (!CollectionUtils.isEmpty(errorSkuSet)) {
            throw new BizException("订单中以下sku未配置税率，请联系品类运营进行配置" + JSONUtil.toJsonStr(errorSkuSet));
        }
        //专票补充订单信息
        if (FinancialInvoiceEnum.dutyFreeGood.NOT_DUTY_FREE_GOOD.ordinal() == financialInvoiceEnum.ordinal()) {
            // 运费单独计一行 开票运费 = 实付运费 + 运费调整金额 + 运费退款金额（负数）
            BigDecimal deliveryFee = financialInvoiceMoney.getDeliveryFee()
                    .add(financialInvoiceMoney.getAdjustDeliveryFeeTotalPrice())
                    .add(financialInvoiceMoney.getDeliveryPreferentialAmount());
            if (BigDecimal.ZERO.compareTo(deliveryFee) < 0) {
                allList.add(0, buildDeliveryFee(deliveryFee));
            }
            // 超时加单单 独计一行
            BigDecimal outTimesFee = financialInvoiceMoney.getOutTimesFee();
            if (BigDecimal.ZERO.compareTo(outTimesFee) < 0) {
                allList.add(0, buildOutTimesFeeBigDecimal(outTimesFee));
            }
        }
        financialInvoiceUserDTO.setAmountExcludingTax(financialInvoiceUserDTO.getAllMoney().subtract(financialInvoiceUserDTO.getTotalTaxMoney()));
        //拆分
        Map<Integer, List<FinancialInvoiceOrderItemDTO>> spit = spit(allList);
        Collection<List<FinancialInvoiceOrderItemDTO>> values = spit.values();
        int i = 0;
        for (List<FinancialInvoiceOrderItemDTO> value : values) {
            BigFinancialSkuVO vo = new BigFinancialSkuVO();
            vo.setOrderItemIdList(financialInvoiceUserDTO.getOrderItemIdList());
            BigDecimal totalPrice = value.stream().map(FinancialInvoiceOrderItemDTO::getSkuTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (i == 0) {
                FinanceInvoiceExpand financeInvoiceExpand = financialInvoiceUserDTO.getFinanceInvoiceExpand();
                if (financeInvoiceExpand != null) {
                    financeInvoiceExpand.reCalPreferentialAmount(totalPrice);
                    vo.setFinanceInvoiceExpand(financeInvoiceExpand);
                }else {
                    vo.setFinanceInvoiceExpand(FinanceInvoiceExpand.getBaseFinanceInvoiceExpand(totalPrice));
                }
            }else {
                vo.setFinanceInvoiceExpand(FinanceInvoiceExpand.getBaseFinanceInvoiceExpand(totalPrice));
            }
            vo.setBatchNo(getBatchNo(orderNo, i, financialInvoiceEnum));
            //再次合并
            vo.setList(buildBigFinancialSkuItemVOList(value, orderNo, null));
            vo.setDutyFreeGood(financialInvoiceEnum.ordinal());
            //获取专票类型 免税品要电票
            Integer invoiceType = financialInvoiceInput.getInvoiceType();
            if (FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD.equals(financialInvoiceEnum)){
                invoiceType = 0;
            }
            vo.setInvoiceType(invoiceType);
            outs.add(vo);
            i++;
        }
        return outs;
    }

    private FinancialInvoiceOrderItemDTO buildDeliveryFee(BigDecimal deliveryFee){
        FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO = new FinancialInvoiceOrderItemDTO();
        financialInvoiceOrderItemDTO.setSku(TIME_FRAME_FEE_SKU);
        financialInvoiceOrderItemDTO.setPrice(deliveryFee);
        financialInvoiceOrderItemDTO.setAmount(1);
        financialInvoiceOrderItemDTO.setSort(0);
        TaxRateConfigBO config = financeTaxRateConfig.getConfig(FinanceTaxRateConfig.BizType.DIGITAL_DELIVERY);
        financialInvoiceOrderItemDTO.setPdName(config.getProductName());
        financialInvoiceOrderItemDTO.setTaxRateCode(config.getTaxRateCode());
        financialInvoiceOrderItemDTO.setTaxRateValue(new BigDecimal(config.getTaxRate()));
//        financialInvoiceOrderItemDTO.setPdName("运输服务费-配送费");
//        financialInvoiceOrderItemDTO.setTaxRateValue(new BigDecimal("0.13"));
//        financialInvoiceOrderItemDTO.setTaxRateCode("3010102020100000000");
        financialInvoiceOrderItemDTO.setWeigh("个");
        return financialInvoiceOrderItemDTO;
    }
    private FinancialInvoiceOrderItemDTO buildOutTimesFeeBigDecimal(BigDecimal outTimesFee){
        FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO = new FinancialInvoiceOrderItemDTO();
        financialInvoiceOrderItemDTO.setPdName("其他现代服务-超时加单");
        financialInvoiceOrderItemDTO.setSku(ODERA_ADD_TIMD_SKU);
        financialInvoiceOrderItemDTO.setPrice(outTimesFee);
        financialInvoiceOrderItemDTO.setAmount(1);
        financialInvoiceOrderItemDTO.setSort(0);
        financialInvoiceOrderItemDTO.setTaxRateValue(new BigDecimal("0.06"));
        financialInvoiceOrderItemDTO.setTaxRateCode("3049900000000000000");
        financialInvoiceOrderItemDTO.setWeigh("个");
        return financialInvoiceOrderItemDTO;
    }

    private List<BigFinancialSkuItemVO> buildBigFinancialSkuItemVOList(List<FinancialInvoiceOrderItemDTO> list,String orderNo,String billNumber){
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<String, List<FinancialInvoiceOrderItemDTO>> maps = list.stream()
                .collect(Collectors.groupingBy(FinancialInvoiceOrderItemDTO::getSkuPriceGroup));
        List<BigFinancialSkuItemVO> outs = new ArrayList<>(list.size());
        for (String s : maps.keySet()) {
            List<FinancialInvoiceOrderItemDTO> financialInvoiceOrderItemDTOS = maps.get(s);
            FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO = financialInvoiceOrderItemDTOS.get(0);
            BigFinancialSkuItemVO bigFinancialSkuItemVO = new BigFinancialSkuItemVO();
            BeanCopyUtil.copyProperties(financialInvoiceOrderItemDTO, bigFinancialSkuItemVO);
            bigFinancialSkuItemVO.setTotalPrice(bigFinancialSkuItemVO.getPrice().multiply(new BigDecimal(financialInvoiceOrderItemDTOS.size())));
            bigFinancialSkuItemVO.setAmount(financialInvoiceOrderItemDTOS.size());
            bigFinancialSkuItemVO.setOrderNo(orderNo);
            bigFinancialSkuItemVO.setWeight(financialInvoiceOrderItemDTO.getWeigh());
            bigFinancialSkuItemVO.setBillNumber(billNumber);
            outs.add(bigFinancialSkuItemVO);
        }
        return outs;


    }

    private String getBatchNo(String orderNo,int i , FinancialInvoiceEnum.dutyFreeGood dutyFreeGood){
        return orderNo + "_" + i + "_" + dutyFreeGood.ordinal();
    }


    private Map<Integer, List<FinancialInvoiceOrderItemDTO>> spit(List<FinancialInvoiceOrderItemDTO> allList) {
        int i = 1;
        Map<Integer, List<FinancialInvoiceOrderItemDTO>> map = new HashMap<>();
        List<FinancialInvoiceOrderItemDTO> spitList = new ArrayList<>();
        BigDecimal sum = BigDecimal.ZERO;
        for (int j = 0; j < allList.size(); j++) {
            FinancialInvoiceOrderItemDTO itemDTO = allList.get(j);
            if (itemDTO.getPrice().add(sum).compareTo(limit) < 0) {
                spitList.add(itemDTO);
                sum = sum.add(itemDTO.getPrice());
            } else {
                if (itemDTO.getPrice().add(sum).compareTo(limit) == 0) {
                    spitList.add(itemDTO);
                    sum = BigDecimal.ZERO;
                }
                map.put(i, spitList);
                spitList = new ArrayList<>();
                i = i + 1;
                if (sum.compareTo(BigDecimal.ZERO) > 0) {
                    sum = allList.get(j).getPrice();
                    spitList.add(itemDTO);
                }
            }
            if (j == allList.size() - 1 && sum.compareTo(limit) <= 0) {
                map.put(i, spitList);
            }

        }
        return map;
    }

    /**
     * 校验重复开票的订单及明细, 订单中的sku
     * @param financialInvoiceUserDTO 订单信息
     * @return 是否重复
     */
    private boolean checkRepeatInvoice(FinancialInvoiceUserDTO financialInvoiceUserDTO) {
        if (CollectionUtils.isEmpty(financialInvoiceUserDTO.getOrderNoList())) {
            return false;
        }
        List<OrderAndItemDTO> orderAndItemDTOList = ordersMapper.selectOrderAndItemList(financialInvoiceUserDTO.getOrderNoList());

        orderAndItemDTOList = orderAndItemDTOList.stream()
                .filter(o -> ObjectUtil.notEqual(InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal(),o.getInvoiceStatus()))
                .collect(Collectors.toList());
        return CollectionUtil.isNotEmpty(orderAndItemDTOList);
    }

    /**
     * 获取订单信息,并拼装成百旺所需格式
     * @param financialInvoiceUserDTO 订单信息
     * @return 是否包含免税品
     */
    public AjaxResult getInvoiceProductInfo(FinancialInvoiceUserDTO financialInvoiceUserDTO, FinancialInvoiceMoneyVO financialInvoiceMoney,
                                            List<BwInvoiceProduct> bwInvoiceProductList) {
        // 订单明细数据
        List<FinancialInvoiceOrderItemDTO> orderItemResultList = financialInvoiceUserDTO.getOrderItemResultList();

        // 根据sku分组
        Map<String, List<FinancialInvoiceOrderItemDTO>> skuListMap = orderItemResultList.stream()
                .collect(Collectors.groupingBy(FinancialInvoiceOrderItemDTO::getSkuPriceGroup));
        Set<Map.Entry<String, List<FinancialInvoiceOrderItemDTO>>> entries = skuListMap.entrySet();

        // 未配置税率的sku
        Set<String> skuSet = new HashSet<>();

        // 是否包含精准送 sku
        boolean isCotainTimeFrameSku = false;

        // 精准送税率配置
        TaxRateConfigBO timeFrameTaxRateConfig = financeTaxRateConfig.getConfig(FinanceTaxRateConfig.BizType.DIGITAL_TIME_FRAME);
        for (Map.Entry<String, List<FinancialInvoiceOrderItemDTO>> stringListEntry : entries) {
            String sku = stringListEntry.getKey().split("_")[0];
            // 该sku下的所有明细
            List<FinancialInvoiceOrderItemDTO> orderItemList = stringListEntry.getValue();
            FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO =  orderItemList.get(0);
                // 该对象内仅有同一sku订单明细内的相同内容可用
                Integer categoryId = financialInvoiceOrderItemDTO.getCategoryId();

                // 构造百旺商品清单对象
                BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
                String pdName = financialInvoiceOrderItemDTO.getPdName();
                String taxRateCode;
                BigDecimal taxRateValue;
                // 精准送单独计算
                if (TIME_FRAME_FEE_SKU.equals(sku)) {
                    pdName = timeFrameTaxRateConfig.getProductName();
                    taxRateCode = timeFrameTaxRateConfig.getTaxRateCode();
                    taxRateValue = new BigDecimal(timeFrameTaxRateConfig.getTaxRate());
//                    pdName = "运输服务费-精准送";
//                    taxRateCode = "3010102020100000000";
//                    taxRateValue = new BigDecimal("0.13");
                    isCotainTimeFrameSku = true;
                }else {
                    // 其他sku税率和税收编码
                    TaxRateConfig taxRateConfig = this.getTaxRateConfig(sku, categoryId);
                    if (Objects.isNull(taxRateConfig.getTaxRateValue()) || Objects.isNull(taxRateConfig.getTaxRateCode())) {
                        logger.error("没有税率和税收编码:{},{}", sku, categoryId);
                        skuSet.add(sku);
                    }
                    taxRateCode = taxRateConfig.getTaxRateCode();
                    taxRateValue = taxRateConfig.getTaxRateValue();
                }

                bwInvoiceProduct.setSpmc(pdName);
                bwInvoiceProduct.setSpbm(taxRateCode);

                // 金额(默认为含税金额)
                BigDecimal price = orderItemList.stream().map(FinancialInvoiceOrderItemDTO::getTotalPrice)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);                // 金额为0不开票
                if (BigDecimal.ZERO.compareTo(price) == 0) {
                    logger.info("sku:{},金额为0,不计入百旺开票商品清单,涉及信息:{}", sku, financialInvoiceUserDTO);
                    continue;
                }

                this.dealWithBwProductInfo(orderItemList, bwInvoiceProduct, price, financialInvoiceUserDTO.getRedFlushTag());

                BigDecimal taxRate = taxRateValue.divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP);
                this.calculateTaxAmount(bwInvoiceProduct, price, taxRate, financialInvoiceUserDTO);
                if (BigDecimal.ZERO.compareTo(taxRate) == 0) {
                    BwFinancialInvoiceBuilder.buildBwDutyFreeGoodInvoiceProduct(bwInvoiceProduct);
                }
                logger.info("商品组装信息:{}", JSON.toJSONString(bwInvoiceProduct));
                bwInvoiceProductList.add(bwInvoiceProduct);
            }
       // }


        if (CollectionUtil.isNotEmpty(skuSet)) {
            return AjaxResult.getErrorWithMsg("订单中以下sku未配置税率，请联系品类运营进行配置" + skuSet);
        }

        // 运费单独计一行 开票运费 = 实付运费 + 运费调整金额 + 运费退款金额（负数）
        BigDecimal deliveryFee = financialInvoiceMoney.getDeliveryFee()
                .add(financialInvoiceMoney.getAdjustDeliveryFeeTotalPrice())
                .add(financialInvoiceMoney.getDeliveryPreferentialAmount());
        if (financialInvoiceMoney.getRefundDeliveryFee() != null) {
            deliveryFee = deliveryFee.subtract(financialInvoiceMoney.getRefundDeliveryFee());
        }

        if(BigDecimal.ZERO.compareTo(deliveryFee) < 0){
            TaxRateConfigBO deliveryTaxRateConfig = financeTaxRateConfig.getConfig(FinanceTaxRateConfig.BizType.DIGITAL_DELIVERY);
            BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
            bwInvoiceProduct.setSpmc(deliveryTaxRateConfig.getProductName());
            bwInvoiceProduct.setSpbm(deliveryTaxRateConfig.getTaxRateCode());
            BigDecimal taxRate = new BigDecimal(deliveryTaxRateConfig.getTaxRate());
//            bwInvoiceProduct.setSpmc("运输服务费-配送费");
//            bwInvoiceProduct.setSpbm("3010102020100000000");
//            BigDecimal taxRate = new BigDecimal("0.13");
            this.calculateTaxAmount(bwInvoiceProduct, deliveryFee, taxRate, financialInvoiceUserDTO);
            bwInvoiceProductList.add(bwInvoiceProduct);
        }

        // 精准送金额
        BigDecimal timeFrameFee = financialInvoiceMoney.getTimeFrameFee();
        if (!isCotainTimeFrameSku && BigDecimal.ZERO.compareTo(timeFrameFee) < 0) {
            BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
            bwInvoiceProduct.setSpmc(timeFrameTaxRateConfig.getProductName());
            bwInvoiceProduct.setSpbm(timeFrameTaxRateConfig.getTaxRateCode());
            BigDecimal taxRate = new BigDecimal(timeFrameTaxRateConfig.getTaxRate());
//            bwInvoiceProduct.setSpmc("运输服务费-精准送");
//            bwInvoiceProduct.setSpbm("3010102020100000000");
//            BigDecimal taxRate = new BigDecimal("0.13");
            this.calculateTaxAmount(bwInvoiceProduct, timeFrameFee, taxRate, financialInvoiceUserDTO);
            bwInvoiceProductList.add(bwInvoiceProduct);

        }

        // 超时加单单独计一行
        BigDecimal outTimesFee = financialInvoiceMoney.getOutTimesFee();
        if(BigDecimal.ZERO.compareTo(outTimesFee) < 0){
            BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
            bwInvoiceProduct.setSpmc("其他现代服务-超时加单");
            bwInvoiceProduct.setSpbm("3049900000000000000");
            BigDecimal taxRate = new BigDecimal("0.06");
            this.calculateTaxAmount(bwInvoiceProduct, outTimesFee, taxRate, financialInvoiceUserDTO);
            bwInvoiceProductList.add(bwInvoiceProduct);
        }

        // 税收编码简称填充
        populateTaxRateCodeName(bwInvoiceProductList);

        financialInvoiceUserDTO.setAmountExcludingTax(financialInvoiceUserDTO.getAllMoney().subtract(financialInvoiceUserDTO.getTotalTaxMoney()));
        return AjaxResult.getOK();
    }

    /**
     * 填充税收编码简称（数电票所需）
     * @param bwInvoiceProductList
     */
    private void populateTaxRateCodeName(List<BwInvoiceProduct> bwInvoiceProductList) {
        Set<String> taxRateCodeSet = bwInvoiceProductList.stream().map(BwInvoiceProduct::getSpbm).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(taxRateCodeSet)) {
            return;
        }
        List<TaxRateCodeNameMapping> taxRateCodeNameMappings = taxRateCodeNameMappingMapper.selectByCodes(taxRateCodeSet);
        Map<String, String> taxRateCodeNameMappingMap = taxRateCodeNameMappings.stream().collect(Collectors.toMap(TaxRateCodeNameMapping::getTaxRateCode, TaxRateCodeNameMapping::getShortName, (v1, v2) -> v1));
        bwInvoiceProductList.forEach(el -> el.setShbmjc(taxRateCodeNameMappingMap.get(el.getSpbm())));
    }

    /**
     * 处理发票商品规格等信息
     * @param orderItemList 商品清单
     * @param bwInvoiceProduct 百旺商品对象
     */
    private void dealWithBwProductInfo(List<FinancialInvoiceOrderItemDTO> orderItemList, BwInvoiceProduct bwInvoiceProduct
                                        ,BigDecimal price,Integer redFlushTag) {
        FinancialInvoiceOrderItemDTO orderItemDTO = orderItemList.get(NumberUtils.INTEGER_ZERO);
        if (orderItemDTO == null){
            logger.error("开票订单找不到orderItemDTO 操作{} orderItemDTO:{} ", redFlushTag, orderItemList);
            return;
        }
        String weigh = Objects.isNull(orderItemDTO.getWeigh()) ? "" : orderItemDTO.getWeigh().split("/")[0];
        bwInvoiceProduct.setGgxh(cleanWeighStr(weigh));
        Inventory selectKeys = new Inventory();
        selectKeys.setSku(orderItemDTO.getSku());
        Inventory inventory = inventoryMapper.selectOne(selectKeys);
        Optional.ofNullable(inventory).ifPresent(i -> bwInvoiceProduct.setDw(inventory.getUnit()));

        int amount = orderItemList.stream()
                .mapToInt(FinancialInvoiceOrderItemDTO::getAmount)
                .sum();
        if(amount <= 0){
            throw new DefaultServiceException("sku数量错误:" + orderItemDTO.getSku());
        }
        BigDecimal divide = price.divide(BigDecimal.valueOf(amount),6, RoundingMode.HALF_UP);

        if(Objects.equals(FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal(),redFlushTag)){
            amount = -amount;
        }
        bwInvoiceProduct.setSpsl(String.valueOf(amount));
        bwInvoiceProduct.setDj(divide.toPlainString());
    }

    /**
     * @param bwInvoiceProduct 商品清单对象
     * @param allMoney         含税金额
     * @param taxRate          税率
     * @param bigFinancialSkuVO 发票金额对象
     * @return 税额
     */
    public void calculateTaxAmount(BwInvoiceProduct bwInvoiceProduct, BigDecimal allMoney, BigDecimal taxRate,
                                   BigFinancialSkuVO bigFinancialSkuVO){
        if(Objects.equals(FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal(), bigFinancialSkuVO.getRedFlushTag())){
            allMoney = allMoney.negate();
        }
        bwInvoiceProduct.setSl(taxRate.toPlainString());
        bwInvoiceProduct.setJe(allMoney.toPlainString());
        // 税额 = 含税金额除以(一加税率) 乘以 税率
        BigDecimal taxMoney = allMoney.multiply(taxRate).divide(BigDecimal.ONE.add(taxRate),2, RoundingMode.HALF_UP);
        bwInvoiceProduct.setSe(taxMoney.toPlainString());

        // 记录总的金额,税额,价税合计
        bigFinancialSkuVO.setTotalTaxMoney(taxMoney.add(bigFinancialSkuVO.getTotalTaxMoney() == null ? BigDecimal.ZERO : bigFinancialSkuVO.getTotalTaxMoney()));
        bigFinancialSkuVO.setAllMoney(allMoney.add(bigFinancialSkuVO.getAllMoney() == null ? BigDecimal.ZERO : bigFinancialSkuVO.getAllMoney()));
    }
    /**
     * @param bwInvoiceProduct 商品清单对象
     * @param allMoney         含税金额
     * @param taxRate          税率
     * @param financialInvoiceUserDTO 发票金额对象
     * @return 税额
     */
    public void calculateTaxAmount(BwInvoiceProduct bwInvoiceProduct, BigDecimal allMoney, BigDecimal taxRate,
                                   FinancialInvoiceUserDTO financialInvoiceUserDTO){
        if(Objects.equals(FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal(),financialInvoiceUserDTO.getRedFlushTag())){
            allMoney = allMoney.negate();
        }
        bwInvoiceProduct.setSl(taxRate.toPlainString());
        bwInvoiceProduct.setJe(allMoney.toPlainString());
        // 税额 = 含税金额除以(一加税率) 乘以 税率
        BigDecimal taxMoney = allMoney.multiply(taxRate).divide(BigDecimal.ONE.add(taxRate),2, RoundingMode.HALF_UP);
        bwInvoiceProduct.setSe(taxMoney.toPlainString());

        // 记录总的金额,税额,价税合计
        financialInvoiceUserDTO.setTotalTaxMoney(taxMoney.add(financialInvoiceUserDTO.getTotalTaxMoney()));
        financialInvoiceUserDTO.setAllMoney(allMoney.add(financialInvoiceUserDTO.getAllMoney()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult billInvoice(FinancialInvoiceVO financialInvoiceVO) {
        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoiceVO.getInvoiceId());
        if(Objects.isNull(invoiceConfig)){
            return AjaxResult.getErrorWithMsg("不存在对应id的对应的配置,请重新选择订单");
        }

        // 自动转数电票（原票据类型已经失效）
        financialInvoiceVO.setInvoiceType(FinancialInvoiceEnum.invoiceType.transfer2Digital(financialInvoiceVO.getInvoiceType()));

        // 判断可以开票的账单订单的发起金额总计
        FinancialOrderQuery financialOrderQuery = new FinancialOrderQuery();
        financialOrderQuery.setOrderNoList(financialInvoiceVO.getOrderNoList());
        financialOrderQuery.setInvoiceStatus(InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal());
        List<FinancialOrderVO> financialOrderVOList = ordersMapper.selectFinancialOrders(financialOrderQuery);
        if(CollectionUtils.isEmpty(financialOrderVOList)){
            return AjaxResult.getErrorWithMsg("未找到账单关联的订单，请刷新重试");
        }
        String billNumber = financialOrderVOList.get(0).getBillNumber();
        Set<String> skuSet = new HashSet<>();
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        FinancialInvoiceUserDTO dutyFreeGoodUserDTO = new FinancialInvoiceUserDTO();
        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
        financialInvoiceUserDTO.setBillNumber(billNumber);
        financialInvoiceUserDTO.setSellingEntityName(financialOrderVOList.get(0).getSellingEntityName());
        dutyFreeGoodUserDTO.setInvoiceConfig(invoiceConfig);
        dutyFreeGoodUserDTO.setBillNumber(billNumber);
        dutyFreeGoodUserDTO.setSellingEntityName(financialOrderVOList.get(0).getSellingEntityName());

        List<Long> orderItemIdList = new ArrayList<>();
        Set<String> orderNoList = new HashSet<>(financialOrderVOList.size());

        // 免税品订单
        List<Long> dutyFreeGoodOrderItemIdList = new ArrayList<>();
        Set<String> dutyFreeGoodOrderNoList = new HashSet<>(financialOrderVOList.size());

        //订单层循环
        for (FinancialOrderVO financialOrderVO : financialOrderVOList) {
            this.dealWithFinancialOrderVO(false, financialOrderVO);

            List<FinancialOrderItemVO> financialOrderItemList = financialOrderVO.getFinancialOrderItemList();
            List<Long> dutyFreeGoodItemIdList = new ArrayList<>();
            List<Long> itemIdList = new ArrayList<>();
            // 订单详情层循环
            for (FinancialOrderItemVO financialOrderItemVO : financialOrderItemList) {
                // 无税率或税收编码的,不能开票
                boolean isNotTax = Objects.isNull(financialOrderItemVO.getTaxRateValue()) || Objects.isNull(financialOrderItemVO.getTaxRateCode());
                if(isNotTax){
                    skuSet.add(financialOrderItemVO.getSku());
                    continue;
                }
                // 专票筛选免税品
                boolean isDutyFreeGood = FinancialInvoiceEnum.invoiceType.isSpecial(financialInvoiceVO.getInvoiceType())
                        && BigDecimal.ZERO.compareTo(financialOrderItemVO.getTaxRateValue()) == 0;
                if(isDutyFreeGood){
                    dutyFreeGoodItemIdList.add(financialOrderItemVO.getOrderItemId());
                    dutyFreeGoodOrderNoList.add(financialOrderVO.getOrderNo());
                }else {
                    itemIdList.add(financialOrderItemVO.getOrderItemId());
                    orderNoList.add(financialOrderVO.getOrderNo());
                }
            }
            dutyFreeGoodOrderItemIdList.addAll(dutyFreeGoodItemIdList);
            orderItemIdList.addAll(itemIdList);

            FinancialInvoiceOrderDTO invoiceOrderDTO = new FinancialInvoiceOrderDTO();
            FinancialInvoiceOrderDTO dutyFreeGoodInvoiceOrderDTO = new FinancialInvoiceOrderDTO();

            // 处理订单商品仅包含免税品,但订单有运费或超时加单费的场景
            boolean isAllDutyFreeGood = Objects.equals(dutyFreeGoodItemIdList.size(),financialOrderItemList.size())
                    && (BigDecimal.ZERO.compareTo(financialOrderVO.getDeliveryFee()) < 0
                    || BigDecimal.ZERO.compareTo(financialOrderVO.getOutTimesFee()) < 0 );
            if(isAllDutyFreeGood) {
                invoiceOrderDTO.setOrderNo(financialOrderVO.getOrderNo());
                orderNoList.add(financialOrderVO.getOrderNo());
            }

            if(CollectionUtil.isNotEmpty(dutyFreeGoodItemIdList)){
                dutyFreeGoodInvoiceOrderDTO.setOrderNo(financialOrderVO.getOrderNo());
                dutyFreeGoodInvoiceOrderDTO.setOrderItemIdList(dutyFreeGoodItemIdList);
                dutyFreeGoodUserDTO.getFinancialInvoiceOrderDTOList().add(dutyFreeGoodInvoiceOrderDTO);
            }

            if(CollectionUtil.isNotEmpty(itemIdList) || Objects.nonNull(invoiceOrderDTO.getOrderNo())){
                invoiceOrderDTO.setOrderNo(financialOrderVO.getOrderNo());
                invoiceOrderDTO.setOrderItemIdList(itemIdList);
                financialInvoiceUserDTO.getFinancialInvoiceOrderDTOList().add(invoiceOrderDTO);
            }
        }

        if(CollectionUtil.isNotEmpty(skuSet)){
            return AjaxResult.getErrorWithMsg("订单中以下sku未配置税率，请联系品类运营进行配置" + skuSet);
        }

        if(CollectionUtil.isNotEmpty(dutyFreeGoodOrderItemIdList)){
            dutyFreeGoodUserDTO.setInvoiceId(financialInvoiceVO.getInvoiceId());
            dutyFreeGoodUserDTO.setOrderNoList(new ArrayList<>(dutyFreeGoodOrderNoList));
            dutyFreeGoodUserDTO.setOrderItemIdList(dutyFreeGoodOrderItemIdList);
            Integer invoiceType = financialInvoiceVO.getInvoiceType();
            dutyFreeGoodUserDTO.setInvoiceType(financialInvoiceVO.getInvoiceType());
            // 专票这里需要切普票
            if (FinancialInvoiceEnum.invoiceType.isSpecial(invoiceType)){
                Integer generalInvoiceType = invoiceType == FinancialInvoiceEnum.invoiceType.VAT.ordinal()
                        ? FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal()
                        : FinancialInvoiceEnum.invoiceType.DIGITAL_GENERAL.ordinal();
                dutyFreeGoodUserDTO.setInvoiceType(generalInvoiceType);
            }
            dutyFreeGoodUserDTO.setDutyFreeGood(FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD.ordinal());
            dutyFreeGoodUserDTO.setFinanceOrderId(financialInvoiceVO.getFinanceAccountId());
            AjaxResult result = this.creatBillInvoice(dutyFreeGoodUserDTO);
            if(!result.isSuccess()){
                throw new net.xianmu.common.exception.BizException(result.getMsg());
            }
        }

        if(CollectionUtil.isNotEmpty(orderNoList)){
            financialInvoiceUserDTO.setFinanceOrderId(financialInvoiceVO.getFinanceAccountId());
            financialInvoiceUserDTO.setInvoiceType(financialInvoiceVO.getInvoiceType());
            financialInvoiceUserDTO.setInvoiceId(financialInvoiceVO.getInvoiceId());
            financialInvoiceUserDTO.setOrderNoList(new ArrayList<>(orderNoList));
            financialInvoiceUserDTO.setOrderItemIdList(orderItemIdList);
            financialInvoiceUserDTO.setDutyFreeGood(FinancialInvoiceEnum.dutyFreeGood.NOT_DUTY_FREE_GOOD.ordinal());
            AjaxResult result = this.creatBillInvoice(financialInvoiceUserDTO);
            if (!result.isSuccess()){
                throw new net.xianmu.common.exception.BizException(result.getMsg());
            }
        }
        return AjaxResult.getOK();

    }


    /**
     * 创建 账单待开票的发票信息
     * @param financialInvoiceUserDTO 发票信息对象
     */
    private AjaxResult creatBillInvoice(FinancialInvoiceUserDTO financialInvoiceUserDTO) {
        AjaxResult result = this.creatInvoice(financialInvoiceUserDTO, null, InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING);
        if (!result.isSuccess()) {
            return result;
        }

        if(Objects.equals(FinancialInvoiceEnum.dutyFreeGood.NOT_DUTY_FREE_GOOD.ordinal(),financialInvoiceUserDTO.getDutyFreeGood())){
            // 不管之前是否被驳回过，重置财务审核状态为未审核，销售申请开票为申请中
            FinanceAccountingPeriodOrder updatePeriodOrder = new FinanceAccountingPeriodOrder();
            updatePeriodOrder.setId(financialInvoiceUserDTO.getFinanceOrderId());
            updatePeriodOrder.setCustomerConfirmStatus(ReceiptCustomerConfirmStatusEnum.CONFIRMED.getId());
            updatePeriodOrder.setFinancialAudit(FinancialAuditEnum.NOT_APPROVED.ordinal());
            updatePeriodOrder.setSalesInvoicing(SalesInvoicingEnum.UNDER_APPLICATION.ordinal());
            updatePeriodOrder.setUpdateTime(LocalDateTime.now());
            updatePeriodOrder.setUpdater(getAdminName());
            financeAccountingPeriodOrderMapper.updateByPrimaryKeySelective(updatePeriodOrder);
        }
        return result;
    }


    @Override
    public AjaxResult cancel(FinancialInvoiceQuery financialInvoiceQuery) {
        //判断是否是拆分的发票
        List<BigFinancialInvoiceSku> bigFinancialInvoiceSkus = bigFinancialInvoiceSkuMapper.selectByFinancialId(financialInvoiceQuery.getId());
        if (CollectionUtils.isEmpty(bigFinancialInvoiceSkus)){
            return cancelFinancialInvoice(financialInvoiceQuery);
        }
        String orderNo = bigFinancialInvoiceSkus.get(0).getOrderNo();
        List<BigFinancialInvoiceSku> bigFinancialInvoiceSkusOrders = bigFinancialInvoiceSkuMapper.selectByOrderNo(orderNo, 1);
        Set<Long> invoiceIds = bigFinancialInvoiceSkusOrders.stream().map(BigFinancialInvoiceSku::getInvoiceId).collect(Collectors.toSet());
        //检查票是否可以废弃
        List<FinancialInvoice> financialInvoices = new ArrayList<>();
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        financialInvoiceUserDTO.setAutoRedFlush(financialInvoiceQuery.isAutoRedFlush());
        financialInvoiceUserDTO.setOrderNo(orderNo);
        for (Long invoiceId : invoiceIds) {
            FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(invoiceId);
            AjaxResult ajaxResult = checkCancelInvoice(financialInvoice, financialInvoiceUserDTO);
            if (ajaxResult != null){
                return ajaxResult;
            }
            financialInvoices.add(financialInvoice);
        }
        financialInvoices.forEach(
                (FinancialInvoice i) ->{
                    FinancialInvoiceUserDTO financialInvoiceUserDTOs = new FinancialInvoiceUserDTO();
                    financialInvoiceUserDTOs.setInvoiceConfig(financialInvoiceUserDTO.getInvoiceConfig());
                    cancelInvoice(i, financialInvoiceUserDTO, true);
                }
        );

        return AjaxResult.getOK(financialInvoiceQuery.getId());
    }



    public AjaxResult cancelFinancialInvoice(FinancialInvoiceQuery financialInvoiceQuery) {
        FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(financialInvoiceQuery.getId());
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        financialInvoiceUserDTO.setAutoRedFlush(financialInvoiceQuery.isAutoRedFlush());
        AjaxResult checkAndCancelInvoice = this.checkAndCancelInvoice(financialInvoice,financialInvoiceUserDTO);
        if(!checkAndCancelInvoice.isSuccess()){
            return checkAndCancelInvoice;
        }

        // 发起专票时若包含免税品，免税品会额外发起一张电票，取消这二者之一，另外一张也需要取消
        this.checkAndCancelDutyFreeGoodInvoice(financialInvoice);

        return AjaxResult.getOK(financialInvoice.getId());
    }


    /**
     * 校验发票有没有一同发起的免税品发票,有的话就取消掉
     * @param financialInvoice 发票信息
     */
    private void checkAndCancelDutyFreeGoodInvoice(FinancialInvoice financialInvoice) {
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        // 电票且包含免税品票标记 有同时发起的专票
        if(FinancialInvoiceEnum.invoiceType.isGeneral(financialInvoice.getInvoiceType())
                && Objects.equals(FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD.ordinal(),financialInvoice.getDutyFreeGood())){
            List<FinancialInvoiceOrdernoRelation> relations = financialInvoiceOrdernoRelationMapper.selectByFinancialInvoiceId(financialInvoice.getId());
            Set<String> orderNoSet = relations.stream()
                    .map(FinancialInvoiceOrdernoRelation::getOrderNo)
                    .collect(Collectors.toSet());

            financialInvoiceUserDTO.setOrderNoList(new ArrayList<>(orderNoSet));
            List<FinancialInvoiceVO> invoiceVOList = financialInvoiceMapper.selectFinancialInvoiceByOrder(financialInvoiceUserDTO);

            FinancialInvoiceVO financialInvoiceVO = invoiceVOList.stream()
                    .filter(i -> FinancialInvoiceEnum.invoiceType.isSpecial(i.getInvoiceType()))
                    .max(Comparator.comparing(FinancialInvoiceVO::getId)).orElse(new FinancialInvoiceVO());
            // 需要同步作废
            if(Objects.deepEquals(FinancialInvoiceEnum.invoiceState.NORMAL.ordinal(),financialInvoiceVO.getInvoiceStatus())){
                logger.info("免税票{}全额红冲,专票{}同步作废",financialInvoice.getId(),financialInvoiceVO.getId());
                this.checkAndCancelInvoice(financialInvoiceVO,financialInvoiceUserDTO);
            }
        }

        if (FinancialInvoiceEnum.invoiceType.isSpecial(financialInvoice.getInvoiceType())) {
            List<FinancialInvoiceOrdernoRelation> relations = financialInvoiceOrdernoRelationMapper.selectByFinancialInvoiceId(financialInvoice.getId());
            Set<String> orderNoSet = relations.stream()
                    .map(FinancialInvoiceOrdernoRelation::getOrderNo)
                    .collect(Collectors.toSet());

            financialInvoiceUserDTO.setOrderNoList(new ArrayList<>(orderNoSet));
            List<FinancialInvoiceVO> invoiceVOList = financialInvoiceMapper.selectFinancialInvoiceByOrder(financialInvoiceUserDTO);

            FinancialInvoiceVO financialInvoiceVO = invoiceVOList.stream()
                    .filter(i -> Objects.equals(FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD.ordinal(),i.getDutyFreeGood()))
                    .filter(i -> FinancialInvoiceEnum.invoiceType.isGeneral(i.getInvoiceType()))
                    .max(Comparator.comparing(FinancialInvoiceVO::getId)).orElse(new FinancialInvoiceVO());
            // 需要同步作废
            if(Objects.deepEquals(FinancialInvoiceEnum.invoiceState.NORMAL.ordinal(),financialInvoiceVO.getInvoiceStatus())){
                logger.info("专票{}作废,附加的免税品电票{}同步作废",financialInvoice.getId(),financialInvoiceVO.getId());
                this.checkAndCancelInvoice(financialInvoiceVO,financialInvoiceUserDTO);
            }
        }
    }

    /**
     * 检验取消发票的正确性,并携带发票抬头信息
     * @param financialInvoice 发票票据信息
     * @param financialInvoiceUserDTO 发票信息对象
     * @return ok
     */
    private AjaxResult checkAndCancelInvoice(FinancialInvoice financialInvoice,FinancialInvoiceUserDTO financialInvoiceUserDTO){
        AjaxResult check = checkCancelInvoice(financialInvoice, financialInvoiceUserDTO);
        if (check != null) {
            return check;
        }
        return this.cancelInvoice(financialInvoice, financialInvoiceUserDTO);
    }

    public AjaxResult checkCancelInvoice(FinancialInvoice financialInvoice, FinancialInvoiceUserDTO financialInvoiceUserDTO){
        if(Objects.isNull(financialInvoice) ){
            return AjaxResult.getErrorWithMsg("该票据不存在,请刷新页面后重试");
        }

        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoice.getInvoiceId());
        if(Objects.isNull(invoiceConfig)){
            return AjaxResult.getErrorWithMsg("该票据开票抬头已不存在,请联系管理员解决");
        }
        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);

        if(Objects.equals(FinancialInvoiceEnum.invoiceState.CANCEL.ordinal(), financialInvoice.getInvoiceStatus())){
            return AjaxResult.getErrorWithMsg("该票据已作废,请勿重复操作");
        }
        if (BigDecimal.ZERO.compareTo(financialInvoice.getAmountMoney()) >= 0) {
            return AjaxResult.getErrorWithMsg("该票据已红冲,请勿重复操作");
        }

        boolean vatNotCancel = (Objects.equals(FinancialInvoiceEnum.invoiceType.VAT.ordinal(), financialInvoice.getInvoiceType())
                || Objects.equals(FinancialInvoiceEnum.invoiceType.DIGITAL_SPECIAL.ordinal(), financialInvoice.getInvoiceType()))
                && (FinancialInvoiceConstant.getVatCancelEndTime().isBefore(financialInvoice.getCreateTime())
                || DateUtil.getFirstDayOfMonth().isAfter(financialInvoice.getCreateTime()));
        if(vatNotCancel){
            return AjaxResult.getErrorWithMsg("专票只能在当月1号0时至最后一天22时作废");
        }
        return null;
    }

    /**
     * 执行作废发票逻辑
     * @param financialInvoice 发票票据信息
     * @param financialInvoiceUserDTO 发票信息对象
     */
    private AjaxResult cancelInvoice(FinancialInvoice financialInvoice, FinancialInvoiceUserDTO financialInvoiceUserDTO) {
      return cancelInvoice(financialInvoice,financialInvoiceUserDTO,false);
    }

    /**
     * 执行作废发票逻辑
     * @param financialInvoice 发票票据信息
     * @param financialInvoiceUserDTO 发票信息对象
     */
    private AjaxResult cancelInvoice(FinancialInvoice financialInvoice, FinancialInvoiceUserDTO financialInvoiceUserDTO, boolean bigFinancialInvoice) {
        //开票失败的不作废
        if (financialInvoice.getInvoiceResult() == 2) {
            return AjaxResult.getOK();
        }
        // 关联的账单状态变更为驳回,开始取消流程便自动驳回,无需等待作废开票结果
        if (Objects.nonNull(financialInvoice.getFinanceOrderId())) {
            FinanceAccountingPeriodOrder financeAccountingPeriodOrder = new FinanceAccountingPeriodOrder();
            financeAccountingPeriodOrder.setId(financialInvoice.getFinanceOrderId());
            financeAccountingPeriodOrder.setSalesInvoicing(SalesInvoicingEnum.REJECT.ordinal());
            financeAccountingPeriodOrderMapper.updateByPrimaryKeySelective(financeAccountingPeriodOrder);
        }

        // 构造数据
        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
        BeanCopyUtil.copyProperties(financialInvoice,financialInvoiceUserDTO);
        financialInvoiceUserDTO.setFinancialInvoiceId(financialInvoice.getId());
        financialInvoiceUserDTO.setInvoiceTime(LocalDateTime.now());
        financialInvoiceUserDTO.setInvoiceType(financialInvoice.getInvoiceType());
        int invoiceState = FinancialInvoiceEnum.invoiceState.FULL_RED_CHARGE.ordinal();
        // 专票发起作废
        if(Objects.equals(FinancialInvoiceEnum.invoiceType.VAT.ordinal(), financialInvoice.getInvoiceType())){
            FinancialInvoiceDataHandleUtil.assemblyBwVatCancelContentBO(financialInvoice,bwInvoiceBO);
            bwInvoiceBO.getContent().setZfr(getAdminName());
            invoiceState = FinancialInvoiceEnum.invoiceState.CANCEL.ordinal();

            this.sendFinancialInvoiceMessage(bwInvoiceBO,financialInvoiceUserDTO.getFinancialInvoiceId(),
                    InvoiceUrlEnum.CANCEL_VAT_INVOICE, MessageType.VAT_CANCEL_INVOICING);
        }

        financialInvoice.setHandlerRemark(getAdminName() + BwInvoiceConstant.CANCEL_REMARK);
        // 生成数电票作废红冲任务
        if (FinancialInvoiceEnum.invoiceType.isDigital(financialInvoice.getInvoiceType())) {
            invoiceState = FinancialInvoiceEnum.invoiceState.CANCEL.ordinal();
            generateInvalidTask(financialInvoice);
            if (financialInvoiceUserDTO.isAutoRedFlush()) {
                financialInvoice.setHandlerRemark(BwInvoiceConstant.AUTO_RED_FLUSH_CANCEL_REMARK);
            }
        }

        financialInvoice.setHandlerId(getAdminId());
        financialInvoice.setHandleTime(LocalDateTime.now());
        financialInvoice.setInvoiceStatus(invoiceState);
        financialInvoiceMapper.updateByPrimaryKeySelective(financialInvoice);


        // 电票发起红冲
        if(Objects.equals(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal(), financialInvoice.getInvoiceType())){
            // 上下文接口约定:PARTIAL_RED_FLUSH 红冲标识
            financialInvoiceUserDTO.setRedFlushTag(FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal());

            if (bigFinancialInvoice) {
                List<BigFinancialInvoiceSku> bigFinancialInvoiceSkus = bigFinancialInvoiceSkuMapper.selectByFinancialId(financialInvoice.getId());
                buildBigFinancialCancel(bigFinancialInvoiceSkus, financialInvoiceUserDTO, FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal());
            } else {
                AjaxResult orderResult = this.getRedFlushInvoiceOrderInfo(financialInvoiceUserDTO);
                if (!orderResult.isSuccess()) {
                    return orderResult;
                }

                // 创建发票
                AjaxResult creatResult = this.creatInvoice(financialInvoiceUserDTO, bwInvoiceBO, InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED);
                if (!creatResult.isSuccess()) {
                    return creatResult;
                }

                // 注意这里的发票id已经变成新生成的红冲票据id
                this.sendFinancialInvoiceMessage(bwInvoiceBO, financialInvoiceUserDTO.getFinancialInvoiceId(),
                        InvoiceUrlEnum.ELECTRONIC_INVOICE, MessageType.INITIATE_INVOICING);
            }
        }

        //更新bigsku票的状态
        if (bigFinancialInvoice){
            //更新状态
            bigFinancialInvoiceSkuMapper.updateStatusByInvoiceId(financialInvoice.getId(), -1);
            //更新订单状态
            Orders order = new Orders();
            order.setOrderNo(financialInvoiceUserDTO.getOrderNo());
            order.setInvoiceStatus(InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal());
            ordersMapper.update(order);
        }

        return AjaxResult.getOK();
    }

    private void generateInvalidTask(FinancialInvoice financialInvoice) {
        // 生成数电票作废红冲任务
        Long financeInvoiceId = financialInvoice.getId();
        FinancialInvoiceAsyncTask blueInvoiceAsyncTask = financialInvoiceAsyncTaskMapper.queryLastByInvoiceIdAndType(financeInvoiceId, FinancialInvoiceAsyncTaskEnum.TaskType.BLUE.getType());
        if (blueInvoiceAsyncTask == null) {
            logger.error("未查询到原蓝字发票任务,发票id:{}", financeInvoiceId, new ProviderException("未查询到原蓝字发票任务，请关注"));
            return;
        }
        // 原蓝票据信息
        String blueInvoiceInvokeParams = blueInvoiceAsyncTask.getInvokeParams();
        BwInvoiceBO blueInvoiceBO = JSON.parseObject(blueInvoiceInvokeParams, BwInvoiceBO.class);
        String blueContent = blueInvoiceBO.getDigitalContent();
        BwContentDigitalBlueInvoiceBO blueContentDigitalInvoiceBO = JSON.parseObject(blueContent, BwContentDigitalBlueInvoiceBO.class);

        // 作废红冲首先要查询红字确认单准备信息
        BwContentDigitalRedInvoicePreparationBO preparationBO = new BwContentDigitalRedInvoicePreparationBO();
        preparationBO.setNsrsbh(blueContentDigitalInvoiceBO.getNsrsbh());
        preparationBO.setGmfnsrsbh(blueContentDigitalInvoiceBO.getGmfnsrsbh());
        preparationBO.setXsfnsrsbh(blueContentDigitalInvoiceBO.getNsrsbh());
        preparationBO.setSqly(BwInvoiceConstant.APPLY_SOURCE);
        preparationBO.setYfphm(financialInvoice.getInvoiceNumber());
        preparationBO.setKprq(cn.hutool.core.date.DateUtil.format(financialInvoice.getInvoiceIssueTime(), "yyyy-MM-dd HH:mm:ss"));
        preparationBO.setLoginName(blueContentDigitalInvoiceBO.getLoginName());
        preparationBO.setQqlsh(BwFinancialInvoiceRequestUtil.createReqMsgId(financeInvoiceId));

        BwInvoiceBO redInvoiceBO = new BwInvoiceBO();
        redInvoiceBO.setBusinessCode(FinancialInvoiceEnum.businessCode.QUERY_RED_INVOICE_PREPARATION.getCode());
        redInvoiceBO.setDigitalContent(JSON.toJSONString(preparationBO));

        // 虽然是作废，本质上是红冲，红冲需要先查询准备单信息
        FinancialInvoiceAsyncTask redInvoiceAsyncTask = new FinancialInvoiceAsyncTask();
        redInvoiceAsyncTask.setType(FinancialInvoiceAsyncTaskEnum.TaskType.INVALID_INVOICE_PREPARATION.getType());
        redInvoiceAsyncTask.setInvoiceId(financialInvoice.getId());
        redInvoiceAsyncTask.setTaskResult(FinancialInvoiceAsyncTaskEnum.Status.INIT.getResult());
        redInvoiceAsyncTask.setInvokeCount(0);
        redInvoiceAsyncTask.setInvokeParams(JSON.toJSONString(redInvoiceBO));
        financialInvoiceAsyncTaskMapper.insert(redInvoiceAsyncTask);
        logger.info("数电发票红字确认单任务插入完毕，发票id:{}，任务id:{}", financeInvoiceId, redInvoiceAsyncTask.getId());
    }


    private void buildBigFinancialCancel(List<BigFinancialInvoiceSku> bigFinancialInvoiceSkus, FinancialInvoiceUserDTO financialInvoiceUserDTO, Integer redFlushTag) {
        List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();
        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
        String batchNo = bigFinancialInvoiceSkus.get(0).getBatchNo();
        BigFinancialSkuVO bigFinancialSkuVO = new BigFinancialSkuVO();
        bigFinancialSkuVO.setBatchNo(batchNo);
        bigFinancialSkuVO.setRedFlushTag(redFlushTag);

        BigFinancialInvoiceSku bigFinancialInvoiceSku = bigFinancialInvoiceSkus.get(0);
        Long invoiceId = bigFinancialInvoiceSku.getInvoiceId();
        BigDecimal totalPrice = BigDecimal.ZERO;
        List<BigFinancialSkuItemVO> itemVOS = bigFinancialInvoiceSkus.stream().map(
                it -> {
                    BigFinancialSkuItemVO bigFinancialSkuItemVO = new BigFinancialSkuItemVO();
                    bigFinancialSkuItemVO.setSku(it.getSku());
                    bigFinancialSkuItemVO.setAmount(it.getOrderSkuAmount());
                    bigFinancialSkuItemVO.setPdName(it.getPdName());
                    bigFinancialSkuItemVO.setPrice(it.getPrice());
                    bigFinancialSkuItemVO.setWeight(it.getWeight());
                    totalPrice.add(it.getPrice().multiply(new BigDecimal(it.getOrderSkuAmount())));
                    return bigFinancialSkuItemVO;
                }
        ).collect(Collectors.toList());
        bigFinancialSkuVO.setList(itemVOS);
        FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(invoiceId);
        //抬头id
        bigFinancialSkuVO.setInvoiceId(financialInvoice.getInvoiceId());
        String index = batchNo.split("_")[2];
        financialInvoiceUserDTO.setOrderNo(batchNo.split("_")[0]);
        bigFinancialSkuVO.setDutyFreeGood(Integer.valueOf(index));
        Integer dutyFreeGood = Integer.valueOf(index);
        List<String> orderNoList = bigFinancialSkuVO.getList().stream().map(BigFinancialSkuItemVO::getOrderNo).collect(Collectors.toList());
        List<Long> orderItemIdList = bigFinancialSkuVO.getList().stream().map(BigFinancialSkuItemVO::getOrderItemId).collect(Collectors.toList());
        financialInvoiceUserDTO.setInvoiceId(invoiceId);
        financialInvoiceUserDTO.setOrderNoList(new ArrayList<>(orderNoList));
        financialInvoiceUserDTO.setOrderItemIdList(orderItemIdList);
        String reqMsgId = BwFinancialInvoiceRequestUtil.createReqMsgId(null);
        financialInvoiceUserDTO.setReqMsgId(reqMsgId);
        financialInvoiceUserDTO.setInvoiceType(financialInvoice.getInvoiceType());
        if (dutyFreeGood == 0) {
            financialInvoiceUserDTO.setDutyFreeGood(FinancialInvoiceEnum.dutyFreeGood.NOT_DUTY_FREE_GOOD.ordinal());
        } else {
            financialInvoiceUserDTO.setDutyFreeGood(FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD.ordinal());
        }
        this.makeBwListCalculateMoney(bigFinancialSkuVO, bwInvoiceProductList);
        financialInvoiceUserDTO.setAdminName(getAdminName());
        BeanCopyUtil.copyProperties(bigFinancialSkuVO, financialInvoiceUserDTO);
        FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBO(bwInvoiceProductList, financialInvoiceUserDTO, bwInvoiceBO);
        FinanceInvoiceExpand baseFinanceInvoiceExpand = FinanceInvoiceExpand.getBaseFinanceInvoiceExpand(totalPrice);
        financialInvoiceUserDTO.setFinanceInvoiceExpand(baseFinanceInvoiceExpand);
        financialInvoiceUserDTO.setInvoiceType(financialInvoice.getInvoiceType());
        financialTransaction.creatInvoice(financialInvoiceUserDTO, bigFinancialSkuVO, InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING);

        InvoiceResultEnum.orderInvoiceStatus orderResultEnum = InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING;
        // 更新原票状态
        FinancialInvoice newFinancialInvoice = new FinancialInvoice();
        newFinancialInvoice.setId(financialInvoice.getId());
        newFinancialInvoice.setInvoiceStatus(orderResultEnum.ordinal());
        financialInvoiceMapper.updateByPrimaryKeySelective(newFinancialInvoice);
        BwRed content = bwInvoiceBO.getContent();
        content.setFplxdm("026");
        content.setQdbz("0");
        this.sendFinancialInvoiceMessage(bwInvoiceBO, financialInvoiceUserDTO.getFinancialInvoiceId(),
                InvoiceUrlEnum.ELECTRONIC_INVOICE, MessageType.INITIATE_INVOICING);
    }




    /**
     * 获取全额红冲关联的订单信息
     * @param financialInvoiceUserDTO 发票信息
     * @return 全额红冲关联的订单信息
     */
    private AjaxResult getRedFlushInvoiceOrderInfo(FinancialInvoiceUserDTO financialInvoiceUserDTO){
        // 获取关联的订单详情
        List<FinancialInvoiceOrdernoRelation> orderNoRelationList = financialInvoiceOrdernoRelationMapper.selectByFinancialInvoiceId(financialInvoiceUserDTO.getFinancialInvoiceId());
        if(CollectionUtil.isEmpty(orderNoRelationList)){
            return AjaxResult.getErrorWithMsg("发票没有关联的订单信息");
        }

        Map<String, List<FinancialInvoiceOrdernoRelation>> listMap = orderNoRelationList.stream()
                .collect(Collectors.groupingBy(FinancialInvoiceOrdernoRelation::getOrderNo));
        List<String> orderNoList = new ArrayList<>();
        List<Long> orderItemIdList = new ArrayList<>();
        List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = new ArrayList<>();

        Set<Map.Entry<String, List<FinancialInvoiceOrdernoRelation>>> entries = listMap.entrySet();
        for (Map.Entry<String, List<FinancialInvoiceOrdernoRelation>> entry : entries) {
            orderNoList.add(entry.getKey());
            List<Long> list = entry.getValue().stream().map(FinancialInvoiceOrdernoRelation::getOrderItemId).collect(Collectors.toList());
            orderItemIdList.addAll(list);
            FinancialInvoiceOrderDTO financialInvoiceOrderDTO = new FinancialInvoiceOrderDTO();
            financialInvoiceOrderDTO.setOrderNo(entry.getKey());
            financialInvoiceOrderDTO.setOrderItemIdList(list);
            financialInvoiceOrderDTOList.add(financialInvoiceOrderDTO);
        }

        financialInvoiceUserDTO.setOrderNoList(orderNoList);
        financialInvoiceUserDTO.setOrderItemIdList(orderItemIdList);
        financialInvoiceUserDTO.setFinancialInvoiceOrderDTOList(financialInvoiceOrderDTOList);

        return AjaxResult.getOK();
    }

    @Async
    @Override
    public void autoRedFlush(String orderNo, String afterSaleOrderNo) {
        logger.info("开始处理售后单:{}自动红冲流程", afterSaleOrderNo);
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        financialInvoiceUserDTO.setOrderNo(orderNo);
        int count = bigFinancialInvoiceSkuMapper.countByOrderNo(orderNo);

        boolean checkAutoRedFlushOrder = count > 0 ? this.checkBigRedFlush(financialInvoiceUserDTO) : this.checkAutoRedFlushOrderAndGetInfo(financialInvoiceUserDTO);
        if (checkAutoRedFlushOrder) {
            AfterSaleOrderQuery afterSaleOrderQuery = new AfterSaleOrderQuery();
            afterSaleOrderQuery.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
            afterSaleOrderQuery.setRefundTag(NumberUtils.INTEGER_ZERO);
            afterSaleOrderQuery.setAfterSaleOrderNo(afterSaleOrderNo);
            List<AfterSaleOrderVO> afterSaleOrderVOList = afterSaleOrderMapper.selectBySelectKeys(afterSaleOrderQuery);
            afterSaleOrderVOList = Optional.ofNullable(afterSaleOrderVOList).orElse(new ArrayList<>());
            // 查询售后详细信息
            if (count > 0) {
                sendBigFinancialDing(orderNo, afterSaleOrderNo, afterSaleOrderVOList);
                return;
            }

            // 数电票部分售后只能整张作废
            Integer invoiceType = financialInvoiceUserDTO.getInvoiceType();
            Long invoiceId = financialInvoiceUserDTO.getInvoiceId();
            // 如果是数电票 不允许部分红冲 只能整张作废
            if (CollectionUtils.isEmpty(afterSaleOrderVOList)) {
                logger.info("售后单:{}非退款类型，自动红冲流程结束", afterSaleOrderNo);
                return;
            }
            if (FinancialInvoiceEnum.invoiceType.isDigital(invoiceType)) {
                logger.info("数电票不允许部分红冲, 只能整张作废:{}", invoiceId);
                processDigitalCancel(financialInvoiceUserDTO);
                return;
            }

            for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOList) {
                // 构造部分红冲票据
                this.creatPartialRedFlushInvoice(afterSaleOrderVO, financialInvoiceUserDTO);
            }
        }
    }

    /**
     * 自动红冲-数电票作废
     * @param financialInvoiceUserDTO
     */
    private void processDigitalCancel(FinancialInvoiceUserDTO financialInvoiceUserDTO) {
        Long invoiceId = financialInvoiceUserDTO.getFinancialInvoiceId();
        xmLockTemplate.executeTryLock(lock -> {//NOSONAR
            if (lock) {
                try {
                    FinancialInvoiceQuery param = new FinancialInvoiceQuery();
                    param.setId(invoiceId);
                    param.setAutoRedFlush(true);
                    AjaxResult cancelResult = cancel(param);
                    if (!cancelResult.isSuccess()) {
                        logger.error("数电票:{}整张作废失败，异常信息:{}，请关注", invoiceId, cancelResult.getMsg(), new ProviderException(cancelResult.getMsg()));
                        return null;
                    }
                    logger.info("数电票:{}整张作废请求成功，等待定时任务调度进行作废...", invoiceId);
                } catch (Exception e) {
                    logger.error("数电票:{}整张作废失败", invoiceId, e);
                }
            } else {
                // 锁占用失败告警
                logger.error("数电票:{}整张作废锁占用失败，可能有其他线程正在处理中，请稍后再试", invoiceId, new ProviderException("数电票整张作废锁占用失败"));
            }
            return null;
        }, FinanceInvoiceRedisKeyEnum.INVALID_LOCK.getKey() + invoiceId, 30000);
    }


    private void bigOrderAtoRedFlush(String orderNo, String afterSaleOrderNo) {
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        financialInvoiceUserDTO.setOrderNo(orderNo);
        // 账单内订单均不红冲
        List<String> billNumList = financeAccountingStoreDetailMapper.selectBillByOrderNo(orderNo);
        if(CollectionUtil.isNotEmpty(billNumList)){
            logger.info("订单具有关联的账单,不自动红冲,orderNo:{}",orderNo);
            return ;
        }

        // 已开票才可以红冲
        Orders orders = ordersMapper.queryByOrderNo(orderNo);
        if(Objects.isNull(orders) || Objects.equals(InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal(),orders.getInvoiceStatus())){
            logger.info("订单不存在或未开票,orderNo:{}",orderNo);
            return ;
        }

        // 校验抬头
        financialInvoiceUserDTO.setOrderNoList(CollectionUtil.toList(orderNo));
        List<FinancialInvoiceVO> invoiceVOList = financialInvoiceMapper.selectFinancialInvoiceByOrder(financialInvoiceUserDTO);
        if(CollectionUtil.isEmpty(invoiceVOList)){
            logger.info("未查询到订单关联的发票,orderNo:{}",orderNo);
            return ;
        }
        //查询相关售后信息
        AfterSaleOrderQuery afterSaleOrderQuery = new AfterSaleOrderQuery();
        afterSaleOrderQuery.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        afterSaleOrderQuery.setRefundTag(NumberUtils.INTEGER_ZERO);
        afterSaleOrderQuery.setAfterSaleOrderNo(afterSaleOrderNo);
        List<AfterSaleOrderVO> afterSaleOrderVOList = afterSaleOrderMapper.selectBySelectKeys(afterSaleOrderQuery);
        afterSaleOrderVOList = Optional.ofNullable(afterSaleOrderVOList).orElse(new ArrayList<>());
        for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOList) {
            creatPartialRedFlushInvoice(afterSaleOrderVO);
        }
    }

    private boolean checkBigRedFlush(FinancialInvoiceUserDTO financialInvoiceUserDTO){
        String orderNo = financialInvoiceUserDTO.getOrderNo();
        // 账单内订单均不红冲
        List<String> billNumList = financeAccountingStoreDetailMapper.selectBillByOrderNo(orderNo);
        if(CollectionUtil.isNotEmpty(billNumList)){
            logger.info("订单具有关联的账单,不自动红冲,orderNo:{}",orderNo);
            return false;
        }

        // 已开票才可以红冲
        Orders orders = ordersMapper.queryByOrderNo(orderNo);
        if(Objects.isNull(orders) || Objects.equals(InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal(),orders.getInvoiceStatus())){
            logger.info("订单不存在或未开票,orderNo:{}",orderNo);
            return false;
        }

        // 校验抬头
        financialInvoiceUserDTO.setOrderNoList(CollectionUtil.toList(orderNo));
        List<FinancialInvoiceVO> invoiceVOList = financialInvoiceMapper.selectFinancialInvoiceByOrder(financialInvoiceUserDTO);
        if(CollectionUtil.isEmpty(invoiceVOList)){
            logger.info("未查询到订单关联的发票,orderNo:{}",orderNo);
            return false;
        }
        return true;
    }
    /**
     * 校验售后自动开具红冲发票的合法性
     * @return true/false
     */
    private boolean checkAutoRedFlushOrderAndGetInfo(FinancialInvoiceUserDTO financialInvoiceUserDTO) {
        String orderNo = financialInvoiceUserDTO.getOrderNo();
        // 账单内订单均不红冲
        List<String> billNumList = financeAccountingStoreDetailMapper.selectBillByOrderNo(orderNo);
        if(CollectionUtil.isNotEmpty(billNumList)){
            logger.info("订单具有关联的账单,不自动红冲,orderNo:{}",orderNo);
            return false;
        }

        // 已开票才可以红冲
        Orders orders = ordersMapper.queryByOrderNo(orderNo);
        if(Objects.isNull(orders) || Objects.equals(InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal(),orders.getInvoiceStatus())){
            logger.info("订单不存在或未开票,orderNo:{}",orderNo);
            return false;
        }

        // 校验抬头
        financialInvoiceUserDTO.setOrderNoList(CollectionUtil.toList(orderNo));
        List<FinancialInvoiceVO> invoiceVOList = financialInvoiceMapper.selectFinancialInvoiceByOrder(financialInvoiceUserDTO);
        if(CollectionUtil.isEmpty(invoiceVOList)){
            logger.info("未查询到订单关联的发票,orderNo:{}",orderNo);
            return false;
        }

        // 获取对应的正数电票, 正数电票只能开具全额红冲后再重开, 所以取最近的一张正数电票即为红冲票对应的正数发票
        FinancialInvoiceVO financialInvoiceVO = invoiceVOList.stream()
                .filter(f -> Objects.equals(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal(), f.getInvoiceType())
                        || Objects.equals(FinancialInvoiceEnum.invoiceType.DIGITAL_GENERAL.ordinal(), f.getInvoiceType()))
                .filter(f -> BigDecimal.ZERO.compareTo(f.getAmountMoney()) < 0)
                .max(Comparator.comparing(FinancialInvoiceVO::getId))
                .orElse(new FinancialInvoiceVO());

        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoiceVO.getInvoiceId());
        if(Objects.isNull(invoiceConfig)){
            logger.info("抬头信息不存在:{}",financialInvoiceVO);
            return false;
        }

        // 补充订单数据
        List<Long> orderItemList = invoiceVOList.stream()
                .filter(f -> f.getId().equals(financialInvoiceVO.getId()))
                .map(FinancialInvoiceVO::getOrderItemId)
                .collect(Collectors.toList());

        if(CollectionUtil.isEmpty(orderItemList)){
            logger.info("百旺开票上线前票据,财务线下处理:{}",orderNo);
            return false;
        }

        List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = new ArrayList<>();
        FinancialInvoiceOrderDTO financialInvoiceOrderDTO = new FinancialInvoiceOrderDTO();
        financialInvoiceOrderDTO.setOrderNo(orderNo);
        financialInvoiceOrderDTO.setOrderItemIdList(orderItemList);
        financialInvoiceOrderDTOList.add(financialInvoiceOrderDTO);

        // 构造数据
        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
        financialInvoiceUserDTO.setInvoiceId(financialInvoiceVO.getInvoiceId());
        financialInvoiceUserDTO.setDutyFreeGood(financialInvoiceVO.getDutyFreeGood());
        financialInvoiceUserDTO.setRedFlushTag(FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal());
        financialInvoiceUserDTO.setInvoiceType(financialInvoiceVO.getInvoiceType());
        financialInvoiceUserDTO.setInvoiceCode(financialInvoiceVO.getInvoiceCode());
        financialInvoiceUserDTO.setInvoiceNumber(financialInvoiceVO.getInvoiceNumber());
        financialInvoiceUserDTO.setInvoiceTime(financialInvoiceVO.getHandleTime());
        financialInvoiceUserDTO.setOrderItemIdList(orderItemList);
        financialInvoiceUserDTO.setOrderNoList(Collections.singletonList(orderNo));
        financialInvoiceUserDTO.setFinancialInvoiceOrderDTOList(financialInvoiceOrderDTOList);
        financialInvoiceUserDTO.setFinancialInvoiceId(financialInvoiceVO.getId());

        // 已开票金额
        BigDecimal alreadyAllMoney = invoiceVOList.stream()
                .filter(f -> Objects.equals(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal(), f.getInvoiceType()) ||
                        Objects.equals(FinancialInvoiceEnum.invoiceType.DIGITAL_GENERAL.ordinal(), f.getInvoiceType()))
                .map(FinancialInvoiceVO::getAmountMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        financialInvoiceUserDTO.setAlreadyAllMoney(alreadyAllMoney);
        return true;
    }

    /**
     * 大订单红冲
     * @param afterSaleOrderVO
     */
    private void creatPartialRedFlushInvoice(AfterSaleOrderVO afterSaleOrderVO){
        Integer handleType = afterSaleOrderVO.getHandleType();
        // 退款类型
        Set<Integer> returnTypeSet = BwFinancialInvoiceBuilder.buildReturnTypeSet();
        // 退运费类型
        Set<Integer> returnShippingTypeSet = BwFinancialInvoiceBuilder.buildReturnShippingTypeSet();
        if (!returnTypeSet.contains(handleType) && !returnShippingTypeSet.contains(handleType)){
            logger.error("错误的退款类型 {} , 售后信息 {}", handleType, JSONUtil.toJsonStr(afterSaleOrderVO));
            return;
        }
        String orderNo  = afterSaleOrderVO.getOrderNo();
        //运费sku单独处理
//        if (returnShippingTypeSet.contains(handleType)){
//           redFlushFreight(afterSaleOrderVO);
//           return;
//        }
        //全部退款的逻辑 类似于作废的逻辑
        if (Objects.isNull(afterSaleOrderVO.getSku())) {
            List<BigFinancialInvoiceSku> allBigFinancialInvoiceSkus = bigFinancialInvoiceSkuMapper.selectByOrderNo(orderNo, 1);
            Map<String, List<BigFinancialInvoiceSku>> map = CheckHelper.groupByIfNotNull(allBigFinancialInvoiceSkus, BigFinancialInvoiceSku::getBatchNo);
            Set<String> strings = map.keySet();
            strings.forEach(bachNo -> {
                FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
                //
                List<BigFinancialInvoiceSku> bigFinancialInvoiceSkus = map.get(bachNo);
                Long invoiceId = bigFinancialInvoiceSkus.get(0).getInvoiceId();
                FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(invoiceId);
                BeanCopyUtil.copyProperties(financialInvoice, financialInvoiceUserDTO);
                buildBigFinancialCancel(bigFinancialInvoiceSkus, financialInvoiceUserDTO, FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal());
            });
            return;
        }
       // redFlushSku(afterSaleOrderVO);
        //退单个sku的逻辑
    }

//    private void redFlushFreight(AfterSaleOrderVO afterSaleOrderVO) {
//        List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();
//        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
//        String orderNo = afterSaleOrderVO.getOrderNo();
//        BigDecimal price = afterSaleOrderVO.getHandleNum();
//        BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
//        bwInvoiceProduct.setSpmc("运输服务费-配送费");
//        bwInvoiceProduct.setSpbm("3010102020100000000");
//        BigDecimal taxRate = new BigDecimal("0.13");
//        //找到有邮费的订单信息
//        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
//        financialInvoiceUserDTO.setOrderNoList(CollectionUtil.toList(orderNo));
//        List<Long> financialIds = bigFinancialInvoiceSkuMapper.selectFinancialIdByOrderSku(afterSaleOrderVO.getOrderNo(), TIME_FRAME_FEE_SKU);
//        if (CollectionUtils.isEmpty(financialIds)){
//            logger.error("退款退运费找不到运费信息  {} , 售后信息 {}", 1, JSONUtil.toJsonStr(afterSaleOrderVO));
//        }
//        List<FinancialInvoiceVO> invoiceVOList = financialInvoiceMapper.selectFinancialInvoiceByOrder(financialInvoiceUserDTO);
//        FinancialInvoiceVO financialInvoiceVO = invoiceVOList.stream()
//                .filter(f -> Objects.equals(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal(), f.getInvoiceType()))
//                .filter(f -> BigDecimal.ZERO.compareTo(f.getAmountMoney()) < 0)
//                .filter(f-> financialIds.contains(f.getId())).findAny().orElseThrow(()->new DefaultServiceException("退款退运费找不到运费信息"));
//        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoiceVO.getInvoiceId());
//        if(Objects.isNull(invoiceConfig)){
//            logger.info("抬头信息不存在:{}",financialInvoiceVO);
//        }
//        Long financialInvoiceId = financialInvoiceVO.getId();
//        // 补充订单数据
//        List<Long> orderItemList = invoiceVOList.stream()
//                .filter(f -> f.getId().equals(financialInvoiceVO.getId()))
//                .map(FinancialInvoiceVO::getOrderItemId)
//                .collect(Collectors.toList());
//
//        if(CollectionUtil.isEmpty(orderItemList)){
//            logger.info("百旺开票上线前票据,财务线下处理:{}",orderNo);
//        }
//
//        List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = new ArrayList<>();
//        FinancialInvoiceOrderDTO financialInvoiceOrderDTO = new FinancialInvoiceOrderDTO();
//        financialInvoiceOrderDTO.setOrderNo(orderNo);
//        financialInvoiceOrderDTO.setOrderItemIdList(orderItemList);
//        financialInvoiceOrderDTOList.add(financialInvoiceOrderDTO);
//
//        // 构造数据
//        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
//        financialInvoiceUserDTO.setInvoiceId(financialInvoiceVO.getInvoiceId());
//        financialInvoiceUserDTO.setDutyFreeGood(financialInvoiceVO.getDutyFreeGood());
//        financialInvoiceUserDTO.setRedFlushTag(FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal());
//        financialInvoiceUserDTO.setInvoiceType(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal());
//        financialInvoiceUserDTO.setInvoiceCode(financialInvoiceVO.getInvoiceCode());
//        financialInvoiceUserDTO.setInvoiceNumber(financialInvoiceVO.getInvoiceNumber());
//        financialInvoiceUserDTO.setInvoiceTime(financialInvoiceVO.getHandleTime());
//        financialInvoiceUserDTO.setOrderItemIdList(orderItemList);
//        financialInvoiceUserDTO.setOrderNoList(Collections.singletonList(orderNo));
//        financialInvoiceUserDTO.setFinancialInvoiceOrderDTOList(financialInvoiceOrderDTOList);
//        financialInvoiceUserDTO.setFinancialInvoiceId(financialInvoiceVO.getId());
//
//        // 已开票金额
//        BigDecimal alreadyAllMoney = invoiceVOList.stream()
//                .filter(f -> Objects.equals(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal(), f.getInvoiceType()))
//                .map(FinancialInvoiceVO::getAmountMoney)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        financialInvoiceUserDTO.setAlreadyAllMoney(alreadyAllMoney);
//        this.calculateTaxAmount(bwInvoiceProduct, price, taxRate, financialInvoiceUserDTO);
//        bwInvoiceProductList.add(bwInvoiceProduct);
//        // 记录总的金额,税额,价税合计
//        BigDecimal subtract = financialInvoiceUserDTO.getAllMoney().subtract(financialInvoiceUserDTO.getTotalTaxMoney());
//        financialInvoiceUserDTO.setAmountExcludingTax(subtract);
//
//        BigDecimal add = financialInvoiceUserDTO.getAlreadyAllMoney().add(price);
//        InvoiceResultEnum.orderInvoiceStatus orderResultEnum = BigDecimal.ZERO.compareTo(add) >= 0
//                ? InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING
//                : InvoiceResultEnum.orderInvoiceStatus.PARTIAL_INVOICING;
//
//        // 更新原票状态
//        FinancialInvoice financialInvoice = new FinancialInvoice();
//        financialInvoice.setId(financialInvoiceId);
//        financialInvoice.setInvoiceStatus(orderResultEnum.ordinal());
//        financialInvoiceMapper.updateByPrimaryKeySelective(financialInvoice);
//
//        // 生成发票信息
//        financialTransaction.creatInvoice(financialInvoiceUserDTO, orderResultEnum);
//
//        // 拼装数据
//        financialInvoiceUserDTO.setAdminName(getAdminName());
//        FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBO(bwInvoiceProductList,financialInvoiceUserDTO,bwInvoiceBO);
//
//        this.sendFinancialInvoiceMessage(bwInvoiceBO,financialInvoiceUserDTO.getFinancialInvoiceId(),
//                InvoiceUrlEnum.ELECTRONIC_INVOICE, MessageType.INITIATE_INVOICING);
//        return;
//    }

//    private void redFlushSku(AfterSaleOrderVO afterSaleOrderVO) {
//        String orderNo = afterSaleOrderVO.getOrderNo();
//        List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();
//        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
//        BigDecimal price = afterSaleOrderVO.getHandleNum();
//        List<OrderAndItemDTO> orderAndItemDTOList = ordersMapper.selectOrderAndItemList(Collections.singletonList(orderNo));
//        if (CollectionUtil.isEmpty(orderAndItemDTOList)) {
//            logger.error("订单不存在orderNo:{}", orderNo);
//            return;
//        }
//        List<OrderItem> orderItems = orderAndItemDTOList
//                .get(NumberUtils.INTEGER_ZERO)
//                .getOrderItemList()
//                .stream()
//                .filter(o -> Objects.equals(o.getSku(), afterSaleOrderVO.getSku()))
//                .collect(Collectors.toList());
//        if (CollectionUtil.isEmpty(orderItems)) {
//            logger.error("未在订单中找到该sku:{},orderNo:{}", afterSaleOrderVO.getSku(), orderNo);
//            return;
//        }
//
//        // 构造百旺商品清单对象
//        OrderItem orderItem = orderItems.get(NumberUtils.INTEGER_ZERO);
//        BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
//
//        // 税率,编码
//        TaxRateConfig taxRateConfig = this.getTaxRateConfig(orderItem.getSku(), orderItem.getCategoryId());
//        if (Objects.isNull(taxRateConfig.getTaxRateValue()) || Objects.isNull(taxRateConfig.getTaxRateCode())) {
//            logger.error("没有税率和税收编码:{},{}", orderItem.getSku(), orderItem.getCategoryId());
//            return;
//        }
//        bwInvoiceProduct.setSpmc(orderItem.getPdName());
//        bwInvoiceProduct.setSpbm(taxRateConfig.getTaxRateCode());
//
//
//        List<BigFinancialInvoiceSku> bigFinancialInvoiceSkus = bigFinancialInvoiceSkuMapper.selectByOrderSku(orderNo, afterSaleOrderVO.getSku());
//        if (CollectionUtils.isEmpty(bigFinancialInvoiceSkus)) {
//            logger.error("找不到大订单成功信息:{},{}", orderItem.getSku(), orderItem.getOrderNo());
//            return;
//        }
//        bigFinancialInvoiceSkus = bigFinancialInvoiceSkus.stream().sorted(Comparator.comparing(BigFinancialInvoiceSku::getNoRedSkuAmount).reversed()).collect(Collectors.toList());
//
//        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
//        financialInvoiceUserDTO.setOrderNoList(CollectionUtil.toList(orderNo));
//        List<FinancialInvoiceVO> invoiceVOList = financialInvoiceMapper.selectFinancialInvoiceByOrder(financialInvoiceUserDTO);
//        //查询
//        List<BigFinancialInvoiceSku> finalBigFinancialInvoiceSkus = bigFinancialInvoiceSkus;
//        FinancialInvoiceVO financialInvoiceVO = invoiceVOList.stream()
//                .filter(f -> Objects.equals(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal(), f.getInvoiceType()))
//                .filter(f -> BigDecimal.ZERO.compareTo(f.getAmountMoney()) < 0)
//                .filter(f -> Objects.equals(finalBigFinancialInvoiceSkus.get(0).getInvoiceId(), f.getId())).findAny().orElseThrow(() -> new DefaultServiceException("退款退运费找不到运费信息"));
//
//
//        // 计算金额
//        BigDecimal taxRate = taxRateConfig.getTaxRateValue().divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP);
//        this.calculateTaxAmount(bwInvoiceProduct, price, taxRate, financialInvoiceUserDTO);
//        if (BigDecimal.ZERO.compareTo(taxRateConfig.getTaxRateValue()) == 0) {
//            BwFinancialInvoiceBuilder.buildBwDutyFreeGoodInvoiceProduct(bwInvoiceProduct);
//        }
//        bwInvoiceProductList.add(bwInvoiceProduct);
//        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoiceVO.getInvoiceId());
//        if (Objects.isNull(invoiceConfig)) {
//            logger.info("抬头信息不存在:{}", financialInvoiceVO);
//        }
//        Long financialInvoiceId = financialInvoiceVO.getId();
//        // 补充订单数据
//        List<Long> orderItemList = invoiceVOList.stream()
//                .filter(f -> f.getId().equals(financialInvoiceVO.getId()))
//                .map(FinancialInvoiceVO::getOrderItemId)
//                .collect(Collectors.toList());
//
//        BigDecimal subtract = financialInvoiceUserDTO.getAllMoney().subtract(financialInvoiceUserDTO.getTotalTaxMoney());
//        financialInvoiceUserDTO.setAmountExcludingTax(subtract);
//        // 退单个sku时,售后红冲票仅仅关联该明细id
//        List<Long> orderItemIds = Collections.singletonList(orderItem.getId());
//        List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = financialInvoiceUserDTO.getFinancialInvoiceOrderDTOList();
//        for (FinancialInvoiceOrderDTO financialInvoiceOrderDTO : financialInvoiceOrderDTOList) {
//            if (Objects.equals(financialInvoiceUserDTO.getOrderNo(), financialInvoiceOrderDTO.getOrderNo())) {
//                financialInvoiceOrderDTO.setOrderItemIdList(orderItemIds);
//            }
//        }
//        FinancialInvoiceOrderDTO financialInvoiceOrderDTO = new FinancialInvoiceOrderDTO();
//        financialInvoiceOrderDTO.setOrderNo(orderNo);
//        financialInvoiceOrderDTO.setOrderItemIdList(orderItemList);
//        financialInvoiceOrderDTOList.add(financialInvoiceOrderDTO);
//
//        // 构造数据
//        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
//        financialInvoiceUserDTO.setInvoiceId(financialInvoiceVO.getInvoiceId());
//        financialInvoiceUserDTO.setDutyFreeGood(financialInvoiceVO.getDutyFreeGood());
//        financialInvoiceUserDTO.setRedFlushTag(FinancialInvoiceEnum.invoiceState.PARTIAL_RED_FLUSH.ordinal());
//        financialInvoiceUserDTO.setInvoiceType(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal());
//        financialInvoiceUserDTO.setInvoiceCode(financialInvoiceVO.getInvoiceCode());
//        financialInvoiceUserDTO.setInvoiceNumber(financialInvoiceVO.getInvoiceNumber());
//        financialInvoiceUserDTO.setInvoiceTime(financialInvoiceVO.getHandleTime());
//        financialInvoiceUserDTO.setOrderItemIdList(orderItemList);
//        financialInvoiceUserDTO.setOrderNoList(Collections.singletonList(orderNo));
//        financialInvoiceUserDTO.setFinancialInvoiceOrderDTOList(financialInvoiceOrderDTOList);
//        financialInvoiceUserDTO.setFinancialInvoiceId(financialInvoiceVO.getId());
//
//        // 已开票金额
//        BigDecimal alreadyAllMoney = invoiceVOList.stream()
//                .filter(f -> Objects.equals(FinancialInvoiceEnum.invoiceType.ELECTRONIC.ordinal(), f.getInvoiceType()))
//                .map(FinancialInvoiceVO::getAmountMoney)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        financialInvoiceUserDTO.setAlreadyAllMoney(alreadyAllMoney);
//        this.calculateTaxAmount(bwInvoiceProduct, price, taxRate, financialInvoiceUserDTO);
//        bwInvoiceProductList.add(bwInvoiceProduct);
//        // 记录总的金额,税额,价税合计
//
//        BigDecimal add = financialInvoiceUserDTO.getAlreadyAllMoney().add(price);
//        InvoiceResultEnum.orderInvoiceStatus orderResultEnum = BigDecimal.ZERO.compareTo(add) >= 0
//                ? InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING
//                : InvoiceResultEnum.orderInvoiceStatus.PARTIAL_INVOICING;
//
//        // 更新原票状态
//        FinancialInvoice financialInvoice = new FinancialInvoice();
//        financialInvoice.setId(financialInvoiceId);
//        financialInvoice.setInvoiceStatus(orderResultEnum.ordinal());
//        financialInvoiceMapper.updateByPrimaryKeySelective(financialInvoice);
//
//
//        BigFinancialInvoiceSku bigFinancialInvoiceSku = bigFinancialInvoiceSkus.get(0);
//        bigFinancialInvoiceSkuMapper.updateRedCountById(bigFinancialInvoiceSku.getId());
//        // 生成发票信息
//        financialTransaction.creatInvoice(financialInvoiceUserDTO, orderResultEnum);
//
//        // 拼装数据
//        financialInvoiceUserDTO.setAdminName(getAdminName());
//        FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBO(bwInvoiceProductList, financialInvoiceUserDTO, bwInvoiceBO);
//
//        this.sendFinancialInvoiceMessage(bwInvoiceBO, financialInvoiceUserDTO.getFinancialInvoiceId(),
//                InvoiceUrlEnum.ELECTRONIC_INVOICE, MessageType.INITIATE_INVOICING);
//    }

        /**
         * 创建因售后退款的部分红冲发票
         * @param afterSaleOrderVO 售后信息
         * @param financialInvoiceUserDTO 发票信息
         */
    private void  creatPartialRedFlushInvoice(AfterSaleOrderVO afterSaleOrderVO, FinancialInvoiceUserDTO financialInvoiceUserDTO){
        Integer handleType = afterSaleOrderVO.getHandleType();
        BigDecimal price = afterSaleOrderVO.getHandleNum();
        // 原票id
        Long financialInvoiceId = financialInvoiceUserDTO.getFinancialInvoiceId();

        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
        List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();

        // 退款类型
        Set<Integer> returnTypeSet = BwFinancialInvoiceBuilder.buildReturnTypeSet();
        // 退运费类型
        Set<Integer> returnShippingTypeSet = BwFinancialInvoiceBuilder.buildReturnShippingTypeSet();
        if(returnTypeSet.contains(handleType)){
            // sku为空,表示整个订单退
            if(Objects.isNull(afterSaleOrderVO.getSku())){
                // 金额信息
                FinancialInvoiceMoneyVO financialInvoiceMoney = new FinancialInvoiceMoneyVO();
                this.getFinancialInvoiceMoney(financialInvoiceUserDTO,financialInvoiceMoney);

                // 获取订单信息
                AjaxResult orderInfoResult = this.getInvoiceProductInfo(financialInvoiceUserDTO, financialInvoiceMoney,
                        bwInvoiceProductList);
                if(!orderInfoResult.isSuccess()){
                    logger.error(orderInfoResult.getMsg());
                    return;
                }
                if(CollectionUtil.isEmpty(bwInvoiceProductList)){
                    logger.error("订单内无有效商品可开票");
                    return;
                }
            }else {
                // 仅仅退单个sku, 金额为售后金额
                List<OrderAndItemDTO> orderAndItemDTOList = ordersMapper.selectOrderAndItemList(financialInvoiceUserDTO.getOrderNoList());
                if(CollectionUtil.isEmpty(orderAndItemDTOList)){
                    logger.error("订单不存在orderNo:{}",financialInvoiceUserDTO.getOrderNo());
                    return;
                }

                List<OrderItem> orderItems = orderAndItemDTOList
                        .get(NumberUtils.INTEGER_ZERO)
                        .getOrderItemList()
                        .stream()
                        .filter(o -> Objects.equals(o.getSku(), afterSaleOrderVO.getSku()))
                        .collect(Collectors.toList());
                if(CollectionUtil.isEmpty(orderItems)){
                    logger.error("未在订单中找到该sku:{},orderNo:{}",afterSaleOrderVO.getSku(),financialInvoiceUserDTO.getOrderNo());
                    return;
                }

                // 构造百旺商品清单对象
                OrderItem orderItem = orderItems.get(NumberUtils.INTEGER_ZERO);
                BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();

                // 税率,编码
                TaxRateConfig taxRateConfig = this.getTaxRateConfig(orderItem.getSku(), orderItem.getCategoryId());
                if(Objects.isNull(taxRateConfig.getTaxRateValue()) || Objects.isNull(taxRateConfig.getTaxRateCode()) ){
                    logger.error("没有税率和税收编码:{},{}",orderItem.getSku(), orderItem.getCategoryId());
                    return;
                }
                bwInvoiceProduct.setSpmc(orderItem.getPdName());
                bwInvoiceProduct.setSpbm(taxRateConfig.getTaxRateCode());

                // 计算金额
                BigDecimal taxRate = taxRateConfig.getTaxRateValue().divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP);
                this.calculateTaxAmount(bwInvoiceProduct, price, taxRate, financialInvoiceUserDTO);
                if(BigDecimal.ZERO.compareTo(taxRateConfig.getTaxRateValue()) == 0){
                    BwFinancialInvoiceBuilder.buildBwDutyFreeGoodInvoiceProduct(bwInvoiceProduct);
                }
                bwInvoiceProductList.add(bwInvoiceProduct);

                BigDecimal subtract = financialInvoiceUserDTO.getAllMoney().subtract(financialInvoiceUserDTO.getTotalTaxMoney());
                financialInvoiceUserDTO.setAmountExcludingTax(subtract);
                // 退单个sku时,售后红冲票仅仅关联该明细id
                List<Long> orderItemIds = Collections.singletonList(orderItem.getId());
                List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = financialInvoiceUserDTO.getFinancialInvoiceOrderDTOList();
                for (FinancialInvoiceOrderDTO financialInvoiceOrderDTO : financialInvoiceOrderDTOList) {
                    if(Objects.equals(financialInvoiceUserDTO.getOrderNo(),financialInvoiceOrderDTO.getOrderNo())){
                        financialInvoiceOrderDTO.setOrderItemIdList(orderItemIds);
                    }
                }
            }
        }else if(returnShippingTypeSet.contains(handleType)){
            BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
            TaxRateConfigBO config = financeTaxRateConfig.getConfig(FinanceTaxRateConfig.BizType.DIGITAL_DELIVERY);
            bwInvoiceProduct.setSpmc(config.getProductName());
            bwInvoiceProduct.setSpbm(config.getTaxRateCode());
            BigDecimal taxRate = new BigDecimal(config.getTaxRate());
//            bwInvoiceProduct.setSpmc("运输服务费-配送费");
//            bwInvoiceProduct.setSpbm("3010102020100000000");
//            BigDecimal taxRate = new BigDecimal("0.13");
            this.calculateTaxAmount(bwInvoiceProduct, price, taxRate, financialInvoiceUserDTO);
            bwInvoiceProductList.add(bwInvoiceProduct);
            // 记录总的金额,税额,价税合计
            BigDecimal subtract = financialInvoiceUserDTO.getAllMoney().subtract(financialInvoiceUserDTO.getTotalTaxMoney());
            financialInvoiceUserDTO.setAmountExcludingTax(subtract);
        }else {
            return;
        }

        // 填充税率编码名称
        populateTaxRateCodeName(bwInvoiceProductList);

        BigDecimal add = financialInvoiceUserDTO.getAlreadyAllMoney().add(price);
        InvoiceResultEnum.orderInvoiceStatus orderResultEnum = BigDecimal.ZERO.compareTo(add) >= 0
                ? InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING
                : InvoiceResultEnum.orderInvoiceStatus.PARTIAL_INVOICING;

        // 更新原票状态
        FinancialInvoice financialInvoice = new FinancialInvoice();
        financialInvoice.setId(financialInvoiceId);
        financialInvoice.setInvoiceStatus(orderResultEnum.ordinal());
        financialInvoiceMapper.updateByPrimaryKeySelective(financialInvoice);

        // 生成发票信息
        financialTransaction.creatInvoice(financialInvoiceUserDTO,orderResultEnum);

        // 拼装数据
        financialInvoiceUserDTO.setAdminName(getAdminName());
//        boolean isDigitalInvoice = FinancialInvoiceEnum.invoiceType.isDigital(financialInvoiceUserDTO.getInvoiceType());
//        if (isDigitalInvoice) {
//            FinancialInvoiceDataHandleUtil.assemblyBwContentDigitalRedInvoiceBO(bwInvoiceProductList,financialInvoiceUserDTO,bwInvoiceBO);
//            // 插入发票异步任务表
//            generateInvoiceAsyncTask(financialInvoiceUserDTO, bwInvoiceBO, FinancialInvoiceAsyncTaskEnum.taskType.RED);
//            return;
//        }

        FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBO(bwInvoiceProductList,financialInvoiceUserDTO,bwInvoiceBO);

        this.sendFinancialInvoiceMessage(bwInvoiceBO,financialInvoiceUserDTO.getFinancialInvoiceId(),
                InvoiceUrlEnum.ELECTRONIC_INVOICE, MessageType.INITIATE_INVOICING);
    }

    @Override
    public void creatAccountInvoice(FinancialInvoice financialInvoice) {
        List<FinancialInvoiceOrdernoRelation> relations = financialInvoiceOrdernoRelationMapper.selectByFinancialInvoiceId(financialInvoice.getId());
        List<String> orderNoList = relations.stream().map(FinancialInvoiceOrdernoRelation::getOrderNo).collect(Collectors.toList());
        List<Long> orderItemIdList = relations.stream().map(FinancialInvoiceOrdernoRelation::getOrderItemId).collect(Collectors.toList());

        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        financialInvoiceUserDTO.setOrderNoList(orderNoList);
        financialInvoiceUserDTO.setOrderItemIdList(orderItemIdList);
        financialInvoiceUserDTO.setInvoiceType(financialInvoice.getInvoiceType());
        financialInvoiceUserDTO.setFinancialInvoiceId(financialInvoice.getId());
        financialInvoiceUserDTO.setReqMsgId(BwFinancialInvoiceRequestUtil.createReqMsgId(null));
        financialInvoiceUserDTO.setAdminName(getAdminName());
        financialInvoiceUserDTO.setDutyFreeGood(financialInvoice.getDutyFreeGood());
        FinanceAccountingPeriodOrder financeAccountingPeriodOrder = financeAccountingPeriodOrderMapper.selectByPrimaryKey(financialInvoice.getFinanceOrderId());
        if (financeAccountingPeriodOrder != null) {
            financialInvoiceUserDTO.setBillNumber(financeAccountingPeriodOrder.getBillNumber());
        }

        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoice.getInvoiceId());
        if(Objects.isNull(invoiceConfig)){
            logger.error("无发票抬头:{}",financialInvoice);
            return;
        }
        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
        financialInvoiceUserDTO.setInvoiceId(financialInvoice.getInvoiceId());

        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
        // 金额信息
        FinancialInvoiceMoneyVO financialInvoiceMoney = new FinancialInvoiceMoneyVO();
        this.getFinancialInvoiceMoney(financialInvoiceUserDTO,financialInvoiceMoney);

        // 获取订单信息
        List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();
        AjaxResult orderInfoResult = this.getInvoiceProductInfo(financialInvoiceUserDTO, financialInvoiceMoney, bwInvoiceProductList);
        if(!orderInfoResult.isSuccess()){
            logger.error(orderInfoResult.getMsg());
            return;
        }
        if(CollectionUtil.isEmpty(bwInvoiceProductList)){
            logger.error("订单内无有效商品可开票");
        }

        // 更新订单状态
        for (String orderNo : orderNoList) {
            Orders updateContent = new Orders();
            updateContent.setInvoiceStatus(InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING.ordinal());
            updateContent.setOrderNo(orderNo);
            ordersMapper.update(updateContent);
        }

        // 拼装数据
//        FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBO(bwInvoiceProductList, financialInvoiceUserDTO,bwInvoiceBO);
        Integer invoiceType = financialInvoice.getInvoiceType();
        boolean isDigitalInvoice = FinancialInvoiceEnum.invoiceType.isDigital(invoiceType);
        FinancialInvoiceSellerInfoDTO sellerInfoDTO = prepareSellingInfoDTO(financialInvoice.getSellingEntityName());
        FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBOFacade(isDigitalInvoice, bwInvoiceProductList, financialInvoiceUserDTO, bwInvoiceBO, sellerInfoDTO);
        if (isDigitalInvoice) {
            // 插入发票异步任务表
            generateInvoiceAsyncTask(financialInvoiceUserDTO, bwInvoiceBO, FinancialInvoiceAsyncTaskEnum.TaskType.BLUE);
            return;
        }

        // 发送mq消息
        InvoiceUrlEnum invoiceUrlEnum = Objects.equals(FinancialInvoiceEnum.invoiceType.VAT.ordinal(), financialInvoiceUserDTO.getInvoiceType()) ?
                InvoiceUrlEnum.VAT_INVOICE : InvoiceUrlEnum.ELECTRONIC_INVOICE;
        this.sendFinancialInvoiceMessage(bwInvoiceBO,financialInvoiceUserDTO.getFinancialInvoiceId(),
                invoiceUrlEnum,MessageType.INITIATE_INVOICING);
    }

    /**
     * 拆分大订单信息
     *
     * @param financialInvoiceInput 查询大订单的信息
     * @return BigFinancialSkuItemVO
     */
    @Override
    public AjaxResult<List<BigFinancialSkuVO>> bigInvoiceOrderList(FinancialInvoiceInput financialInvoiceInput) {
        // 校验抬头
        return bigInvoiceOrderList(financialInvoiceInput, null);
    }
    /**
     * 拆分大订单信息
     *
     * @param financialInvoiceInput 查询大订单的信息
     * @param  invoiceConfig  配置头信息
     * @return BigFinancialSkuItemVO
     */
    public AjaxResult<List<BigFinancialSkuVO>> bigInvoiceOrderList(FinancialInvoiceInput financialInvoiceInput, InvoiceConfig invoiceConfig) {
        // 校验抬头
        if (invoiceConfig == null) {
            invoiceConfig = initInvoiceConfig(financialInvoiceInput);
        }
        if (Objects.isNull(invoiceConfig)) {
            return AjaxResult.getError("不存在该抬头信息,请重新选择");
        }

        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        FinancialInvoiceUserDTO dutyFreeGoodUserDTO = new FinancialInvoiceUserDTO();
        //区分订单是免税单 或者是其他的单子
        AjaxResult ajaxResult = FinancialInvoiceDataHandleUtil.financialInvoiceOrderHandle(financialInvoiceInput, financialInvoiceUserDTO, dutyFreeGoodUserDTO);
        if(!ajaxResult.isSuccess()){
            return ajaxResult;
        }
        financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
        financialInvoiceUserDTO.getOrderItemIdList().addAll(dutyFreeGoodUserDTO.getOrderItemIdList());
        String orderNo = financialInvoiceInput.getFinancialInvoiceOrderDTOList().get(0).getOrderNo();
        List<BigFinancialSkuVO> outs = new ArrayList<>();
        // 金额信息
        FinancialInvoiceMoneyVO financialInvoiceMoney = new FinancialInvoiceMoneyVO();
        this.getFinancialInvoiceMoney(financialInvoiceUserDTO, financialInvoiceMoney);
        List<FinancialInvoiceOrderItemDTO> orderItemResultList = financialInvoiceUserDTO.getOrderItemResultList();
        //非免税品
        if (CollectionUtil.isNotEmpty(financialInvoiceUserDTO.getOrderNoList())) {
           List<FinancialInvoiceOrderItemDTO> resultList = orderItemResultList.stream().filter(it -> financialInvoiceUserDTO.getSkuList().contains(it.getSku())).collect(Collectors.toList());
           financialInvoiceUserDTO.setOrderItemResultList(resultList);
            List<BigFinancialSkuVO> bigFinancialSkuVOS = this.showFinancial(financialInvoiceUserDTO, financialInvoiceMoney ,orderNo, financialInvoiceInput, FinancialInvoiceEnum.dutyFreeGood.NOT_DUTY_FREE_GOOD);
            outs.addAll(bigFinancialSkuVOS);
            financialInvoiceUserDTO.setFinanceInvoiceExpand(null);
            financialInvoiceMoney = new FinancialInvoiceMoneyVO();
        }

        //免税品
        if (CollectionUtil.isNotEmpty(dutyFreeGoodUserDTO.getOrderItemIdList())) {
            List<FinancialInvoiceOrderItemDTO> resultList = orderItemResultList.stream().filter(it -> dutyFreeGoodUserDTO.getSkuList().contains(it.getSku())).collect(Collectors.toList());
            dutyFreeGoodUserDTO.setOrderItemResultList(resultList);
            List<BigFinancialSkuVO> bigFinancialSkuVOS = this.showFinancial(dutyFreeGoodUserDTO, financialInvoiceMoney, orderNo, financialInvoiceInput,FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD);
            outs.addAll(bigFinancialSkuVOS);
        }


        return AjaxResult.getOK(outs);
    }

    @Override
    public AjaxResult bigInvoiceSave(FinancialInvoiceInput financialInvoiceInput) {
        InvoiceConfig invoiceConfig = initInvoiceConfig(financialInvoiceInput);
        if (invoiceConfig == null) {
            return AjaxResult.getError("不存在该抬头信息,请重新选择");
        }
        AjaxResult<List<BigFinancialSkuVO>> listAjaxResult = bigInvoiceOrderList(financialInvoiceInput, invoiceConfig);
        List<BigFinancialSkuVO> skuVOS = new ArrayList<>(listAjaxResult.getData());
        int index = 0;
        for (BigFinancialSkuVO it : skuVOS) {// 拼装数据
            BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
            FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
            it.setInvoiceId(invoiceConfig.getId());
            financialInvoiceUserDTO.setIndex(index);
            // 生成发票信息
            Integer dutyFreeGood = it.getDutyFreeGood();
            List<String> orderNoList = it.getList().stream().map(BigFinancialSkuItemVO::getOrderNo).collect(Collectors.toList());
            List<Long> orderItemIdList = it.getList().stream().map(BigFinancialSkuItemVO::getOrderItemId).collect(Collectors.toList());
            financialInvoiceUserDTO.setInvoiceId(financialInvoiceInput.getInvoiceId());
            financialInvoiceUserDTO.setOrderNoList(new ArrayList<>(orderNoList));
            financialInvoiceUserDTO.setOrderItemIdList(orderItemIdList);
            financialInvoiceUserDTO.setInvoiceConfig(invoiceConfig);
            financialInvoiceUserDTO.setInvoiceId(invoiceConfig.getId());
            financialInvoiceUserDTO.setBelongType(financialInvoiceInput.getBelongType());
            financialInvoiceUserDTO.setOrderNo(orderNoList.get(0));
            financialInvoiceUserDTO.setInvoiceType(financialInvoiceInput.getInvoiceType());
            //不是免税拼装
            if (dutyFreeGood == 0) {
                financialInvoiceUserDTO.setDutyFreeGood(FinancialInvoiceEnum.dutyFreeGood.NOT_DUTY_FREE_GOOD.ordinal());
            } else {
                financialInvoiceUserDTO.setDutyFreeGood(FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD.ordinal());
            }

            List<BwInvoiceProduct> bwInvoiceProductList = new ArrayList<>();
            //  拼装数据
            this.makeBwListCalculateMoney(it, bwInvoiceProductList);
            financialInvoiceUserDTO.setInvoiceId(invoiceConfig.getId());
            financialInvoiceUserDTO.setAdminName(getAdminName());
            BeanCopyUtil.copyProperties(it, financialInvoiceUserDTO);
            FinancialInvoiceDataHandleUtil.assemblyBwContentInvoiceBO(bwInvoiceProductList, financialInvoiceUserDTO, bwInvoiceBO);
            financialInvoiceUserDTO.setOrderNo(it.getBatchNo().split("_")[0]);
            financialInvoiceUserDTO.setOrderItemIdList(financialInvoiceInput.getFinancialInvoiceOrderDTOList().get(0).getOrderItemIdList());
            financialInvoiceUserDTO.setMailAddress(financialInvoiceInput.getPersonMail());
            financialTransaction.creatInvoice(financialInvoiceUserDTO, it, InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING);
            bwInvoiceBO.getContent().setFpqqlsh(financialInvoiceUserDTO.getReqMsgId());
            // 发送mq消息
            InvoiceUrlEnum invoiceUrlEnum = Objects.equals(FinancialInvoiceEnum.invoiceType.VAT.ordinal(), financialInvoiceUserDTO.getInvoiceType()) ?
                    InvoiceUrlEnum.VAT_INVOICE : InvoiceUrlEnum.ELECTRONIC_INVOICE;
            this.sendFinancialInvoiceMessage(bwInvoiceBO, financialInvoiceUserDTO.getFinancialInvoiceId(),
                    invoiceUrlEnum, MessageType.INITIATE_INVOICING, it.getBatchNo());
            index++;
        }
        return AjaxResult.getOK();
    }

    @Override
    public CommonResult<Long> exportInvoice(FinancialInvoiceExportQuery query) {
        //如果根据订单查询，先查询订单情况
        if (Objects.nonNull(query.getOrderNo())) {
            FinancialInvoiceQuery invoiceQuery = new FinancialInvoiceQuery();
            invoiceQuery.setInvoiceResult(query.getInvoiceResult());
            invoiceQuery.setOrderNo(query.getOrderNo());
            invoiceQuery.setInvoiceStatus(query.getInvoiceStatus());
            List<Long> ids = financialInvoiceMapper.selectIdByQuery(invoiceQuery);
            if (ids.isEmpty()) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "当前筛选项没有记录");
            }
            query.setIds(ids);
        }
        String filename = "销项发票-" + LocalDate.now() + UUID.randomUUID()+".xlsx";
        DownloadCenterInitReq req = new DownloadCenterInitReq();
        req.setAdminId(Long.valueOf(getAdminId()));
        req.setFileName(filename);
        req.setBizType(FINANCIAL_INVOICE_DOWNLOAD.getBizType());
        req.setFileExpiredDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY);
        DubboResponse<DownloadCenterResp> response = downloadCenterProvider.initRecord(req);
        if (!response.isSuccess()) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "网络异常，请重试");
        }
        SpringUtil.getBean(FinancialInvoiceService.class).exportInvoiceAsync(query, response.getData().getResId(), filename);
        return CommonResult.ok(response.getData().getResId());
    }

    @Override
    @Async("downloadExecutor")
    public void exportInvoiceAsync(FinancialInvoiceExportQuery query,Long resId,String filename) {
        if(Objects.nonNull(query.getInvoiceQueryEndTime())){
            query.setInvoiceQueryEndTime(query.getInvoiceQueryEndTime().plusDays(NumberUtils.INTEGER_ONE));
        }
        List<FinanceInvoiceExportVo> financeInvoiceExportVos = financialInvoiceMapper.selectExportInvoice(query);
        for (FinanceInvoiceExportVo exportVo:financeInvoiceExportVos){
            if (exportVo.getInvoiceResult()==TO_BE_INVOICED.ordinal()){
                exportVo.setInvoiceResultStr("开票中");
            }else  if (exportVo.getInvoiceResult()==INVOICED.ordinal()){
                exportVo.setInvoiceResultStr("已开票");
            }else if (exportVo.getInvoiceResult()==CANCEL.ordinal()){
                exportVo.setInvoiceResultStr("开票失败");
            }
            String invoiceTypeDesc = FinancialInvoiceEnum.invoiceType.getDesc(Integer.parseInt(exportVo.getInvoiceType()));
            exportVo.setInvoiceType(invoiceTypeDesc);
        }
        File file = new File(System.getProperty(FinanceConstants.TEMP_FILE_PATH) + File.separator + filename);
        try (ExcelWriter excelWriter = EasyExcel.write(file).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().head(FinanceInvoiceExportVo.class).build();
            List<List<FinanceInvoiceExportVo>> splitList = ListUtil.split(financeInvoiceExportVos, 100);
            for (List<FinanceInvoiceExportVo> invoiceList:splitList) {
                excelWriter.write(invoiceList, writeSheet);
            }
            excelWriter.finish();
            OssUploadResult upload = OssUploadUtil.upload(filename, file, OSSExpiredLabelEnum.THREE_DAY);
            DownloadCenterUploadReq req = new DownloadCenterUploadReq();
            req.setResId(resId);
            req.setFilePath(upload.getObjectOssKey());
            req.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterProvider.uploadFile(req);
        }
        finally {
            FileUtil.del(file);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<FinanceInvoiceImportVo> importInvoice(FinancialInvoiceImportQuery importQuery) {
        InputStream inputStream = OssGetUtil.getInputStream(importQuery.getUrl());
        List<FinancialInvoiceImportDTO> importDTOList = EasyExcel.read(inputStream).head(FinancialInvoiceImportDTO.class).sheet().doReadSync();
        if (importDTOList.isEmpty()) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "导入记录不能为空");
        }
        List<FinanceInvoiceImportDetailVo> errorImportList = new ArrayList<>();
        List<Long> idList = importDTOList.stream().map(FinancialInvoiceImportDTO::getId).collect(Collectors.toList());
        // 过滤 id 正常的记录
        List<FinancialInvoice> financialInvoices = financialInvoiceMapper.selectByIds(idList);
        if (financialInvoices.size() != importDTOList.size()) {
            idList = financialInvoices.stream().map(FinancialInvoice::getId).collect(Collectors.toList());
            importDTOList = check(idList, errorImportList, importDTOList, "id不存在");
        }
        if (!idList.isEmpty()) {
            // 过滤快递单号正常的记录
            List<Long> emptyExpressIds = financialInvoiceMapper.selectEmptyExpressByIds(idList);
            if (emptyExpressIds.size() != importDTOList.size()) {
                importDTOList = check(emptyExpressIds, errorImportList, importDTOList, "已有快递单号");
            }
        }

        FinanceInvoiceImportVo importVo = new FinanceInvoiceImportVo();
        if (!errorImportList.isEmpty()) {
            String filename="发票快递单号导入结果-" + UUID.randomUUID()+".xlsx";
            DownloadCenterInitReq req = new DownloadCenterInitReq();
            req.setFileExpiredDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY);
            req.setBizType(FINANCIAL_INVOICE_IMPORT_RESULT.getBizType());
            req.setFileName(filename);
            req.setAdminId(Long.valueOf(getAdminId()));
            DubboResponse<DownloadCenterResp> response = downloadCenterProvider.initRecord(req);
            if (!response.isSuccess()) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "网络波动，请重试");
            }
            importVo.setResId(response.getData().getResId());
            SpringUtil.getBean(FinancialInvoiceService.class).uploadExportFile(errorImportList,filename,response.getData().getResId());
        }

        if (!importDTOList.isEmpty()) {
            financialInvoiceMapper.updateBatchById(importDTOList);
        }
        importVo.setSuccessCount(importDTOList.size());
        importVo.setFailedCount(errorImportList.size());
        return CommonResult.ok(importVo);
    }

    @Override
    @Async("downloadExecutor")
    public void uploadExportFile(List<FinanceInvoiceImportDetailVo> errorImportList, String filename, Long resId) {
        File file = new File(System.getProperty(FinanceConstants.TEMP_FILE_PATH) + File.separator + filename);
        EasyExcel.write(file, FinanceInvoiceImportDetailVo.class).sheet("导入结果").doWrite(errorImportList);
        OssUploadResult upload = OssUploadUtil.upload(filename, file, OSSExpiredLabelEnum.THREE_DAY);
        DownloadCenterUploadReq req = new DownloadCenterUploadReq();
        req.setStatus(DownloadCenterEnum.Status.UPLOADED);
        req.setResId(resId);
        req.setFilePath(upload.getObjectOssKey());
        downloadCenterProvider.uploadFile(req);
    }

    public List<FinancialInvoiceImportDTO> check(List<Long> normalIdList, List<FinanceInvoiceImportDetailVo> errorImportList, List<FinancialInvoiceImportDTO> importDTOList,String reason) {
        // 错误数据
        List<FinanceInvoiceImportDetailVo> notExistList = importDTOList.stream().filter(ip -> !normalIdList.contains(ip.getId())).map(ip -> {
            FinanceInvoiceImportDetailVo detailVo = new FinanceInvoiceImportDetailVo();
            if (ip.getId() != null) {
                detailVo.setId(String.valueOf(ip.getId()));
            }
            detailVo.setExpress(ip.getExpress());
            detailVo.setReason(reason);
            return detailVo;
        }).collect(Collectors.toList());
        errorImportList.addAll(notExistList);

        // 正确数据
        return importDTOList.stream().filter(i -> normalIdList.contains(i.getId())).collect(Collectors.toList());
    }

    private void makeBwListCalculateMoney(BigFinancialSkuVO bigFinancialSkuVO, List<BwInvoiceProduct> bwInvoiceProductList) {
        List<BigFinancialSkuItemVO> list = bigFinancialSkuVO.getList();
        for (BigFinancialSkuItemVO bigFinancialSkuItemVO : list) {
            String sku = bigFinancialSkuItemVO.getSku();
            BwInvoiceProduct bwInvoiceProduct = BwFinancialInvoiceBuilder.buildBwInvoiceProduct();
            bwInvoiceProduct.setSpmc(bigFinancialSkuItemVO.getPdName());
            Integer categoryId = bigFinancialSkuItemVO.getCategoryId();
            String taxRateCode;
            BigDecimal taxRateValue;
            String pdName = bigFinancialSkuItemVO.getPdName();
            // 精准送单独计算
            if (TIME_FRAME_FEE_SKU.equals(sku)) {
                TaxRateConfigBO config = financeTaxRateConfig.getConfig(FinanceTaxRateConfig.BizType.DIGITAL_TIME_FRAME);
                pdName = config.getProductName();
                taxRateCode = config.getTaxRateCode();
                taxRateValue = new BigDecimal(config.getTaxRate());
//                pdName = "运输服务费-精准送";
//                taxRateCode = "3010102020100000000";
//                taxRateValue = new BigDecimal("0.13");
            }else if (ODERA_ADD_TIMD_SKU.equals(sku)){
                pdName = "其他现代服务-超时加单";
                taxRateCode = "3049900000000000000";
                taxRateValue = new BigDecimal("0.06");
            } else{
                // 其他sku税率和税收编码
                TaxRateConfig taxRateConfig = this.getTaxRateConfig(sku, categoryId);
                if (Objects.isNull(taxRateConfig.getTaxRateValue()) || Objects.isNull(taxRateConfig.getTaxRateCode())) {
                    logger.error("没有税率和税收编码:{},{}", sku, categoryId);
                }
                taxRateCode = taxRateConfig.getTaxRateCode();
                taxRateValue = taxRateConfig.getTaxRateValue();
            }

            bwInvoiceProduct.setSpmc(pdName);
            bwInvoiceProduct.setSpbm(taxRateCode);
            // 金额(默认为含税金额)
            BigDecimal price = bigFinancialSkuItemVO.getPrice().multiply(new BigDecimal(bigFinancialSkuItemVO.getAmount()));
            // 金额为0不开票
            if (BigDecimal.ZERO.compareTo(price) == 0) {
                logger.info("sku:{},金额为0,不计入百旺开票商品清单,涉及信息:{}", sku, bigFinancialSkuItemVO);
                continue;
            }

            FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO = new FinancialInvoiceOrderItemDTO();
            financialInvoiceOrderItemDTO.setWeigh(bigFinancialSkuItemVO.getWeight());
            financialInvoiceOrderItemDTO.setSku(sku);
            financialInvoiceOrderItemDTO.setAmount(bigFinancialSkuItemVO.getAmount());
            financialInvoiceOrderItemDTO.setPrice(bigFinancialSkuItemVO.getPrice());
            this.dealWithBwProductInfo(Arrays.asList(financialInvoiceOrderItemDTO), bwInvoiceProduct, price, bigFinancialSkuVO.getRedFlushTag());

            BigDecimal taxRate = taxRateValue.divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP);
            this.calculateTaxAmount(bwInvoiceProduct, price, taxRate, bigFinancialSkuVO);
            if (BigDecimal.ZERO.compareTo(taxRate) == 0) {
                BwFinancialInvoiceBuilder.buildBwDutyFreeGoodInvoiceProduct(bwInvoiceProduct);
            }
            bwInvoiceProductList.add(bwInvoiceProduct);
        }

        bigFinancialSkuVO.setAmountExcludingTax(bigFinancialSkuVO.getAllMoney().subtract(bigFinancialSkuVO.getTotalTaxMoney()));

    }


    /**
     * 发送开票消息至mq
     * @param bwInvoiceBO 开票消息内容
     * @param financialInvoiceId 票据id
     * @param invoiceUrlEnum 开票url信息
     */
    private void sendFinancialInvoiceMessage(BwInvoiceBO bwInvoiceBO, Long financialInvoiceId,
                                             InvoiceUrlEnum invoiceUrlEnum, String messageType) {
        sendFinancialInvoiceMessage(bwInvoiceBO, financialInvoiceId, invoiceUrlEnum, messageType, null);
    }

    /**
     * 发送开票消息至mq
     * @param bwInvoiceBO 开票消息内容
     * @param financialInvoiceId 票据id
     * @param invoiceUrlEnum 开票url信息
     */
    private void sendFinancialInvoiceMessage(BwInvoiceBO bwInvoiceBO, Long financialInvoiceId,
                                             InvoiceUrlEnum invoiceUrlEnum, String messageType,
                                             String batchNo) {
        JSONObject msgJson = new JSONObject();
        msgJson.put("bwInvoiceBO", bwInvoiceBO);
        msgJson.put("financialInvoiceId",financialInvoiceId);
        msgJson.put("invoiceUrl",invoiceUrlEnum.getUrl());
        if (!StringUtils.isEmpty(batchNo)) {
            msgJson.put("batchNo", batchNo);
        }
        String msg = msgJson.toJSONString();
        MQData mqData = new MQData();
        mqData.setType(messageType);
        mqData.setBusiness(MessageBusiness.FINANCE);
        mqData.setData(msg);
        logger.info("发送票据申请至百旺:{}", JSON.toJSONString(mqData));
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
    }

    @Override
    public AjaxResult invoiceList(Integer pageIndex, Integer pageSize, FinancialInvoiceQuery query) {
        // 若是入参的queryType来自CRM
        if(Objects.nonNull(query.getQueryType())
                && Objects.deepEquals(query.getQueryType(),AdminAuthExtendEnum.Type.WX_CRM.ordinal())){
            //则只能看到发起人的即登入着的自己发起的开票
           query.setCreatorId(getAdminId());
        }

        //如果根据订单查询，先查询订单情况
        if (Objects.nonNull(query.getOrderNo())){
            List<Long> ids = financialInvoiceMapper.selectIdByQuery(query);
            if(CollectionUtil.isEmpty(ids)){
                return AjaxResult.getOK();
            }
            query.setIds(ids);
        }
        if(Objects.nonNull(query.getInvoiceQueryEndTime())){
            query.setInvoiceQueryEndTime(query.getInvoiceQueryEndTime().plusDays(NumberUtils.INTEGER_ONE));
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<FinancialInvoiceVO> financialInvoiceList = financialInvoiceMapper.selectByKey(query);
        if(Objects.nonNull(query.getQueryType())
                && Objects.deepEquals(query.getQueryType(),AdminAuthExtendEnum.Type.WX_CRM.ordinal())){
            this.selectList(financialInvoiceList);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financialInvoiceList));
    }

    private void selectList(List<FinancialInvoiceVO> financialInvoiceList){
        if(!CollectionUtils.isEmpty(financialInvoiceList)){
            for (FinancialInvoiceVO item : financialInvoiceList) {
                Long invoiceId = item.getInvoiceId();
                InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(invoiceId);
                item.setInvoiceConfigItem(invoiceConfig);
            }
        }
    }

    @Override
    public AjaxResult<FinancialInvoiceDetailVO> invoiceDetail(FinancialInvoiceInput financialInvoiceInput) {
        FinancialInvoiceDetailVO finalInvoiceDetail = new FinancialInvoiceDetailVO();

        // 发票信息
        FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(financialInvoiceInput.getId());
        if(Objects.isNull(financialInvoice)){
            return AjaxResult.getErrorWithMsg("该发票不存在，请联系管理员解决");
        }
//        if(Objects.equals(INVOICED.ordinal(),financialInvoice.getInvoiceResult())
//                && Objects.isNull(financialInvoice.getPdfUrl())){
//            // 开票成功与获取票据pdf文件有延时,所以在点击详情时,若还没获取到票据pdf路径,则手动获取一次
//            this.getInvoicePdfUrl(financialInvoice);
//        }
        FinancialInvoiceVO financialInvoiceVO = new FinancialInvoiceVO();
        BeanCopyUtil.copyProperties(financialInvoice,financialInvoiceVO);
        // 创建人
        if (StrUtil.isNotBlank(financialInvoice.getCreatorName())) {
            financialInvoiceVO.setCreatorType(NumberUtils.INTEGER_TWO);
        } else {
            Admin admin = adminMapper.selectByPrimaryKey(financialInvoice.getCreatorId());
            financialInvoiceVO.setCreatorName(Optional.ofNullable(admin).orElse(new Admin()).getRealname());
            financialInvoiceVO.setCreatorType(NumberUtils.INTEGER_ZERO);
        }
        int orderNums = financialInvoiceOrdernoRelationMapper.countOrdersByInvoiceId(financialInvoiceInput.getId());
        financialInvoiceVO.setOrderNums(orderNums);
        // 票据颜色
        int invoiceColor = BigDecimal.ZERO.compareTo(financialInvoice.getAmountMoney()) > 0 ? 0 : 1;
        financialInvoiceVO.setInvoiceColor(invoiceColor);
        // 不含税金额
        BigDecimal taxAmount = Objects.isNull(financialInvoiceVO.getTaxAmount()) ? BigDecimal.ZERO : financialInvoiceVO.getTaxAmount();
        BigDecimal subtract = financialInvoiceVO.getAmountMoney().subtract(taxAmount);
        financialInvoiceVO.setAmountExcludingTax(subtract);
        // 是否可作废
        this.isCancel(financialInvoiceVO);
        // 抬头信息
        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoice.getInvoiceId());
        if (invoiceConfig != null) {
            if (StringUtils.isNotEmpty(financialInvoice.getMailAddress())) {
                invoiceConfig.setMailAddress(financialInvoice.getMailAddress());
            }
            finalInvoiceDetail.setInvoiceConfig(invoiceConfig);

            Integer type = invoiceConfig.getType();
            if (Objects.equals(type, NumberUtils.INTEGER_THREE)) {
                financialInvoiceVO.setBelongType(1);
            } else {
                financialInvoiceVO.setBelongType(0);
                InvoiceEmailOverride invoiceEmailOverride = invoiceEmailOverrideService.selectByConfigIdAndMId(invoiceConfig.getId(), financialInvoice.getMId());
                if (invoiceEmailOverride != null) {
                    invoiceConfig.setCompanyEmail(invoiceEmailOverride.getEmail());
                }
            }
        }else {
            financialInvoiceVO.setBelongType(0);
        }

        finalInvoiceDetail.setFinancialInvoice(financialInvoiceVO);

        // 金额信息
        FinancialInvoiceMoneyVO financialInvoiceMoney = new FinancialInvoiceMoneyVO();
        FinanceInvoiceExpand financeInvoiceExpand = financeInvoiceExpandMapper.selectByFinancialInvoiceId(financialInvoiceInput.getId());
        if(Objects.nonNull(financeInvoiceExpand)){
            BeanCopyUtil.copyProperties(financeInvoiceExpand,financialInvoiceMoney);
            financialInvoiceMoney.setAdjustTotalPrice(financeInvoiceExpand.getSkuAdjustPrice()
                    .add(financeInvoiceExpand.getDeliveryFeeAdjustPrice()));
            financialInvoiceMoney.setInvoiceAmount(financialInvoice.getAmountMoney());
            // 实付总金额 = 商品金额 + 配送费 + 配送费优惠金额 + 超时加单 + 精准送
            FinancialInvoiceDataHandleUtil.getFinancialInvoiceTotalMoney(financialInvoiceMoney);
        }
        // 红冲票需要取反处理
        if (BigDecimal.ZERO.compareTo(financialInvoice.getAmountMoney()) > 0) {
            this.redFlushInvoiceNegate(financialInvoiceMoney);
        }

        finalInvoiceDetail.setFinancialMoneyVO(financialInvoiceMoney);
        return AjaxResult.getOK(finalInvoiceDetail);
    }

    private void isCancel(FinancialInvoiceVO financialInvoiceVO) {
        int ordinal = FinancialInvoiceEnum.isCancel.YES.ordinal();
        if(ObjectUtil.notEqual(FinancialInvoiceEnum.invoiceState.NORMAL.ordinal(),financialInvoiceVO.getInvoiceStatus())){
            ordinal = FinancialInvoiceEnum.isCancel.NO.ordinal();
        }

        if(BigDecimal.ZERO.compareTo(financialInvoiceVO.getAmountMoney()) > 0){
            ordinal = FinancialInvoiceEnum.isCancel.NO.ordinal();
        }

        if(ObjectUtil.notEqual(INVOICED.ordinal(),financialInvoiceVO.getInvoiceResult())){
            ordinal = FinancialInvoiceEnum.isCancel.NO.ordinal();
        }


        boolean vatNotCancel = FinancialInvoiceEnum.invoiceType.isSpecial(financialInvoiceVO.getInvoiceType())
                && (FinancialInvoiceConstant.getVatCancelEndTime().isBefore(financialInvoiceVO.getCreateTime())
                || DateUtil.getFirstDayOfMonth().isAfter(financialInvoiceVO.getCreateTime()));
        if(vatNotCancel){
            ordinal = FinancialInvoiceEnum.isCancel.NO.ordinal();
        }

        //超过10w订单的 若有调整单子不允许作废
        int count = bigFinancialInvoiceSkuMapper.countByInvoiceId(financialInvoiceVO.getId());
        if (count > 0 && Objects.equals(FinancialInvoiceEnum.isCancel.YES.ordinal(), ordinal)) {
            List<BigFinancialInvoiceSku> bigFinancialInvoiceSkus = bigFinancialInvoiceSkuMapper.selectByFinancialId(financialInvoiceVO.getId());
            if (!CollectionUtils.isEmpty(bigFinancialInvoiceSkus)) {
                String orderNo = bigFinancialInvoiceSkus.get(0).getOrderNo();
                //查询是否有售后单子 有就不允许作废
                AfterSaleOrderQuery afterSaleOrderQuery = new AfterSaleOrderQuery();
                afterSaleOrderQuery.setRefundTag(NumberUtils.INTEGER_ZERO);
                afterSaleOrderQuery.setOrderNo(orderNo);
                List<AfterSaleOrderVO> afterSaleOrderVOList = afterSaleOrderMapper.selectBySelectKeys(afterSaleOrderQuery);
                if (!CollectionUtils.isEmpty(afterSaleOrderVOList)) {
                    ordinal = FinancialInvoiceEnum.isCancel.NO.ordinal();
                }
            }
        }
        financialInvoiceVO.setCancel(ordinal);
    }

    /**
     * 红冲发票金额展示为负, 取反处理
     * @param financialInvoiceMoney 发票金额信息
     */
    private void redFlushInvoiceNegate(FinancialInvoiceMoneyVO financialInvoiceMoney) {
        financialInvoiceMoney.setPreferentialAmount(financialInvoiceMoney.getPreferentialAmount().negate());
        // 配送费 = 总配送费 + 优惠配送费 + 调整配送费
        BigDecimal deliveryFee = financialInvoiceMoney.getDeliveryFee()
                .add(financialInvoiceMoney.getDeliveryPreferentialAmount())
                .add(financialInvoiceMoney.getAdjustDeliveryFeeTotalPrice());
        financialInvoiceMoney.setDeliveryFee(deliveryFee.negate());
        financialInvoiceMoney.setOutTimesFee(financialInvoiceMoney.getOutTimesFee().negate());
        financialInvoiceMoney.setTimeFrameFee(financialInvoiceMoney.getTimeFrameFee().negate());

    }

    @Override
    public void getInvoicePdfUrl(){
        FinancialInvoiceQuery financialInvoiceQuery = new FinancialInvoiceQuery();
        financialInvoiceQuery.setInvoiceResult(INVOICED.ordinal());
        financialInvoiceQuery.setHasPdfUrl(Boolean.FALSE);
        List<FinancialInvoice> financialInvoiceList = financialInvoiceMapper.selectFinancialInvoiceByQuery(financialInvoiceQuery );
        for (FinancialInvoice financialInvoice : financialInvoiceList) {
            this.getInvoicePdfUrl(financialInvoice);
        }
    }

    /**
     * 获取发票pdf路径地址
     * @param financialInvoice 发票信息
     */
    private void getInvoicePdfUrl(FinancialInvoice financialInvoice) {
        BwRed bwContentInvoiceBO = Objects.equals(FinancialInvoiceEnum.invoiceType.VAT.ordinal(), financialInvoice.getInvoiceType())
                ? BwFinancialInvoiceBuilder.buildBwVatInvoiceContentBO()
                : BwFinancialInvoiceBuilder.buildBwElectronicInvoiceContentBO();
        bwContentInvoiceBO.setType("0");
        bwContentInvoiceBO.setFpdm(financialInvoice.getInvoiceCode());
        bwContentInvoiceBO.setFphm(financialInvoice.getInvoiceNumber());

        String businessCode = "queryPdf";
        BwInvoiceBO bwInvoiceBO = new BwInvoiceBO();
        bwInvoiceBO.setBusinessCode(businessCode);
        bwInvoiceBO.setContent(bwContentInvoiceBO);

        BwInvoiceResponseBO bwInvoiceResponseBO = BwFinancialInvoiceRequestUtil.invoiceOpening(businessCode, JSON.toJSONString(bwInvoiceBO),
                InvoiceUrlEnum.INVOICE_PDF_URL.getUrl(), financialInvoice.getSerialNumber());
        if(!BwInvoiceConstant.RESPONSE_SUCCESS_CODE.equals(bwInvoiceResponseBO.getCode())) {
            return;
        }
        String pdfUrl = bwInvoiceResponseBO.getContent().getPdfUrl();
        financialInvoice.setPdfUrl(pdfUrl);
        financialInvoiceMapper.updateByPrimaryKeySelective(financialInvoice);
    }


    @Override
    public AjaxResult<Map<String,FinancialInvoiceMoneyVO>> calculateInvoiceAmount(FinancialInvoiceInput financialInvoiceInput) {
        // 处理订单数据
        FinancialInvoiceUserDTO financialInvoiceUserDTO = new FinancialInvoiceUserDTO();
        FinancialInvoiceUserDTO dutyFreeGoodUserDTO = new FinancialInvoiceUserDTO();
        Map<String,FinancialInvoiceMoneyVO> financialInvoiceMoneyVOMap = new HashMap<>();

        AjaxResult ajaxResult = FinancialInvoiceDataHandleUtil.financialInvoiceOrderHandle(financialInvoiceInput, financialInvoiceUserDTO, dutyFreeGoodUserDTO);
        if(!ajaxResult.isSuccess()){
            return ajaxResult;
        }

        FinancialInvoiceMoneyVO dutyFreeGoodInvoiceMoney = new FinancialInvoiceMoneyVO();
        if(CollectionUtil.isNotEmpty(dutyFreeGoodUserDTO.getOrderItemIdList())){
            this.getFinancialInvoiceMoney(dutyFreeGoodUserDTO,dutyFreeGoodInvoiceMoney);
        }

        FinancialInvoiceMoneyVO financialInvoiceMoney = new FinancialInvoiceMoneyVO();
        if(CollectionUtil.isNotEmpty(financialInvoiceUserDTO.getOrderNoList())){
            this.getFinancialInvoiceMoney(financialInvoiceUserDTO,financialInvoiceMoney);
        }

        financialInvoiceMoneyVOMap.put("financialInvoiceMoney",financialInvoiceMoney);
        financialInvoiceMoneyVOMap.put("dutyFreeGoodInvoiceMoney",dutyFreeGoodInvoiceMoney);
        return AjaxResult.getOK(financialInvoiceMoneyVOMap);
    }

    /**
     * 获取订单应开票金额
     * @param financialInvoiceUserDTO 订单信息
     * @param financialInvoiceMoneyVO 开票金额对象
     */
    private void getFinancialInvoiceMoney(FinancialInvoiceUserDTO financialInvoiceUserDTO,
                                          FinancialInvoiceMoneyVO financialInvoiceMoneyVO){
        // 用于查询售后信息
        AfterSaleOrderQuery afterSaleOrderQuery = new AfterSaleOrderQuery();
        afterSaleOrderQuery.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        afterSaleOrderQuery.setRefundTag(NumberUtils.INTEGER_ZERO);

        // 获取订单明细的所有信息
        List<OrderAndItemDTO> orderAndItemDTOList = ordersMapper.selectOrderAndItemList(financialInvoiceUserDTO.getOrderNoList());
        // 精准送金额
        Map<String, OrderRelation> relationMap = orderRelationMapper.listActualPaidPrecision(financialInvoiceUserDTO.getOrderNoList()).stream().collect(Collectors.toMap(OrderRelation::getOrderNo, Function.identity()));
        Pair<LocalDate, LocalDate> billCyclePair = getBillCyclePair(financialInvoiceUserDTO.getBillNumber());
        List<AfterSaleOrderVO> allAfterSaleOrderList = Lists.newArrayList();
        for (OrderAndItemDTO orderAndItemDTO : orderAndItemDTOList) {
            // 退款金额 包含退款与退运费
            afterSaleOrderQuery.setOrderNo(orderAndItemDTO.getOrderNo());
            List<AfterSaleOrderVO> afterSaleOrderVOList = afterSaleOrderMapper.selectBySelectKeys(afterSaleOrderQuery);
            if (billCyclePair != null) {
                afterSaleOrderVOList = filterInBillCycleAfterSaleOrder(billCyclePair.getKey(), billCyclePair.getValue(), afterSaleOrderVOList);
                if (!CollectionUtil.isEmpty(afterSaleOrderVOList)) {
                    allAfterSaleOrderList.addAll(afterSaleOrderVOList);
                }
            }
            if(Objects.equals(financialInvoiceUserDTO.getDutyFreeGood(), FinancialInvoiceEnum.dutyFreeGood.NOT_DUTY_FREE_GOOD.ordinal())){
                // 处理非免税品发票的配送费,精准送,超时加单费,运费调整金额
                this.invoiceDeliveryServiceFeeHandle(financialInvoiceMoneyVO,orderAndItemDTO,afterSaleOrderVOList);
            }
            // 精准送金额
            if (relationMap.containsKey(orderAndItemDTO.getOrderNo())){
                financialInvoiceMoneyVO.setTimeFrameFee(NumberUtil.add(financialInvoiceMoneyVO.getTimeFrameFee(), relationMap.get(orderAndItemDTO.getOrderNo()).getPrecisionDeliveryFee()));
            }
            // 订单明细金额处理
            this.invoiceProductFeeHandle(financialInvoiceMoneyVO,orderAndItemDTO,
                    financialInvoiceUserDTO,afterSaleOrderVOList);
        }
        // 跨期售后无脑摊平
        if (!StringUtils.isBlank(financialInvoiceUserDTO.getBillNumber())) {
            processAcrossPeriod(financialInvoiceUserDTO, orderAndItemDTOList, allAfterSaleOrderList, financialInvoiceMoneyVO);
        }

        // 计算开票金额
        FinancialInvoiceDataHandleUtil.financialInvoiceMoneyHandle(financialInvoiceMoneyVO);

        // 记录拓展金额信息
        FinanceInvoiceExpand financeInvoiceExpand = financialInvoiceUserDTO.getFinanceInvoiceExpand();
        BeanCopyUtil.copyProperties(financialInvoiceMoneyVO,financeInvoiceExpand);
        financeInvoiceExpand.setDeliveryFeeAdjustPrice(financialInvoiceMoneyVO.getAdjustDeliveryFeeTotalPrice());
        financeInvoiceExpand.setSkuAdjustPrice(financialInvoiceMoneyVO.getAdjustTotalPrice()
                .subtract(financialInvoiceMoneyVO.getAdjustDeliveryFeeTotalPrice()));
    }

    private void processAcrossPeriod(FinancialInvoiceUserDTO financialInvoiceUserDTO, List<OrderAndItemDTO> orderAndItemDTOList, List<AfterSaleOrderVO> afterSaleOrderVOList, FinancialInvoiceMoneyVO financialInvoiceMoneyVO) {
        if(Objects.equals(FinancialInvoiceEnum.dutyFreeGood.DUTY_FREE_GOOD.ordinal(),financialInvoiceUserDTO.getDutyFreeGood())){
            // 免税票不分摊
            logger.info("免税票不分摊跨期售后，{}", financialInvoiceUserDTO);
            return;
        }
        List<FinancialInvoiceOrderItemDTO> orderItemResultList = financialInvoiceUserDTO.getOrderItemResultList();
        List<Long> mIds = orderAndItemDTOList.stream().map(OrderAndItemDTO::getmId).collect(Collectors.toList());
        Pair<LocalDate, LocalDate> billCyclePair = getBillCyclePair(financialInvoiceUserDTO.getBillNumber());
        List<FinanceBillAfterSaleDetails> financeBillAfterSaleDetails = afterSaleDetailsMapper.selectAfterSaleByMids(mIds, billCyclePair.getKey(), billCyclePair.getValue());

        // 2、匹配出跨期售后
        Set<String> currentPeriodOrderAfterSaleNoSet = afterSaleOrderVOList.stream()
                .map(AfterSaleOrderVO::getAfterSaleOrderNo)
                .collect(Collectors.toSet());
        List<FinanceBillAfterSaleDetails> acrossPeriodOrderAfterSales = financeBillAfterSaleDetails.stream().filter(el -> !currentPeriodOrderAfterSaleNoSet.contains(el.getAfterSaleOrderId())).collect(Collectors.toList());
        // 3、金额无脑摊在订单上
        orderItemResultList.sort(Comparator.comparing(FinancialInvoiceOrderItemDTO::getTotalPrice).reversed());
//        List<FinancialInvoiceOrderItemDTO> sortOrderItems = orderItemResultList.stream().sorted(Comparator.comparing(FinancialInvoiceOrderItemDTO::getTotalPrice).reversed()).collect(Collectors.toList());
        logger.info("订单明细:{}", JSON.toJSONString(orderItemResultList));
        logger.info("跨期售后:{}", JSON.toJSONString(acrossPeriodOrderAfterSales));

        for (FinanceBillAfterSaleDetails billAfterSaleDetails : acrossPeriodOrderAfterSales) {
            BigDecimal afterSaleAmt = billAfterSaleDetails.getAfterSaleAmt();
            logger.info("开始分摊售后单:{}, 金额:{}", billAfterSaleDetails.getAfterSaleOrderId(), afterSaleAmt);
            for (FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO : orderItemResultList) {
                BigDecimal totalPrice = financialInvoiceOrderItemDTO.getTotalPrice();
                if (totalPrice.compareTo(afterSaleAmt) >= 0) {
                    logger.info("订单明细:{}, 金额:{}, 可以覆盖跨期售后:{}", financialInvoiceOrderItemDTO.getOrderItemId(), financialInvoiceOrderItemDTO.getTotalPrice(), afterSaleAmt);
                    financialInvoiceOrderItemDTO.setTotalPrice(totalPrice.subtract(afterSaleAmt));
                    afterSaleAmt = BigDecimal.ZERO;
                    break;
                } else {
                    afterSaleAmt = afterSaleAmt.subtract(totalPrice);
                    financialInvoiceOrderItemDTO.setTotalPrice(BigDecimal.ZERO);
                    logger.info("订单明细:{}, 金额:{}, 可以分摊跨期售后:{}", financialInvoiceOrderItemDTO.getOrderItemId(), financialInvoiceOrderItemDTO.getTotalPrice(), afterSaleAmt);
                }
            }
            if (afterSaleAmt.compareTo(BigDecimal.ZERO) > 0) {
                throw new BizException("跨期售后金额大于订单金额,无法发起开票");
            }
            financialInvoiceMoneyVO.setRefundAmount(financialInvoiceMoneyVO.getRefundAmount().add(billAfterSaleDetails.getAfterSaleAmt()));
        }
        logger.info("订单明细:{}", JSON.toJSONString(orderItemResultList));
    }

    /**
     * 处理订单明细金额及调整金额
     * @param financialInvoiceMoneyVO 票据金额对象
     * @param orderAndItemDTO 订单流转对象
     * @param financialInvoiceUserDTO 票据订单使用流转对象
     * @param afterSaleOrderVOList 订单售后信息
     */
    public void invoiceProductFeeHandle(FinancialInvoiceMoneyVO financialInvoiceMoneyVO, OrderAndItemDTO orderAndItemDTO,
                                        FinancialInvoiceUserDTO financialInvoiceUserDTO,List<AfterSaleOrderVO> afterSaleOrderVOList){

        List<FinancialInvoiceOrderItemDTO> orderItemResultList = financialInvoiceUserDTO.getOrderItemResultList();
        List<OrderItem> orderItemList = orderAndItemDTO.getOrderItemList();
        for (OrderItem orderItem : orderItemList) {
            // 必须与传值中的明细id对应上才可以
            if(financialInvoiceUserDTO.getOrderItemIdList().contains(orderItem.getId())){
                // 金额为0不开票
                if(BigDecimal.ZERO.compareTo(orderItem.getTotalPrice()) >= 0){
                    logger.info("id:{},订单明细金额为0,跳过开票",orderItem.getId());
                    continue;
                }

                FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO = new FinancialInvoiceOrderItemDTO();
                financialInvoiceOrderItemDTO.setCategoryId(orderItem.getCategoryId());
                financialInvoiceOrderItemDTO.setSku(orderItem.getSku());
                financialInvoiceOrderItemDTO.setPdName(orderItem.getPdName());
                financialInvoiceOrderItemDTO.setAmount(orderItem.getAmount());
                financialInvoiceOrderItemDTO.setPrice(orderItem.getPrice());
                if (orderItem.getActualTotalPrice() != null && orderItem.getActualTotalPrice().compareTo(BigDecimal.ZERO) > 0
                  &&orderItem.getActualTotalPrice().compareTo(orderItem.getTotalPrice())!=0) {
                    orderItem.setTotalPrice(orderItem.getActualTotalPrice());
                    orderItem.setPrice(getRealPrice(orderItem.getActualTotalPrice(), orderItem.getAmount()));
                }
                financialInvoiceOrderItemDTO.setWeigh(orderItem.getWeight());
                financialInvoiceOrderItemDTO.setOrderItemId(orderItem.getId());
                // 精准送
                if(Objects.equals(TIME_FRAME_FEE_SKU,orderItem.getSku())){
                    financialInvoiceMoneyVO.setTimeFrameFee(financialInvoiceMoneyVO.getTimeFrameFee().add(orderItem.getTotalPrice()));
                    financialInvoiceOrderItemDTO.setTotalPrice(orderItem.getTotalPrice());
                }else {
                    // 除去精准送外其他sku的金额
                    financialInvoiceMoneyVO.setPreferentialAmount(financialInvoiceMoneyVO.getPreferentialAmount().add(orderItem.getTotalPrice()));

                    // sku售后金额
                    List<AfterSaleOrderVO> skuAfterSaleOrderList= afterSaleOrderVOList.stream()
                            .filter(el -> Objects.deepEquals(el.getSku(), orderItem.getSku()))
                            .filter(el -> (el.getHandleType() >= AfterSaleHandleType.REFUND.getType())
                                    && (el.getHandleType() <= AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType()))
                            .collect(Collectors.toList());
                    BigDecimal refundAmount = BigDecimal.ZERO;
                    if(CollectionUtil.isNotEmpty(skuAfterSaleOrderList)){
                        refundAmount = skuAfterSaleOrderList.stream().map(AfterSaleOrderVO::getHandleNum)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    financialInvoiceMoneyVO.setRefundAmount(financialInvoiceMoneyVO.getRefundAmount().add(refundAmount));

                    // 订单明细调整金额
                    BigDecimal adjustmentPrice = adjustmentDetailMapper.selectSuccessNumByOrderItem(orderItem.getId());
                    financialInvoiceMoneyVO.setAdjustTotalPrice(adjustmentPrice.add(financialInvoiceMoneyVO.getAdjustTotalPrice()));
                    financialInvoiceOrderItemDTO.setAdjustOrderItemPrice(adjustmentPrice);

                    // 订单明细开票金额 = 实付金额 + 明细调整金额 - 退款金额
                    BigDecimal skuTotalPrice = orderItem.getTotalPrice().add(adjustmentPrice).subtract(refundAmount);
                    financialInvoiceOrderItemDTO.setTotalPrice(skuTotalPrice);
                }
                orderItemResultList.add(financialInvoiceOrderItemDTO);
            }
        }
    }

    private BigDecimal getRealPrice(BigDecimal totalPrice, Integer amount) {
        if (amount ==0 || totalPrice.compareTo(BigDecimal.ZERO)==0){
            return BigDecimal.ZERO;
        }
        BigDecimal divide = totalPrice.divide(new BigDecimal(amount + ""),BigDecimal.ROUND_HALF_UP);
        divide.setScale(6);
        return  divide;
    }
    /**
     * 处理票据中的配送费,超时加单,配送费优惠,配送费调整金额
     * @param financialInvoiceMoneyVO 票据金额对象
     * @param orderAndItemDTO 订单流转对象
     */
    private void  invoiceDeliveryServiceFeeHandle(FinancialInvoiceMoneyVO financialInvoiceMoneyVO, OrderAndItemDTO orderAndItemDTO,
                                                  List<AfterSaleOrderVO> afterSaleOrderVOList){
        // 配送费
        financialInvoiceMoneyVO.setDeliveryFee(financialInvoiceMoneyVO.getDeliveryFee().add(orderAndItemDTO.getDeliveryFee()));

        // 超时加单
        financialInvoiceMoneyVO.setOutTimesFee(financialInvoiceMoneyVO.getOutTimesFee().add(orderAndItemDTO.getOutTimesFee()));

        // 配送费优惠金额
        BigDecimal deliveryDiscountMoney = this.calculateDeliveryDiscountMoney(orderAndItemDTO.getOrderNo());
        financialInvoiceMoneyVO.setDeliveryPreferentialAmount(deliveryDiscountMoney.add(financialInvoiceMoneyVO.getDeliveryPreferentialAmount()));

        // 配送费售后
        List<AfterSaleOrderVO> skuAfterSaleOrderList= afterSaleOrderVOList.stream()
                .filter(el -> Objects.isNull(el.getSku()))
                .filter(el -> (el.getHandleType() >= AfterSaleHandleType.RETURN_SHIPPING.getType())
                        && (el.getHandleType() <= AfterSaleHandleType.RETURN_SHIPPING_BILL.getType()))
                .collect(Collectors.toList());
        BigDecimal refundAmount = BigDecimal.ZERO;
        if(CollectionUtil.isNotEmpty(skuAfterSaleOrderList)){
            refundAmount = skuAfterSaleOrderList.stream().map(AfterSaleOrderVO::getHandleNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            financialInvoiceMoneyVO.setRefundDeliveryFee(refundAmount);
        }
        financialInvoiceMoneyVO.setRefundAmount(financialInvoiceMoneyVO.getRefundAmount().add(refundAmount));

        // 配送费调整金额
        BigDecimal adjustmentDeliveryPrice = adjustmentDetailMapper.selectSuccessNumByOrderNo(orderAndItemDTO.getOrderNo());
        financialInvoiceMoneyVO.setAdjustTotalPrice(adjustmentDeliveryPrice.add(financialInvoiceMoneyVO.getAdjustTotalPrice()));
        financialInvoiceMoneyVO.setAdjustDeliveryFeeTotalPrice(adjustmentDeliveryPrice.add(financialInvoiceMoneyVO.getAdjustDeliveryFeeTotalPrice()));
    }



    @Override
    public AjaxResult invoiceOrderList(FinancialOrderQuery financialOrderQuery) {
        if(Objects.isNull(financialOrderQuery.getId())){
            return AjaxResult.getErrorWithMsg("提交参数错误，id不存在，请重新操作。");
        }

        List<FinancialInvoiceOrdernoRelation> orderNoAndOrderItemIdList = financialInvoiceOrdernoRelationMapper.selectByFinancialInvoiceId(financialOrderQuery.getId());
        if(CollectionUtil.isEmpty(orderNoAndOrderItemIdList)){
            return AjaxResult.getErrorWithMsg("该发票无已关联的订单");
        }

        Set<String> orderSet = new HashSet<>(orderNoAndOrderItemIdList.size());
        List<Long> orderItemIdList = new ArrayList<>(orderNoAndOrderItemIdList.size());
        for (FinancialInvoiceOrdernoRelation relation : orderNoAndOrderItemIdList) {
            if(Objects.nonNull(relation.getOrderNo())){
                orderSet.add(relation.getOrderNo());
            }
            if(Objects.nonNull(relation.getOrderItemId())){
                orderItemIdList.add(relation.getOrderItemId());
            }
        }

        financialOrderQuery.setOrderNoList(new ArrayList<>(orderSet));
        financialOrderQuery.setOrderItemIdList(orderItemIdList);

        List<FinancialOrderVO> financialOrderVOList = ordersMapper.selectFinancialOrders(financialOrderQuery);
        if(CollectionUtils.isEmpty(financialOrderVOList)){
            return AjaxResult.getOK();
        }

        // 精准送金额
        List<String> odernNoList = financialOrderVOList.stream().map(FinancialOrderVO::getOrderNo).collect(Collectors.toList());
        Map<String, OrderRelation> relationMap = orderRelationMapper.listActualPaidPrecision(odernNoList).stream().collect(Collectors.toMap(OrderRelation::getOrderNo, Function.identity()));
        for (FinancialOrderVO item : financialOrderVOList) {
            BigDecimal timeFrameFee = BigDecimal.ZERO;
            if (relationMap.containsKey(item.getOrderNo())) {
                OrderRelation orderRelation = relationMap.get(item.getOrderNo());
                timeFrameFee = orderRelation.getPrecisionDeliveryFee();
            }
            item.setTimeFrameFee(timeFrameFee);
            this.dealWithFinancialOrderVO(false, item);
        }
        // 账单跨期售后处理
        String billNumber = financialOrderVOList.get(0).getBillNumber();
        processAcrossPeriodWhenQuery(billNumber, financialOrderVOList);

        return AjaxResult.getOK(financialOrderVOList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateExpress(FinancialInvoice financialInvoiceInput){
        Long id = financialInvoiceInput.getId();
        String express = financialInvoiceInput.getExpress();
        if(Objects.isNull(id) || Objects.isNull(express)){
            return AjaxResult.getErrorWithMsg("id或express不能为空");
        }
        FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(id);
        if(!(Objects.nonNull(financialInvoice.getInvoiceType()) && Objects.equals(PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getId(),financialInvoice.getInvoiceType()))){
            return AjaxResult.getErrorWithMsg("发票状态异常");
        }
        // 编辑订单编号
        financialInvoiceMapper.updateExpress(id,express);
        return AjaxResult.getOK();
    }



    /**
     * 发送钉钉消息至发票发起人
     * @param financialInvoice 消息数据
     */
    private void sendDing(FinancialInvoice financialInvoice) {
        // 获取抬头信息
        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(financialInvoice.getInvoiceId());
        // 获取发票抬头的客户主体名称
        String customer = this.getRealName(invoiceConfig);
        String resultStr = Objects.deepEquals(INVOICED.ordinal(),financialInvoice.getInvoiceResult()) ? "已成功" : "被取消，请确认原因";
        if (financialInvoice.getCreatorId() == null || financialInvoice.getCreatorId() == 0) {
            return;
        }
        //userId有异常，将信息发送至开发人员钉钉
        String  title = "您" + financialInvoice.getCreateTime().getMonth().getValue() + "月" + financialInvoice.getCreateTime().getDayOfMonth() + "日" +"发起的" + "开票申请，" + resultStr;
        StringBuilder text = new StringBuilder(title);
        text.append("\n");
        text.append("> ###### 客户名称：").append(customer).append("\n");
        text.append("> ###### 开票金额：").append(financialInvoice.getAmountMoney()).append("\n");
        text.append("> ###### 发票类型：").append(Objects.deepEquals(PurchaseInvoiceAnalysisEnum.ELECTRONICS_TYPE.getId(),financialInvoice.getInvoiceType()) ? "增值税电子发票" : "增值税专用发票").append("\n");
        if(!StringUtils.isBlank(financialInvoice.getExpress())){
            text.append("> ###### 快递单号：").append(financialInvoice.getExpress()).append("\n");
        }
        if(!StringUtils.isBlank(financialInvoice.getHandlerRemark())){
            text.append("> ###### 备注：").append(financialInvoice.getHandlerRemark()).append("\n");
        }
        text.append("> ###### 如有疑问可至鲜沐后台查询开票详情");
        DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), null, title, text.toString());
        DingTalkMsgReceiverIdBO dingTalkFeishuMsgBO = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
        dingTalkFeishuMsgBO.setReceiverIdList(Arrays.asList(financialInvoice.getCreatorId().longValue()));
        dingTalkMsgSender.sendMessageWithFeiShu(dingTalkFeishuMsgBO);
    }

    /**
     * 大订单发起售后
     */
    private void sendBigFinancialDing(String orderNo, String afterSaleOrderNo, List<AfterSaleOrderVO> afterSaleOrderVOList) {
        if (CollectionUtils.isEmpty(afterSaleOrderVOList)) {
            return;
        }
        //根据订单号去找电票id 若没有电票的id return
        List<BigFinancialInvoiceSku> bigFinancialInvoiceSkus = bigFinancialInvoiceSkuMapper.selectByOrderNo(orderNo, 1);
        if (CollectionUtils.isEmpty(bigFinancialInvoiceSkus)){
            return;
        }
        List<Long> invoiceIds = bigFinancialInvoiceSkus.stream().map(BigFinancialInvoiceSku::getInvoiceId).distinct().collect(Collectors.toList());
        List<FinancialInvoice> financialInvoices = financialInvoiceMapper.selectByIds(invoiceIds);
        if (CollectionUtils.isEmpty(financialInvoices)) {
            return;
        }
        //找到电票 且票状态是正常的票
        Optional<FinancialInvoice> first = financialInvoices.stream().filter(it -> FinancialInvoiceEnum.invoiceType.isGeneral(it.getInvoiceType())).filter(it -> it.getInvoiceStatus().equals(FinancialInvoiceEnum.invoiceState.NORMAL.ordinal())).findFirst();
        if (!first.isPresent()) {
            return;
        }
        FinancialInvoice financialInvoice = first.get();
        BigDecimal price =  afterSaleOrderVOList.stream().map(AfterSaleOrderVO::getHandleNum).reduce(BigDecimal.ZERO, BigDecimal::add);
        String title = "超十万订单电票售后";
        StringBuilder text = new StringBuilder(title);
        text.append("\n");
        text.append("> ###### 蓝票抬头：").append(financialInvoice.getTitle()).append("\n");
        text.append("> ###### 订单号：").append(orderNo).append("\n");
        text.append("> ###### 订单开票时间：").append(DateUtil.formatDate(financialInvoice.getCreateTime())).append("\n");
        text.append("> ###### 售后金额：").append(price).append("\n");
        text.append("> ###### 售后单号：").append(afterSaleOrderNo).append("\n");
        text.append("> ###### 该订单已经开电票,售后需要红冲，请手动红冲!");
        DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), null, title, text.toString());
        DingTalkMsgReceiverIdBO feishuMessage = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
        feishuMessage.setReceiverIdList(Arrays.asList(XIAO_TINT_ID));
        dingTalkMsgSender.sendMessageWithFeiShu(feishuMessage);
    }

    /**
     * 获取发票抬头的客户主体名称
     * @param invoiceConfig 查询条件
     * @return 客户主体名称
     */
    @NotNull
    private String getRealName(InvoiceConfig invoiceConfig) {
        Integer adminId = invoiceConfig.getAdminId();
        Long merchantId = invoiceConfig.getMerchantId();
        StringBuilder customer = new StringBuilder("");

        if (Objects.nonNull(adminId)) {
            AdminVO adminVO = adminMapper.selectByAdminId(adminId);
            String merchantName = Objects.isNull(adminVO) ? "无关联商家" : adminVO.getRealname();
            customer.append(merchantName);
        }else if (Objects.nonNull(merchantId)) {
            Merchant merchant = merchantMapper.selectByMId(merchantId);
            customer.append(Objects.isNull(merchant) ? "" : merchant.getMname());
        }
        return customer.toString();
    }
    @Override
    public AjaxResult statusQuery() {
        String storeMallSwitch = configMapper.selectOne("storeMallSwitch").getValue();
        String brandMallSwitch = configMapper.selectOne("brandMallSwitch").getValue();
        JSONObject json = new JSONObject();
        json.put("storeMallSwitch", storeMallSwitch);
        json.put("brandMallSwitch", brandMallSwitch);
        return AjaxResult.getOK(json);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult statusUpdate(FinancialInvoiceQuery financialInvoiceQuery) {
        //品牌开关修改
        if (!ObjectUtils.isEmpty(financialInvoiceQuery.getStoreMallSwitch())) {
            Config config = new Config();
            config.setKey("storeMallSwitch");
            config.setValue(financialInvoiceQuery.getStoreMallSwitch());
            configMapper.update(config);
        }
        //单店开关修改
        if (!ObjectUtils.isEmpty(financialInvoiceQuery.getBrandMallSwitch())) {
            Config config = new Config();
            config.setKey("brandMallSwitch");
            config.setValue(financialInvoiceQuery.getBrandMallSwitch());
            configMapper.update(config);
        }
        return AjaxResult.getOK();
    }

    @Override
    public void sendAuditMessage(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, INVOICE_RESULT, ID);
            if (Objects.isNull(id)) {
                continue;
            }
            long invoiceId = Long.parseLong(id);
            FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(invoiceId);
            //专票需要有物流单号才可发钉钉消息，普票直接通知对应的申请人
            if(!(Objects.equals(PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getId(),financialInvoice.getInvoiceType())
                    && StringUtils.isEmpty(financialInvoice.getExpress()))){
                sendDing(financialInvoiceMapper.selectByPrimaryKey(invoiceId));
            }
        }
    }

    @Override
    public void sendUpdateExpressMessage(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, EXPRESS, ID);
            if (Objects.isNull(id)) {
                continue;
            }
            long invoiceId = Long.parseLong(id);
            FinancialInvoice financialInvoice = financialInvoiceMapper.selectByPrimaryKey(invoiceId);
            // 有快递单号且已开票后，可发钉钉消息至发起人
            if(Objects.equals(INVOICED.ordinal(),financialInvoice.getInvoiceResult())){
                financialInvoice.setExpress(financialInvoice.getExpress());
                sendDing(financialInvoice);
            }
        }
    }

    private static String cleanWeighStr(String weigh) {
        if (StringUtils.isEmpty(weigh)) {
            return "";
        }
        //根据 \换行
        if (StringUtils.isNotBlank(weigh)
                && weigh.length() > NumberUtils.INTEGER_THIRTY) {
            weigh = weigh.split("\\(")[0];
            if (weigh.length() > NumberUtils.INTEGER_THIRTY) {
                weigh = weigh.substring(0, NumberUtils.INTEGER_THIRTY);
            }
        }
        //去掉()
        int start = weigh.indexOf("(");
        if (start > 0) {
            weigh = weigh.substring(0, start);
        }
        return weigh;
    }

    @Override
    public List<FinancialInvoice> selectByIds(List<Long> invoiceIds) {
        return financialInvoiceMapper.selectByIds(invoiceIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean redoInvoice(FinancialInvoice financialInvoice) {
        Long id = financialInvoice.getId();
        // 针对发票id加分布式锁
        return xmLockTemplate.executeTryLock(lock -> {
            if (Boolean.TRUE.equals(lock)) {
                try {
                    // 具体操作
                    return redoFailedInvoice(financialInvoice);
                } catch (Exception e) {
                    logger.error("重做发票:{}失败", id, e);
                    return false;
                }
            } else {
                // 锁占用失败告警
                logger.error("发票:{}重做锁占用失败", id, new ProviderException("发票重做锁占用失败"));
                return false;
            }
        }, FinanceInvoiceRedisKeyEnum.REDO_LOCK.getKey() + id, 30000);
    }

    private boolean redoFailedInvoice(FinancialInvoice financialInvoice) {
        Long id = financialInvoice.getId();
        logger.info("开始重做发票，id:{}", id);
        if (financialInvoice.getRedoInvoiceId() > 0) {
            logger.info("发票:{}已重做，无需再次重做", id);
            return false;
        }

        FinancialOrderQuery financialOrderQuery = new FinancialOrderQuery();
        financialOrderQuery.setId(financialInvoice.getId());
        // 此接口可以返回该发票与之间之间的关系、金额等
        AjaxResult queryResult = invoiceOrderList(financialOrderQuery);
        if (!queryResult.isSuccess() || queryResult.getData() == null) {
            logger.info("查询发票关联订单失败，id:{}，失败原因:{}", financialInvoice.getId(), queryResult.getMsg());
            return false;
        }
        Object queryData = queryResult.getData();
        List<FinancialOrderVO> relations = (List<FinancialOrderVO>) queryData;

        // 拼装好格式调用invoiceSave方法
        FinancialInvoiceInput financialInvoiceInput = buildInvoiceSaveInput(financialInvoice, relations);
        if (financialInvoiceInput == null) {
            return false;
        }
        logger.info("重做发票:{}，参数:{}，即将调用发票接口...", financialInvoice.getId(), JSON.toJSONString(financialInvoiceInput));
        AjaxResult result = invoiceSave(financialInvoiceInput);
        if (!result.isSuccess()) {
            logger.error("重做发票:{}失败，原因:{}", financialInvoice.getId(), result.getMsg());
            return false;
        }
        Long newInvoiceId = (Long) result.getData();

        // 旧发票更新字段
        FinancialInvoice updateOld = new FinancialInvoice();
        updateOld.setId(id);
        updateOld.setRedoInvoiceId(newInvoiceId);
        financialInvoiceMapper.updateByPrimaryKeySelective(updateOld);

        FinancialInvoice updateNew = new FinancialInvoice();
        updateNew.setId(newInvoiceId);
        updateNew.setMId(financialInvoice.getMId());
        updateNew.setCreatorId(financialInvoice.getCreatorId());
        updateNew.setCreatorName(financialInvoice.getCreatorName());
        financialInvoiceMapper.updateByPrimaryKeySelective(updateNew);
        logger.info("重做发票结束，origin:{}, new:{}", id, newInvoiceId);
        return true;
    }

    /**
     * 组装参数
     * @param originInvoice 原票据信息
     * @param relations 发票订单关联关系
     * @return
     */
    private FinancialInvoiceInput buildInvoiceSaveInput(FinancialInvoice originInvoice, List<FinancialOrderVO> relations) {
        Long invoiceId = originInvoice.getInvoiceId();
        InvoiceConfig invoiceConfig = invoiceConfigMapper.selectByPrimaryKey(invoiceId);
        if (invoiceConfig == null) {
            logger.warn("未查询到发票:{}对应的抬头信息，重开流程结束", invoiceId);
            return null;
        }
        FinancialInvoiceInput financialInvoiceInput = new FinancialInvoiceInput();
        financialInvoiceInput.setInvoiceId(originInvoice.getInvoiceId());
        financialInvoiceInput.setInvoiceType(FinancialInvoiceEnum.invoiceType.transfer2Digital(originInvoice.getInvoiceType()));
        financialInvoiceInput.setBelongType(originInvoice.getBelongType());
        financialInvoiceInput.setPersonName(invoiceConfig.getInvoiceTitle());
        financialInvoiceInput.setPersonMail(invoiceConfig.getMailAddress());
        financialInvoiceInput.setCreatorRemark(originInvoice.getCreatorRemark());

        // 商品明细
        List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = Lists.newArrayListWithCapacity(relations.size());
        for (FinancialOrderVO orderInfo : relations) {
            FinancialInvoiceOrderDTO financialInvoiceOrderDTO = new FinancialInvoiceOrderDTO();
            financialInvoiceOrderDTO.setOrderNo(orderInfo.getOrderNo());
            financialInvoiceOrderDTO.setDeliveryFee(orderInfo.getDeliveryFee());
            financialInvoiceOrderDTO.setOutTimesFee(orderInfo.getOutTimesFee());
            List<FinancialOrderItemVO> financialOrderItemList = orderInfo.getFinancialOrderItemList();
            if (CollectionUtils.isEmpty(financialOrderItemList)) {
                logger.warn("发票:{}, 订单:{}没有明细信息，跳过", invoiceId, orderInfo.getOrderNo());
                return null;
            }
            List<FinancialInvoiceOrderItemDTO> invoiceOrderItemDTOList = Lists.newArrayListWithCapacity(financialOrderItemList.size());
            for (FinancialOrderItemVO orderItem : financialOrderItemList) {
                FinancialInvoiceOrderItemDTO financialInvoiceOrderItemDTO = new FinancialInvoiceOrderItemDTO();
                financialInvoiceOrderItemDTO.setOrderItemId(orderItem.getOrderItemId());
                financialInvoiceOrderItemDTO.setSku(orderItem.getSku());
                financialInvoiceOrderItemDTO.setTaxRateValue(orderItem.getTaxRateValue());
                invoiceOrderItemDTOList.add(financialInvoiceOrderItemDTO);
            }
            financialInvoiceOrderDTO.setInvoiceOrderItemDTOList(invoiceOrderItemDTOList);
            financialInvoiceOrderDTOList.add(financialInvoiceOrderDTO);
        }
        financialInvoiceInput.setFinancialInvoiceOrderDTOList(financialInvoiceOrderDTOList);
        return financialInvoiceInput;
    }

    @Override
    public List<FinancialInvoice> selectRedoInvoice(FinancialFailedInvoiceQuery query) {
        return financialInvoiceMapper.selectRedoInvoices(query);
    }
}


