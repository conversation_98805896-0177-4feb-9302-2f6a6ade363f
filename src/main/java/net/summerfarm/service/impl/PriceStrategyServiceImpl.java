package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.InventoryAdjustPriceTypeEnum;
import net.summerfarm.enums.PriceStrategyTypeEnum;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.PriceStrategyMapper;
import net.summerfarm.model.DTO.market.ActivityLadderPriceDTO;
import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.model.domain.PriceStrategy;
import net.summerfarm.model.vo.LadderPriceVO;
import net.summerfarm.model.vo.PriceStrategyAuditRecordVO;
import net.summerfarm.model.vo.PriceStrategyVO;
import net.summerfarm.service.PriceStrategyAuditService;
import net.summerfarm.service.PriceStrategyService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PriceStrategyServiceImpl implements PriceStrategyService {
    @Resource
    private PriceStrategyMapper priceStrategyMapper;
    @Lazy
    @Resource
    private PriceStrategyAuditService priceStrategyAuditService;
    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Override
    public PriceStrategyAuditRecordVO calcStrategyPrice(PriceStrategyTypeEnum typeEnum, Long businessId, BigDecimal costPrice, BigDecimal originalPrice) {
        PriceStrategy priceStrategy = priceStrategyMapper.select(businessId, typeEnum.getType());
        if (priceStrategy == null) {
            throw new DefaultServiceException("无价格计算数据，type：" + typeEnum.getType() + "，businessId：" + businessId);
        }

        return calcStrategyPrice(priceStrategy, costPrice, originalPrice);
    }

    @Override
    public PriceStrategyAuditRecordVO calcStrategyPrice(PriceStrategy priceStrategy, BigDecimal costPrice, BigDecimal originalPrice) {
        BigDecimal resultPrice;
        boolean deFlag = true;
        //指定价
        if (Objects.equals(InventoryAdjustPriceTypeEnum.APPOINT.getType(), priceStrategy.getAdjustType())) {
            resultPrice = priceStrategy.getAmount().compareTo(originalPrice) <= 0 ? priceStrategy.getAmount() : originalPrice;
        }//百分比
        else if (Objects.equals(InventoryAdjustPriceTypeEnum.PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
            resultPrice = originalPrice.multiply(priceStrategy.getAmount());
        }//定额减
        else if (Objects.equals(InventoryAdjustPriceTypeEnum.QUOTA_REDUCTION.getType(), priceStrategy.getAdjustType())) {
            resultPrice = originalPrice.subtract(priceStrategy.getAmount());
        } //毛利百分比
        else if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
            if (costPrice == null || BigDecimal.ZERO.compareTo(costPrice) == 0) {
                resultPrice = originalPrice;
            } else if (costPrice.compareTo(originalPrice) > 0) {
                //原价小于成本价是不做向上取整
                deFlag = false;
                resultPrice = originalPrice;
            } else {
                resultPrice = originalPrice.subtract((originalPrice.subtract(costPrice).multiply(priceStrategy.getAmount())));
            }
        } else {
            throw new DefaultServiceException("无价格计算数据，type：" + priceStrategy.getType() + "，businessId：" + priceStrategy.getBusinessId());
        }

        //价格异常处理
        if (resultPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("价格计算异常，type：{}，businessId：{}， 计算后的价格：{}", priceStrategy.getType(), priceStrategy.getBusinessId(), resultPrice);
            resultPrice = originalPrice;
        }

        //小数处理
        if (priceStrategy.getRoundingMode() == null || Objects.equals(priceStrategy.getRoundingMode(), 0)) {
            resultPrice = resultPrice.setScale(2, RoundingMode.HALF_UP);
        } else if (deFlag) {
            resultPrice = resultPrice.setScale(0, RoundingMode.CEILING);
        }

        PriceStrategyAuditRecordVO record = new PriceStrategyAuditRecordVO();
        record.setStrategyId(priceStrategy.getId());
        record.setOldPrice(priceStrategy.getEffectivePrice());
        record.setNewPrice(resultPrice);
        record.setPriceStrategyType(priceStrategy.getType());
        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdate(PriceStrategyTypeEnum typeEnum, List<PriceStrategy> strategyList, Integer areaNo, String sku, Integer bizId) {
        if (CollectionUtils.isEmpty(strategyList)) {
            return;
        }

        //搭配购和组合包不触发审批、不计算价格
        if (PriceStrategyTypeEnum.COLLOCATION.equals(typeEnum) || PriceStrategyTypeEnum.SUIT.equals(typeEnum)) {
            for (PriceStrategy strategy : strategyList) {
                PriceStrategy record = priceStrategyMapper.select(strategy.getBusinessId(), typeEnum.getType());
                if (record != null) {
                    strategy.setId(record.getId());
                    priceStrategyMapper.updateSelectiveByBusinessId(strategy);
                } else {
                    priceStrategyMapper.insertSelective(strategy);
                }
            }
            return;
        }

        AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        for (PriceStrategy strategy : strategyList) {
            PriceStrategy record = priceStrategyMapper.select(strategy.getBusinessId(), typeEnum.getType());
            if (record != null) {
                strategy.setId(record.getId());
                priceStrategyMapper.updateSelectiveByBusinessId(strategy);
            } else {
                if (strategy.getEffectivePrice() == null){
                    strategy.setEffectivePrice(areaSku.getPrice());
                }
                priceStrategyMapper.insertSelective(strategy);
            }
        }
        //触发审批
        priceStrategyAuditService.initPriceStrategyAudit(areaNo, sku, areaSku.getPrice(), typeEnum, bizId);

    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void insertOrUpdate(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer areaNo, String sku, Integer bizId) {
        BigDecimal price = insertOrUpdateWithoutDing(typeEnum, strategy, areaNo, sku, bizId);

        //搭配购和组合包不触发审批
        if (PriceStrategyTypeEnum.COLLOCATION.equals(typeEnum) || PriceStrategyTypeEnum.SUIT.equals(typeEnum)) {
           return;
        }
        priceStrategyAuditService.initPriceStrategyAudit(areaNo, sku, price, typeEnum, bizId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public BigDecimal insertOrUpdateWithoutDing(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer areaNo, String sku, Integer bizId) {
        PriceStrategy record = priceStrategyMapper.select(strategy.getBusinessId(), typeEnum.getType());

        //搭配购和组合包不触发审批、不计算价格
        if (PriceStrategyTypeEnum.COLLOCATION.equals(typeEnum) || PriceStrategyTypeEnum.SUIT.equals(typeEnum)) {
            if (record != null) {
                strategy.setId(record.getId());
                priceStrategyMapper.updateSelectiveByBusinessId(strategy);
            } else {
                priceStrategyMapper.insertSelective(strategy);
            }
            return null;
        }

        AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        if (record != null) {
            strategy.setId(record.getId());
            priceStrategyMapper.updateSelectiveByBusinessId(strategy);
        } else {
            if (strategy.getEffectivePrice() == null) {
                strategy.setEffectivePrice(areaSku.getPrice());
            }
            priceStrategyMapper.insertSelective(strategy);
        }

        return areaSku.getPrice();
    }

    @Override
    public BigDecimal insertOrUpdateWithoutDingByExpandActivity(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer bizId) {
        PriceStrategy record = priceStrategyMapper.select(strategy.getBusinessId(), typeEnum.getType());
        //拓展购买专用
            if (record != null) {
                strategy.setId(record.getId());
                priceStrategyMapper.updateSelectiveByBusinessId(strategy);
            } else {
                priceStrategyMapper.insertSelective(strategy);
            }
            return null;
    }

    @Override
    public BigDecimal insertOrUpdateWithoutDingByExchangeActivity(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer bizId) {
        PriceStrategy record = priceStrategyMapper.select(strategy.getBusinessId(), 7);
        //换购专用
        if (record != null) {
            strategy.setId(record.getId());
            priceStrategyMapper.updateSelectiveByBusinessId(strategy);
        } else {
            priceStrategyMapper.insertSelective(strategy);
        }
        return null;
    }

    @Override
    public PriceStrategyVO selectPriceStrategyVO(PriceStrategyTypeEnum typeEnum, Long businessId) {
        PriceStrategy strategy = priceStrategyMapper.select(businessId, typeEnum.getType());
        if (strategy == null) {
            return null;
        }

        PriceStrategyVO vo = new PriceStrategyVO();
        vo.setAdjustType(strategy.getAdjustType());
        vo.setAmount(strategy.getAmount());
        vo.setRoundingMode(strategy.getRoundingMode());
        vo.setEffectivePrice(strategy.getEffectivePrice());

        //审核状态
        Integer auditStatus = priceStrategyAuditService.queryNewestAuditStatus(Long.valueOf(strategy.getId()));
        vo.setAuditStatus(auditStatus);

        return vo;
    }

    @Override
    public BigDecimal selectLadderPriceByUnit(List<ActivityLadderPriceDTO> priceList, Integer unit, BigDecimal salePrice) {
        log.info("开始根据阶梯获取对应的价格, priceList: {}, unit :{}, salePrice: {}", JSON.toJSONString(priceList), unit, salePrice);
        if(CollectionUtils.isEmpty(priceList)) {
            log.info("阶梯价列表为空！");
            return salePrice;
        }
        if(unit == null || unit <= 0) {
            log.info("计算活动阶梯价时，购买数量异常, unit:{}", unit);
            unit = 1;
        }

        ActivityLadderPriceDTO validLadder = null;
        for (ActivityLadderPriceDTO vo : priceList) {
            if (vo.getUnit() <= unit) {
                if (validLadder == null || validLadder.getUnit() < vo.getUnit()) {
                    validLadder = vo;
                }
            }
        }
        return validLadder == null ? salePrice : validLadder.getPrice();
    }
}
