package net.summerfarm.service.impl.item;

import com.cofso.item.client.enums.*;
import com.cofso.item.client.req.*;
import com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq;
import com.cosfo.manage.client.product.resp.SummerFarmSynchronizedSkuResp;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.ItemProductInfoUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.ProductsEnum;
import net.summerfarm.facade.item.MarketProviderFacade;
import net.summerfarm.facade.item.dto.MarketItemInfoDTO;
import net.summerfarm.facade.item.dto.MarketItemOnsaleDTO;
import net.summerfarm.facade.saas.ProductProviderFacade;
import net.summerfarm.goods.client.enums.MaterialTypeEnum;
import net.summerfarm.goods.client.req.XmSyncSkuReq;
import net.summerfarm.goods.client.resp.GoodsSyncSkuResp;
import net.summerfarm.manage.client.inventory.enums.SkuOutdatedEnum;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.ProductSyncRecordMapper;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.item.ProductSyncToItemInput;
import net.summerfarm.model.vo.CategoryVO;
import net.summerfarm.model.vo.inventory.SkuSyncToItemVO;
import net.summerfarm.service.CategoryService;
import net.summerfarm.service.InventoryService;
import net.summerfarm.service.ProductsPropertyValueService;
import net.summerfarm.service.item.ProductSyncToItemService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.common.util.ItemProductInfoUtil.ITEM_CLASSIFICATION_ID;

/**
 * <AUTHOR>
 * @version 1.0.0，.1
 * @date 2023-05-06
 * @description 同步商品到商品中心
 */
@Slf4j
@Service
public class ProductSyncToItemServiceImpl implements ProductSyncToItemService {

    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ProductsPropertyValueService productsPropertyValueService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private ProductSyncRecordMapper productSyncRecordMapper;
    @Resource
    ProductProviderFacade productProviderFacade;
    @Resource
    private MarketProviderFacade marketProviderFacade;
    private static Long CLASSIFICATION_ID = null;


    @Override
    public void skuInsertSyncToItem(Long skuId){
        List<XmSyncSkuReq> synchronizedSkuReq = inventoryService.queryNeedSyncSkuListForGoods(null,Lists.newArrayList(skuId));
        if (CollectionUtils.isEmpty(synchronizedSkuReq)){
            log.error("skuInsertSyncToItem skuId:{},数据异常,无法同步",skuId);
            return;
        }
        XmSyncSkuReq skuReq = buildSyncSkuReq(synchronizedSkuReq.get(0));
        GoodsSyncSkuResp skuResp = productProviderFacade.doneSkuSynchronizedProxy(skuReq);
        if (Objects.isNull(skuResp)){
            return;
        }
        List<SkuSyncToItemVO> skuSyncToItemVO = inventoryMapper.selectNeedSyncToItemSku(Sets.newHashSet(skuId));
        if (CollectionUtils.isEmpty(skuSyncToItemVO)){
            return;
        }
        MarketInputReq marketInputReq = buildMarketInputReq(skuSyncToItemVO.get(0));
        MarketItemInputReq marketItemInputReq = buildMarketItemInputReq(skuSyncToItemVO.get(0),skuResp.getSkuId());
        marketInputReq.setMarketItemInputReq(marketItemInputReq);
        marketProviderFacade.addMarketProxy(marketInputReq);

    }
    @Override
    public void skuUpdateSyncToItem(Long skuId) {
        List<SkuSyncToItemVO> skuSyncToItemVO = inventoryMapper.selectNeedSyncToItemSku(Sets.newHashSet(skuId));
        if (CollectionUtils.isEmpty(skuSyncToItemVO)){
            log.error("skuUpdateSyncToItem skuId:{},数据异常,无法同步",skuId);
            return;
        }
        MarketItemInputReq marketItemInputReq = buildMarketItemInputReq(skuSyncToItemVO.get(0),null);
        marketProviderFacade.updateMarketItemProxy(marketItemInputReq);
    }
    @Override
    public void productsUpdateSyncToItem(Long pdId) {
        List<SkuSyncToItemVO> skuSyncToItemVO = inventoryMapper.selectNeedSyncToItemByPdId(Sets.newHashSet(pdId));
        if (CollectionUtils.isEmpty(skuSyncToItemVO)){
            log.error("productsUpdateSyncToItem pdId:{},数据异常,无法同步",pdId);
            return;
        }
        MarketInputReq marketInputReq = buildMarketInputReq(skuSyncToItemVO.get(0));
        marketProviderFacade.updateMarketProxy(marketInputReq);
    }

    @Override
    public void areaSkuInsertSyncToItem(Integer areaNo,String sku) {
        Inventory inventory = inventoryMapper.selectOneBySku(sku);
        if (Objects.isNull(inventory)
                ||  SkuOutdatedEnum.CREATING.getCode().equals(inventory.getOutdated())
                || !ItemProductInfoUtil.SYNC_SKU_CREATE_TYPE_LIST.contains(Integer.valueOf(inventory.getCreateType()))){
            return;
        }
        if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))) {
            log.error("areaSkuInsertSyncToItem error ,sku未同步到商品中心,sku:{}",inventory.getSku());
            return;
        }
        List<AreaSku> areaSkus = areaSkuMapper.selectListBySkuAndAreaNo(sku, areaNo);
        if (CollectionUtils.isEmpty(areaSkus)){
            return;
        }
        if (Objects.isNull(areaSkus.get(0).getPrice())||areaSkus.get(0).getPrice().compareTo(BigDecimal.ZERO) <= 0){
            log.error("areaSkuInsertSyncToItem价格无效,不同步,sku:{},areaNo:{}",areaSkus.get(0).getSku(),areaSkus.get(0).getAreaNo());
            return;
        }
        marketProviderFacade.initMarketItemPriceOnSaleStrataryProxy(inventory.getInvId(),areaSkus.get(0));
    }

    @Override
    public void skuUpdateSyncToSku(Long skuId) {
//        List<SummerFarmSynchronizedSkuReq> synchronizedSkuReqs = inventoryService.queryNeedSyncSkuList(null,Lists.newArrayList(skuId));
        List<XmSyncSkuReq> synchronizedSkuReqs = inventoryService.queryNeedSyncSkuListForGoods(null,Lists.newArrayList(skuId));
        if (CollectionUtils.isEmpty(synchronizedSkuReqs)){
            log.error("skuUpdateSyncToSku skuId:{},数据异常,无法同步\n",skuId);
            return;
        }
        syncSkuToSaas(synchronizedSkuReqs);
    }

    @Override
    public void productsUpdateSyncToSpu(Long pdId) {
        List<XmSyncSkuReq> synchronizedSkuReqs = inventoryService.queryNeedSyncSkuListForGoods(Lists.newArrayList(pdId),null);
        if (CollectionUtils.isEmpty(synchronizedSkuReqs)){
            log.error("productsUpdateSyncToSpu pdId:{},数据异常,无法同步",pdId);
            return;
        }
        syncSkuToSaas(synchronizedSkuReqs);
    }
    @Override
    public void propertyUpdateSyncToSpu(Long pdId) {
        List<XmSyncSkuReq> synchronizedSkuReqs = inventoryService.queryNeedSyncSkuListForGoods(Lists.newArrayList(pdId),null);
        if (CollectionUtils.isEmpty(synchronizedSkuReqs)){
            log.error("propertyUpdateSyncToSpu pdId:{},数据异常,无法同步",pdId);
            return;
        }
        syncSkuToSaas(synchronizedSkuReqs);
    }
    @Override
    public void skuToItemTask(String startTime,String endTime,Long skuId,Long maxSkuId) {
        List<Inventory> inventories = null;
        int nCount = 0;
        while(!CollectionUtils.isEmpty(inventories = inventoryMapper.queryNeedSyncSkuInfo(startTime,endTime,skuId,maxSkuId))) {
            skuId = inventories.stream().max(Comparator.comparing(Inventory::getInvId)).get().getInvId();
            for (Inventory inventory : inventories){
                try {
                    List<SummerFarmSynchronizedSkuReq> synchronizedSkuReq = inventoryService.queryNeedSyncSkuList(null,Lists.newArrayList(inventory.getInvId()));
                    if (CollectionUtils.isEmpty(synchronizedSkuReq)){
                        log.error("skuToItemTask skuId:{},数据异常,无法同步",inventory.getInvId());
                        continue;
                    }
                    if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))){
                        this.skuInsertSyncToItem(inventory.getInvId());
                    }else {
                        //同步到货品
                        this.skuUpdateSyncToSku(inventory.getInvId());
                        //同步商品中心
                        this.skuUpdateSyncToItem(inventory.getInvId());
                        this.productsUpdateSyncToItem(inventory.getPdId());
                    }
                    nCount++;
                } catch (Exception e) {
                    log.error("skuToItemTask error:sku:{},e:{}",inventory.getSku(),Throwables.getStackTraceAsString(e));
                }
            }
        }
        log.info("skuToItemTask任务受理完毕，本次受理查询提现 {} 条", nCount);
    }

    @Override
    public void skuAndAreaSkuToItemTask(Long skuId,Long maxSkuId) {
        List<Inventory> inventories = null;
        int nCount = 0;
        while(!CollectionUtils.isEmpty(inventories = inventoryMapper.queryNeedSyncSkuInfo(null,null,skuId,maxSkuId))) {
            skuId = inventories.stream().max(Comparator.comparing(Inventory::getInvId)).get().getInvId();
            for (Inventory inventory : inventories){
                try {
                    List<SummerFarmSynchronizedSkuReq> synchronizedSkuReq = inventoryService.queryNeedSyncSkuList(null,Lists.newArrayList(inventory.getInvId()));
                    if (CollectionUtils.isEmpty(synchronizedSkuReq)){
                        log.error("skuAndAreaSkuToItemTask skuId:{},数据异常,无法同步",inventory.getInvId());
                        continue;
                    }
                    if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))){
                        this.skuInsertSyncToItem(inventory.getInvId());
                    }else {
                        //同步到货品
                        this.skuUpdateSyncToSku(inventory.getInvId());
                        //同步商品中心
                        this.skuUpdateSyncToItem(inventory.getInvId());
                        this.productsUpdateSyncToItem(inventory.getPdId());
                    }
                    List<AreaSku> areaSkus = areaSkuMapper.selectListBySkuAndAreaNo(inventory.getSku(), null);
                    if (CollectionUtils.isEmpty(areaSkus)) {
                        continue;
                    }
                    if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))){
                        log.info("skuAndAreaSkuToItemTask error sku在商品中心不存在:sku:{}",inventory.getSku());
                        continue;
                    }
                    for (AreaSku areaSku : areaSkus) {
                        if (Objects.isNull(areaSku.getPrice()) || areaSku.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                            log.error("skuAndAreaSkuToItemTask价格无效,不同步,sku:{},areaNo:{}", areaSku.getSku(), areaSku.getAreaNo());
                            continue;
                        }
                        try {
                            marketProviderFacade.initMarketItemPriceOnSaleStrataryProxy(inventory.getInvId(),areaSku);
                            nCount ++;
                        } catch (Exception e) {
                            log.error("skuAndAreaSkuToItemTask error:sku:{},areaNo{},e:{}",areaSku.getSku(),areaSku.getAreaNo(),Throwables.getStackTraceAsString(e));
                        }
                    }
                    nCount++;
                } catch (Exception e) {
                    log.error("skuAndAreaSkuToItemTask error:sku:{},e:{}",inventory.getSku(),Throwables.getStackTraceAsString(e));
                }
            }
        }
        log.info("initSkuAndItem任务受理完毕，本次受理查询提现 {} 条", nCount);
    }

    @Override
    public void upsertSkuAndAreaToItem(List<Long> skuIds) {
        List<SkuSyncToItemVO> skuSyncToItemVOs = inventoryMapper.selectNeedSyncToItemSku(Sets.newHashSet(skuIds));
        if (!CollectionUtils.isEmpty(skuSyncToItemVOs)){
            int nCount = 0;
            for (SkuSyncToItemVO inventory : skuSyncToItemVOs){
                try {
                    if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))){
                        this.skuInsertSyncToItem(inventory.getInvId());
                    }else {
                        //同步到货品
                        this.skuUpdateSyncToSku(inventory.getInvId());
                        //同步商品中心
                        this.productsUpdateSyncToItem(inventory.getPdId());
                        this.skuUpdateSyncToItem(inventory.getInvId());
                    }
                    if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))){
                        log.info("upsertSkuAndAreaToItem error sku在商品中心不存在:sku:{}",inventory.getSku());
                        continue;
                    }
                    List<AreaSku> areaSkus = areaSkuMapper.selectListBySkuAndAreaNo(inventory.getSku(), null);
                    if (CollectionUtils.isEmpty(areaSkus)) {
                        continue;
                    }
                    for (AreaSku areaSku : areaSkus) {
                        if (Objects.isNull(areaSku.getPrice()) || areaSku.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                            log.error("upsertSkuAndAreaToItem价格无效,不同步,sku:{},areaNo:{}", areaSku.getSku(), areaSku.getAreaNo());
                            continue;
                        }
                        try {
                            marketProviderFacade.initMarketItemPriceOnSaleStrataryProxy(inventory.getInvId(),areaSku);
                            nCount ++;
                        } catch (Exception e) {
                            log.error("upsertSkuAndAreaToItem error:sku:{},areaNo{},e:{}",areaSku.getSku(),areaSku.getAreaNo(),Throwables.getStackTraceAsString(e));
                        }
                    }
                    nCount++;
                } catch (Exception e) {
                    log.error("upsertSkuAndAreaToItem error:sku:{},e:{}",inventory.getSku(),Throwables.getStackTraceAsString(e));
                }
            }
        }
    }

    @Override
    public void upsertSkuToItem(List<Long> skuIds) {
        List<SkuSyncToItemVO> skuSyncToItemVOs = inventoryMapper.selectNeedSyncToItemSku(Sets.newHashSet(skuIds));
        if (!CollectionUtils.isEmpty(skuSyncToItemVOs)){
            for (SkuSyncToItemVO inventory : skuSyncToItemVOs){
                try {
                    if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))){
                        this.skuInsertSyncToItem(inventory.getInvId());
                    }else {
                        //同步到货品
                        this.skuUpdateSyncToSku(inventory.getInvId());
                        //同步商品中心
                        this.productsUpdateSyncToItem(inventory.getPdId());
                        this.skuUpdateSyncToItem(inventory.getInvId());
                    }
                } catch (Exception e) {
                    log.error("upsertSkuToItem error:sku:{},e:{}",inventory.getSku(),Throwables.getStackTraceAsString(e));
                }
            }
        }
    }


    @Override
    public void upsertAreaSkuToItemTask(Long skuId,Long maxSkuId){
        List<Inventory> inventories = null;
        int nCount = 0;
        while(!CollectionUtils.isEmpty(inventories = inventoryMapper.queryNeedSyncSkuInfo(null,null,skuId,maxSkuId))) {
            skuId = inventories.stream().max(Comparator.comparing(Inventory::getInvId)).get().getInvId();
            Iterator<Inventory> iterator = inventories.iterator();
            while (iterator.hasNext()) {
                Inventory inventory = iterator.next();
                if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,inventory.getSku()))){
                    log.info("upsertAreaSkuToItemTask error sku在商品中心不存在:sku:{}",inventory.getSku());
                    continue;
                }
                List<AreaSku> areaSkus = areaSkuMapper.selectListBySkuAndAreaNo(inventory.getSku(), null);
                if (CollectionUtils.isEmpty(areaSkus)) {
                    continue;
                }
                for (AreaSku areaSku : areaSkus) {
                    if (Objects.isNull(areaSku.getPrice()) || areaSku.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                        log.error("upsertAreaSkuToItemTask价格无效,不同步,sku:{},areaNo:{}", areaSku.getSku(), areaSku.getAreaNo());
                        continue;
                    }
                    try {
                        marketProviderFacade.initMarketItemPriceOnSaleStrataryProxy(inventory.getInvId(),areaSku);
                        nCount ++;
                    } catch (Exception e) {
                        log.error("upsertAreaSkuToItemTask error:sku:{},areaNo{},e:{}",areaSku.getSku(),areaSku.getAreaNo(),Throwables.getStackTraceAsString(e));
                    }
                }
            }
        }
        log.info("upsertAreaSkuToItemCenter任务受理完毕，本次受理areasku同步 {} 条", nCount);
    }

    @Override
    public void updateAreaSkuToItemTask(String startTime,String endTime,Integer id,Integer maxId) {
        int nCount = 0;
        List<AreaSku> areaSkus = null;
        while(!CollectionUtils.isEmpty(areaSkus = areaSkuMapper.selectNeedSyncByUpdateTime(startTime,endTime,id,maxId))) {
            id = areaSkus.stream().max(Comparator.comparing(AreaSku::getId)).get().getId();
            Map<String,List<AreaSku>>  areaSkuMap = areaSkus.stream().collect(Collectors.groupingBy(AreaSku::getSku));
            List<Inventory> inventories = inventoryMapper.selectBySkus(Lists.newArrayList(areaSkuMap.keySet()));
            Map<String,Inventory> skuMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(inventories)){
                skuMap = inventories.stream().collect(Collectors.toMap(Inventory::getSku,Function.identity()));
            }

            for (Map.Entry<String,List<AreaSku>> entry :areaSkuMap.entrySet()){
                String sku = entry.getKey();
                Inventory inventory = skuMap.get(sku);
                if (Objects.isNull(inventory)){
                    continue;
                }
                if (Objects.isNull(marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID,sku))){
                    log.info("updateAreaSkuToItemTask error sku在商品中心不存在:sku:{}",sku);
                    continue;
                }
                List<AreaSku> areaSkuList = entry.getValue();
                for (AreaSku areaSku : areaSkuList){
                    if (Objects.isNull(areaSku.getPrice()) || areaSku.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                        log.error("updateAreaSkuToItemTask价格无效,不同步,sku:{},areaNo:{}", areaSku.getSku(), areaSku.getAreaNo());
                        continue;
                    }
                    try {
                        marketProviderFacade.initMarketItemPriceOnSaleStrataryProxy(inventory.getInvId(),areaSku);
                        nCount ++;
                    } catch (Exception e) {
                        log.error("updateAreaSkuToItemTask error:sku:{},areaNo{},e:{}",areaSku.getSku(),areaSku.getAreaNo(),Throwables.getStackTraceAsString(e));
                    }
                }
            }
        }
        log.info("updateAreaSkuToItemTask任务受理完毕，本次受理areasku同步 {} 条", nCount);
    }
    public void updateAreaSkuToItemOffsetsTask(ProductSyncToItemInput input) {
        int nCount = 0;
        List<AreaSku> areaSkus = null;
        Integer maxId = areaSkuMapper.getMaxId();
        while(!CollectionUtils.isEmpty(areaSkus = areaSkuMapper.selectNeedSyncByUpdateTimeDesc(input.getStartTime(),input.getEndTime(),maxId))) {
            maxId = areaSkus.stream().min(Comparator.comparing(AreaSku::getId)).get().getId();
            Map<String,List<AreaSku>>  areaSkuMap = areaSkus.stream().collect(Collectors.groupingBy(AreaSku::getSku));
            List<Inventory> inventories = inventoryMapper.selectBySkus(Lists.newArrayList(areaSkuMap.keySet()));
            Map<String,Inventory> skuMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(inventories)){
                skuMap = inventories.stream().collect(Collectors.toMap(Inventory::getSku,Function.identity()));
            }

            for (Map.Entry<String,List<AreaSku>> entry :areaSkuMap.entrySet()){
                String sku = entry.getKey();
                Inventory inventory = skuMap.get(sku);
                if (Objects.isNull(inventory)){
                    continue;
                }
                MarketItemInfoDTO marketItemInfoDTO = marketProviderFacade.getMarketItemDetailByItemCode(ItemProductInfoUtil.TENANT_ID,sku);
                if (Objects.isNull(marketItemInfoDTO)){
                    this.skuInsertSyncToItem(inventory.getInvId());
                    marketItemInfoDTO = marketProviderFacade.getMarketItemDetailByItemCode(ItemProductInfoUtil.TENANT_ID,sku);
                    if (Objects.isNull(marketItemInfoDTO)){
                        continue;
                    }
                }
                List<MarketItemOnsaleDTO> onsaleStrategyList = marketItemInfoDTO.getOnsaleStrategyList();
                List<Integer> areaNos = Lists.newArrayList();
                if (!CollectionUtils.isEmpty(onsaleStrategyList)){
                    areaNos = onsaleStrategyList.stream().map(e->e.getTargetId().intValue()).collect(Collectors.toList());
                }
                List<AreaSku> areaSkuList = entry.getValue();
                for (AreaSku areaSku : areaSkuList){
                    if (areaNos.contains(areaSku.getAreaNo())){
                        continue;
                    }
                    if (Objects.isNull(areaSku.getPrice()) || areaSku.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                        log.error("updateAreaSkuToItemTask价格无效,不同步,sku:{},areaNo:{}", areaSku.getSku(), areaSku.getAreaNo());
                        continue;
                    }
                    try {
                        marketProviderFacade.initMarketItemPriceOnSaleStrataryProxy(inventory.getInvId(),areaSku);
                        nCount ++;
                    } catch (Exception e) {
                        log.error("updateAreaSkuToItemOffsetsTask error:sku:{},areaNo{},e:{}",areaSku.getSku(),areaSku.getAreaNo(),Throwables.getStackTraceAsString(e));
                    }
                }
            }
        }
        log.info("updateAreaSkuToItemOffsetsTask任务受理完毕，本次受理areasku同步 {} 条", nCount);
    }
    private void syncSkuToSaas(List<XmSyncSkuReq> synchronizedSkuReqs) {
        if (CollectionUtils.isEmpty(synchronizedSkuReqs)){
            return;
        }
        XmSyncSkuReq skuReq = buildSyncSkuReq(synchronizedSkuReqs.get(0));
        productProviderFacade.doneSkuSynchronizedProxy(skuReq);
    }

    public MarketInputReq buildMarketInputReq( SkuSyncToItemVO skuSyncToItemVO){
        MarketInputReq marketInputReq = new MarketInputReq();
        marketInputReq.setTenantId(ItemProductInfoUtil.TENANT_ID);
        marketInputReq.setTitle(skuSyncToItemVO.getPdName());
        marketInputReq.setSubTitle(skuSyncToItemVO.getPddetail());
        marketInputReq.setCategoryId(skuSyncToItemVO.getCategoryId());
        marketInputReq.setMainPicture(skuSyncToItemVO.getPicturePath());
        marketInputReq.setDetailPicture(skuSyncToItemVO.getDetailPicture());
        if (ProductsEnum.Outdated.VALID.getCode().equals(skuSyncToItemVO.getOutdated())){
            marketInputReq.setDeleteFlag(ProductsEnum.Outdated.IN_VALID.getCode());
        } else if (ProductsEnum.Outdated.IN_VALID.getCode().equals(skuSyncToItemVO.getOutdated())) {
            marketInputReq.setDeleteFlag(ProductsEnum.Outdated.VALID.getCode());
        }else {
            marketInputReq.setDeleteFlag(skuSyncToItemVO.getSkuOutdated());
        }
        marketInputReq.setOutId(skuSyncToItemVO.getPdId());
        marketInputReq.setAfterSaleTime(skuSyncToItemVO.getAfterSaleTime());
        marketInputReq.setAfterSaleReason(skuSyncToItemVO.getAfterSaleType());
        marketInputReq.setRefundReason(skuSyncToItemVO.getRefundType());
        marketInputReq.setSlogan(skuSyncToItemVO.getOtherSlogan());
        marketInputReq.setCreateTime(skuSyncToItemVO.getAddTime());
        marketInputReq.setUpdateTime(skuSyncToItemVO.getUpdateTime());
        marketInputReq.setClassificationId(getItemClassificationId());
//        marketInputReq.setDescription();
        return marketInputReq;
    }
    private MarketItemInputReq buildMarketItemInputReq(SkuSyncToItemVO skuSyncToItemVO,Long skuId){
        MarketItemInputReq marketItemInputReq = new MarketItemInputReq();
        marketItemInputReq.setTenantId(ItemProductInfoUtil.TENANT_ID);
        marketItemInputReq.setItemTitle(skuSyncToItemVO.getSkuName());
        marketItemInputReq.setMainPicture(skuSyncToItemVO.getSkuPic());
        marketItemInputReq.setGoodsType(GoodsTypeEnum.SELF_GOOD_TYPE.getCode());
        marketItemInputReq.setSkuId(skuId);
        marketItemInputReq.setSpecification(skuSyncToItemVO.getWeight());
        marketItemInputReq.setSpecificationUnit(skuSyncToItemVO.getUnit());
        marketItemInputReq.setWeightNotes(skuSyncToItemVO.getWeightNotes());
        marketItemInputReq.setMiniOrderQuantity(skuSyncToItemVO.getBaseSaleQuantity());
        marketItemInputReq.setPriceType(PriceTypeEnum.ALL_STORE_SHOW_DIFF.getCode());
        marketItemInputReq.setItemCode(skuSyncToItemVO.getSku());
        marketItemInputReq.setAfterSaleUnit(Objects.isNull(skuSyncToItemVO.getAfterSaleUnit())? "":skuSyncToItemVO.getAfterSaleUnit());
        marketItemInputReq.setMaxAfterSaleAmount(skuSyncToItemVO.getAfterSaleQuantity());
        if (ProductsEnum.Outdated.VALID.getCode().equals(skuSyncToItemVO.getSkuOutdated())){
            marketItemInputReq.setDeleteFlag(ProductsEnum.Outdated.IN_VALID.getCode());
        } else if (ProductsEnum.Outdated.IN_VALID.getCode().equals(skuSyncToItemVO.getSkuOutdated())) {
            marketItemInputReq.setDeleteFlag(ProductsEnum.Outdated.VALID.getCode());
        }else {
            marketItemInputReq.setDeleteFlag(skuSyncToItemVO.getSkuOutdated());
        }
        marketItemInputReq.setOnSale(OnSaleTypeEnum.SOLD_OUT.getCode());
        marketItemInputReq.setOutId(skuSyncToItemVO.getInvId());
        marketItemInputReq.setExtType(skuSyncToItemVO.getExtType());
        marketItemInputReq.setBaseSaleUnit(skuSyncToItemVO.getBaseSaleUnit());
        marketItemInputReq.setAveragePriceFlag(skuSyncToItemVO.getAveragePriceFlag());
        marketItemInputReq.setSamplePool(skuSyncToItemVO.getSamplePool());
        if (!Objects.isNull(skuSyncToItemVO.getAdminId())){
            marketItemInputReq.setCustomerId(skuSyncToItemVO.getAdminId().longValue());
        }
        marketItemInputReq.setOrigin(skuSyncToItemVO.getOrigin());
        marketItemInputReq.setItemType(ItemTypeEnum.PHYSICAL_ITEM.getCode());
        marketItemInputReq.setCreateTime(skuSyncToItemVO.getSkuAddTime());
        marketItemInputReq.setUpdateTime(skuSyncToItemVO.getSkuUpdateTime());
        marketItemInputReq.setAfterSaleRuleDetail(skuSyncToItemVO.getAfterSaleRuleDetail());
        marketItemInputReq.setVideoUrl(skuSyncToItemVO.getVideoUrl());
        marketItemInputReq.setVideoUploadTime (skuSyncToItemVO.getVideoUploadTime ());
        marketItemInputReq.setVideoUploadUser (skuSyncToItemVO.getVideoUploadUser ());
        marketItemInputReq.setQuoteType (skuSyncToItemVO.getQuoteType ());
        marketItemInputReq.setMinAutoAfterSaleThreshold (skuSyncToItemVO.getMinAutoAfterSaleThreshold ());
        return marketItemInputReq;
    }

    private Long getItemClassificationId(){
        if (Objects.isNull(CLASSIFICATION_ID)){
            CLASSIFICATION_ID = Long.valueOf(configMapper.selectOne(ITEM_CLASSIFICATION_ID).getValue());
        }
        return CLASSIFICATION_ID;
    }

    public XmSyncSkuReq buildSyncSkuReq(XmSyncSkuReq synchronizedSkuReq) {

        if (Objects.isNull(synchronizedSkuReq)){
            return null;
        }
        try {
            // 查询 一、二、三级类目
            CategoryVO categoryVO = categoryService.selectDetailById(synchronizedSkuReq.getThirdCategoryId().intValue());
            if (categoryVO != null) {
                synchronizedSkuReq.setThirdCategoryName(categoryVO.getCategory());
                if (categoryVO.getParentId() != null) {
                    CategoryVO secondCategoryVO = categoryService.selectDetailById(categoryVO.getParentId());
                    if (secondCategoryVO != null) {
                        synchronizedSkuReq.setSecondCategoryName(secondCategoryVO.getCategory());
                        synchronizedSkuReq.setSecondCategoryId(secondCategoryVO.getId().longValue());
                        if (secondCategoryVO.getParentId() != null) {
                            CategoryVO firstCategoryVO = categoryService.selectDetailById(secondCategoryVO.getParentId());
                            if (firstCategoryVO != null) {
                                synchronizedSkuReq.setFirstCategoryName(firstCategoryVO.getCategory());
                                synchronizedSkuReq.setFirstCategoryId(firstCategoryVO.getId().longValue());
                            }
                        }
                    }
                }
            }
            // 查询产地、存储温度、品牌等属性、原料/成品
            Long pdId = synchronizedSkuReq.getXmSpuId();
            List<ProductsPropertyValueInfo> propertyList = productsPropertyValueService.selectByPdIdAndProductsPropertyIds(pdId,Lists.newArrayList(Global.ORIGIN,Global.BRAND,Global.STORAGE_TEMPERATURE,Global.INGREDIENTS_FINISHED_PRODUCT));
            if (!CollectionUtils.isEmpty(propertyList)){
                Map<Integer,ProductsPropertyValueInfo> propertyMap = propertyList.stream().collect(Collectors.toMap(ProductsPropertyValueInfo::getProductsPropertyId, Function.identity(), (key1, key2) -> key1));
                ProductsPropertyValue originProperty = propertyMap.get(Global.ORIGIN.intValue());
                ProductsPropertyValue brandProperty = propertyMap.get(Global.BRAND.intValue());
                ProductsPropertyValue storageTemperatureProperty = propertyMap.get(Global.STORAGE_TEMPERATURE.intValue());
                ProductsPropertyValueInfo ingredients = propertyMap.get(Global.INGREDIENTS_FINISHED_PRODUCT.intValue());
                synchronizedSkuReq.setOrigin(Objects.isNull(originProperty) ? "" : originProperty.getProductsPropertyValue());
                synchronizedSkuReq.setBrandName(Objects.isNull(brandProperty) ? "" : brandProperty.getProductsPropertyValue());
                synchronizedSkuReq.setStorageTemperature(Objects.isNull(storageTemperatureProperty) ? "" : storageTemperatureProperty.getProductsPropertyValue());

                Integer materialType = null;
                if (Objects.nonNull(ingredients)) {
                    materialType = MaterialTypeEnum.getByDesc(ingredients.getProductsPropertyValue()) == null ? null
                            : MaterialTypeEnum.getByDesc(ingredients.getProductsPropertyValue()).getValue();
                }
                synchronizedSkuReq.setMaterialType(materialType);
            }

            //查询sku的原料/成品
            String sku = synchronizedSkuReq.getSku();
            List<ProductsPropertyValueInfo> propertyIds = productsPropertyValueService.selectByPdIdAndSkuAndProductsPropertyIds(pdId, sku, Lists.newArrayList(Global.INGREDIENTS_FINISHED_PRODUCT));
            if (!CollectionUtils.isEmpty(propertyIds)){
                Map<Integer,ProductsPropertyValueInfo> propertyMap = propertyIds.stream().collect(Collectors.toMap(ProductsPropertyValueInfo::getProductsPropertyId, Function.identity(), (key1, key2) -> key1));
                ProductsPropertyValueInfo ingredients = propertyMap.get(Global.INGREDIENTS_FINISHED_PRODUCT.intValue());
                if (Objects.nonNull(ingredients)) {
                    Integer materialType = MaterialTypeEnum.getByDesc(ingredients.getProductsPropertyValue()) == null ? null
                            : MaterialTypeEnum.getByDesc(ingredients.getProductsPropertyValue()).getValue();
                    synchronizedSkuReq.setMaterialType(materialType);
                }
            }

            // 查询所有skuId
            List<Inventory> inventories = inventoryService.selectByPdIdAndAdminId(pdId.intValue(), Objects.isNull(synchronizedSkuReq.getAdminId()) ? null : synchronizedSkuReq.getAdminId().intValue());
            List<Long> skuIdList = inventories.stream().map(Inventory::getInvId).collect(Collectors.toList());
            synchronizedSkuReq.setXmSkuIds(skuIdList);
            return synchronizedSkuReq;
        } catch (Exception e) {
            this.addFailedRecord(synchronizedSkuReq.getXmSpuId(),synchronizedSkuReq.getSku(),
                    null,
                    "获取商品信息异常:"+e.getMessage(),
                    "buildSyncSkuReq");
            throw e;
        }
    }
    public void addFailedRecord(Long pdId,String sku,Integer areaNo,String resason,String apiType) {
        ProductSyncRecord record = new ProductSyncRecord();
        record.setPdId(pdId);
        record.setSku(sku);
        record.setAreaNo(areaNo);
        record.setReason(resason.length() > 255 ? resason.substring(0,254) : resason);
        record.setRetryFlag(0);
        record.setApiType(apiType);
        productSyncRecordMapper.insertSelective(record);
    }
}
