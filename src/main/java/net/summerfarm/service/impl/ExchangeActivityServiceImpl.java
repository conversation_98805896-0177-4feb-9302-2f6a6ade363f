package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.*;
import net.summerfarm.enums.market.activity.AdjustTypeEnum;
import net.summerfarm.mapper.ExchangeBaseInfoMapper;
import net.summerfarm.mapper.ExchangeItemConfigMapper;
import net.summerfarm.mapper.ExchangeScopeConfigMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.MerchantPoolInfoMapper;
import net.summerfarm.mapper.manage.ShoppingCartMapper;
import net.summerfarm.model.DTO.ExchangeActivityDeleteDTO;
import net.summerfarm.model.DTO.market.ExchangeActivityBatchSkuDTO;
import net.summerfarm.model.DTO.market.ExchangeItemDTO;
import net.summerfarm.model.DTO.market.ExchangeItemImportDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.ExchangeActivitySkuUploadReq;
import net.summerfarm.model.vo.ExchangeBaseInfoVO;
import net.summerfarm.model.vo.ExchangeScopeConfigVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.service.ExchangeActivityService;
import net.summerfarm.service.PriceStrategyService;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 营销换购活动换购服务层
 * @date 2022/9/14 11:23
 * @Version 1.0
 */
@Service
@Slf4j
public class ExchangeActivityServiceImpl extends BaseService implements ExchangeActivityService {
    @Resource
    private PriceStrategyService priceStrategyService;
    @Resource
    private ExchangeBaseInfoMapper exchangeBaseInfoMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ExchangeItemConfigMapper exchangeItemConfigMapper;
    @Resource
    private ExchangeScopeConfigMapper exchangeScopeConfigMapper;
    @Resource
    private MerchantPoolInfoMapper merchantPoolInfoMapper;
    @Resource
    private ShoppingCartMapper shoppingCartMapper;

    @Autowired
    MqProducer mqProducer;

    @Override
    public AjaxResult queryPage(ExchangeBaseInfoVO exchangeBaseInfoVO, Integer pageSize, Integer pageIndex) {
        if (pageIndex == null || pageIndex == null){
            pageIndex = 1;
            pageSize = 10;
        }
        //查询数据
        List<ExchangeBaseInfoVO> exchangeBaseInfoVOList = exchangeBaseInfoMapper.selectPageByExchangeBaseInfoVO(exchangeBaseInfoVO);
        if (CollectionUtil.isNotEmpty(exchangeBaseInfoVOList)){
            //将范围名塞入baseInfo中
            for (ExchangeBaseInfoVO baseInfoVO : exchangeBaseInfoVOList) {
                baseInfoVO.setRealStatus(getActivityStatus(baseInfoVO));
                List<ExchangeScopeConfigVO> exchangeScopeConfigVOList = baseInfoVO.getExchangeScopeConfigVOList();
                if (CollectionUtil.isNotEmpty(exchangeScopeConfigVOList)){
                    HashSet<String> areaNames = new HashSet<>();
                    HashSet<String> merchantNames = new HashSet<>();
                    for (ExchangeScopeConfigVO exchangeScopeConfigVO : exchangeScopeConfigVOList) {
                        if (exchangeScopeConfigVO.getType() == 1){
                            areaNames.add(exchangeScopeConfigVO.getAreaName());
                        }else {
                            merchantNames.add(exchangeScopeConfigVO.getMerchantName());
                        }
                    }
                }
            }
        }
        int listSize = exchangeBaseInfoVOList.size();
        ArrayList<ExchangeBaseInfoVO> infoVOS = new ArrayList<>();
        int startPage = (pageIndex-1)*pageSize;
        int lastPage = ((pageIndex) * pageSize)>listSize?listSize:((pageIndex) * pageSize);
        for (int i = startPage; i < lastPage; i++) {
            infoVOS.add(exchangeBaseInfoVOList.get(i));
        }
        PageInfo<ExchangeBaseInfoVO> exchangeBaseInfoVOPageInfo = new PageInfo<>();
        exchangeBaseInfoVOPageInfo.setPageSize(pageSize);
        exchangeBaseInfoVOPageInfo.setPageNum(pageIndex);
        exchangeBaseInfoVOPageInfo.setTotal(listSize);
        int pages = listSize%pageSize==0?listSize/pageSize:listSize/pageSize+1;
        exchangeBaseInfoVOPageInfo.setPages(pages);
        exchangeBaseInfoVOPageInfo.setList(infoVOS);
        return AjaxResult.getOK(exchangeBaseInfoVOPageInfo);
    }

    @Override
    public AjaxResult updateExchangeBaseInfo(ExchangeBaseInfo exchangeBaseInfo) {
        ExchangeBaseInfoVO query = exchangeBaseInfoMapper.selectExchangeBaseInfoDetailById(exchangeBaseInfo.getId());
        if (query == null){
            return AjaxResult.getErrorWithMsg("活动不存在");
        }
        Integer realStatus = getActivityStatus(exchangeBaseInfo);
        if(ExchangeActivityRealStatusEnum.OVER.getId().equals(realStatus)){
            return AjaxResult.getErrorWithMsg("活动已结束，不可编辑");
        }
        if(ExchangeActivityRealStatusEnum.IN_FORCE.getId().equals(realStatus)){
            return AjaxResult.getErrorWithMsg("活动生效中，不可编辑");
        }
        //判断是否修改时间
        if (ExchangeTypeEnum.FIXED_TIME.getId().equals(exchangeBaseInfo.getEffectTimeType())){
            AjaxResult result = checkExchangeActivity(exchangeBaseInfo);
            if (!AjaxResult.isSuccess(result)){
                return result;
            }
            //修改结束时间则发送MQ，进行同步
            if (ExchangeTypeEnum.LONG_TERM_EFFECTIVE.getId().equals(query.getEffectTimeType()) || !exchangeBaseInfo.getEndTime().isEqual(query.getEndTime())){
                MQData mqData = new MQData();
                mqData.setType(MType.EXCHANGE_ACTIVITY_CONFIG_END.name());
                ExchangeBaseInfo data = new ExchangeBaseInfo();
                data.setId(exchangeBaseInfo.getId());
                data.setEndTime(exchangeBaseInfo.getEndTime());
                mqData.setData(data);
                long startDeliverTime = DateUtils.localDateTimeToDate(exchangeBaseInfo.getEndTime()).getTime();
                mqProducer.sendStartDeliver(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData),exchangeBaseInfo.getEndTime());
            }
        }
        log.info(getAdminName()+"修改了换购活动，修改值为："+ JSON.toJSONString(exchangeBaseInfo));
        int i = exchangeBaseInfoMapper.updateById(exchangeBaseInfo);
        // 插入价格策略表
        if (!ObjectUtils.isEmpty(exchangeBaseInfo.getDiscountPercentage())){
            PriceStrategy priceStrategy = initPriceStrategy(exchangeBaseInfo);
            priceStrategyService.insertOrUpdateWithoutDingByExchangeActivity(PriceStrategyTypeEnum.EXPAND_ACTIVITY, priceStrategy, exchangeBaseInfo.getId().intValue());
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult deleteExchangeBaseInfo(ExchangeBaseInfo exchangeBaseInfo) {
        if (exchangeBaseInfo.getId() == null){
            return AjaxResult.getErrorWithMsg("id未传值");
        }
        ExchangeBaseInfoVO query = exchangeBaseInfoMapper.selectExchangeBaseInfoDetailById(exchangeBaseInfo.getId());
        Integer realStatus = getActivityStatus(query);
        if (ExchangeActivityRealStatusEnum.IN_FORCE.getId().equals(realStatus)){
            return AjaxResult.getErrorWithMsg("活动进行中，不可删除");
        }
        log.info(getAdminName()+"删除了换购活动："+exchangeBaseInfo.getName());
        int i = exchangeBaseInfoMapper.deleteById(exchangeBaseInfo);
        //删除购物车商品
        MQData mqData = new MQData();
        mqData.setType(MType.DELETE_TROLLEY_EXCHANGE.name());
        ExchangeActivityDeleteDTO exchangeActivityDeleteDTO = new ExchangeActivityDeleteDTO();
        exchangeActivityDeleteDTO.setBaseInfoId(exchangeBaseInfo.getId());
        mqData.setData(exchangeActivityDeleteDTO);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryExchangeBaseInfoDetail(Long id) {
        ExchangeBaseInfoVO exchangeBaseInfoVO = exchangeBaseInfoMapper.selectExchangeBaseInfoDetailById(id);
        exchangeBaseInfoVO.setStatus(getActivityStatus(exchangeBaseInfoVO));
        return AjaxResult.getOK(exchangeBaseInfoVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertExchangeActivity(ExchangeBaseInfoVO exchangeBaseInfoVO) {
        List<ExchangeScopeConfigVO> exchangeScopeConfigVOList = exchangeBaseInfoVO.getExchangeScopeConfigVOList();
        if (CollectionUtil.isEmpty(exchangeScopeConfigVOList)){
            return AjaxResult.getErrorWithMsg("请先添加换购活动范围");
        }
        AjaxResult result = checkExchangeActivity(exchangeBaseInfoVO);
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        //新增时活动为未开启，都不做范围重复校验
        if (exchangeBaseInfoVO.getEffectTimeType().equals(ExchangeTypeEnum.LONG_TERM_EFFECTIVE.getId())){
            exchangeBaseInfoVO.setStartTime(null);
            exchangeBaseInfoVO.setEndTime(null);
        }
        //初始化数据
        exchangeBaseInfoVO.setCreator(getAdminName());
        exchangeBaseInfoVO.setCreateTime(LocalDateTime.now());
        exchangeBaseInfoVO.setIsDelete(0);
        exchangeBaseInfoVO.setStatus(0);
        exchangeBaseInfoMapper.insertExchangeBaseInfo(exchangeBaseInfoVO);
        for (ExchangeScopeConfigVO exchangeScopeConfigVO : exchangeScopeConfigVOList) {
            exchangeScopeConfigVO.setBaseInfoId(exchangeBaseInfoVO.getId());
            exchangeScopeConfigVO.setCreateTime(LocalDateTime.now());
            exchangeScopeConfigVO.setCreator(getAdminName());
            exchangeScopeConfigMapper.insertExchangeScopeConfig(exchangeScopeConfigVO);
            List<ExchangeItemConfig> exchangeItemConfigList = exchangeScopeConfigVO.getExchangeItemConfigList();
            if (CollectionUtil.isNotEmpty(exchangeItemConfigList)){
                if (exchangeItemConfigList.size()>30){
                    return AjaxResult.getErrorWithMsg("商品数量不可超过30个");
                }
                for (ExchangeItemConfig exchangeItemConfig : exchangeItemConfigList) {
                    AjaxResult result1 = checkInventory(exchangeItemConfig.getSku());
                    if (!AjaxResult.isSuccess(result1)){
                        return result1;
                    }
                    exchangeItemConfig.setScopeConfigId(exchangeScopeConfigVO.getId());
                    if (exchangeItemConfig.getAdjustType().equals(3)){
                        exchangeItemConfig.setAmount(exchangeItemConfig.getAmount().divide(new BigDecimal(100)));
                    }
                    exchangeItemConfigMapper.insertExchangeItem(exchangeItemConfig);
                }
            }
        }
        //更新策略表
        if (!ObjectUtils.isEmpty(exchangeBaseInfoVO.getDiscountPercentage())){
            PriceStrategy priceStrategy = initPriceStrategy(exchangeBaseInfoVO);
            priceStrategyService.insertOrUpdateWithoutDingByExchangeActivity(PriceStrategyTypeEnum.EXPAND_ACTIVITY, priceStrategy, exchangeBaseInfoVO.getId().intValue());
        }
        if (exchangeBaseInfoVO.getEffectTimeType().equals(ExchangeTypeEnum.FIXED_TIME.getId())){
            //发送活动截止延迟消息
            MQData mqData = new MQData();
            mqData.setType(MType.EXCHANGE_ACTIVITY_CONFIG_END.name());
            ExchangeBaseInfo exchangeBaseInfo = new ExchangeBaseInfo();
            exchangeBaseInfo.setId(exchangeBaseInfoVO.getId());
            exchangeBaseInfo.setEndTime(exchangeBaseInfoVO.getEndTime());
            mqData.setData(exchangeBaseInfo);
            long startDeliverTime = DateUtils.localDateTimeToDate(exchangeBaseInfoVO.getEndTime()).getTime();
//            producer.sendDelayDataToQueue(RocketMqMessageConstant.MALL_LIST,
//                    JSON.toJSONString(mqData), MQDelayConstant.FOURTEEN_DELAY_LEVEL, startDeliverTime);
            mqProducer.sendStartDeliver(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData),exchangeBaseInfo.getEndTime());
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult updateExchangeScopeConfig(ExchangeScopeConfig exchangeScopeConfig) {
        List<ExchangeBaseInfo> exchangeBaseInfos = exchangeBaseInfoMapper.selectExchangeScopeConfigCount(exchangeScopeConfig.getScopeId(), exchangeScopeConfig.getType());
        if (CollectionUtil.isNotEmpty(exchangeBaseInfos)){
            for (ExchangeBaseInfo exchangeBaseInfo : exchangeBaseInfos) {
                Integer realStatus = getActivityStatus(exchangeBaseInfo);
                if(ExchangeActivityRealStatusEnum.IN_FORCE.getId().equals(realStatus)){
                    return AjaxResult.getErrorWithMsg("人群包id为"+exchangeScopeConfig.getScopeId()+"的换购活动范围已存在");
                }
            }
        }
        log.info(getAdminName()+"修改了换购活动范围，修改值为："+ JSON.toJSONString(exchangeScopeConfig));
        exchangeScopeConfigMapper.updateExchangeScopeConfig(exchangeScopeConfig);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult insertExchangeScopeConfig(ExchangeScopeConfig exchangeScopeConfig) {
        ExchangeBaseInfoVO query = exchangeBaseInfoMapper.selectExchangeBaseInfoDetailById(exchangeScopeConfig.getBaseInfoId());
        List<ExchangeBaseInfo> exchangeBaseInfos = exchangeBaseInfoMapper.selectExchangeScopeConfigCount(exchangeScopeConfig.getScopeId(), exchangeScopeConfig.getType());
        if (CollectionUtil.isNotEmpty(exchangeBaseInfos)){
            for (ExchangeBaseInfo exchangeBaseInfo : exchangeBaseInfos) {
                if (ExchangeActivityStatusEnum.ENABLE.getId().equals(query.getStatus())){
                    //长期有效，则不可在其之后有
                    if (ExchangeTypeEnum.LONG_TERM_EFFECTIVE.getId().equals(exchangeBaseInfo.getEffectTimeType())){
                        return AjaxResult.getErrorWithMsg("人群包id为"+exchangeScopeConfig.getScopeId()+"的换购活动范围已存在至有效活动："+exchangeBaseInfo.getName()+"中");
                    }else {
                        if (exchangeBaseInfo.getStartTime().isBefore(query.getStartTime()) && exchangeBaseInfo.getEndTime().isAfter(query.getStartTime())
                        || exchangeBaseInfo.getStartTime().isBefore(query.getEndTime()) && exchangeBaseInfo.getEndTime().isAfter(query.getEndTime())){
                            return AjaxResult.getErrorWithMsg("人群包id为"+exchangeScopeConfig.getScopeId()+"的换购活动范围已存在至有效活动："+exchangeBaseInfo.getName()+"中");
                        }
                    }
                }
            }
        }
        ExchangeScopeConfigVO scopeConfig = exchangeScopeConfigMapper.selectByBaseInfoIdAndScopeId(exchangeScopeConfig.getBaseInfoId(),exchangeScopeConfig.getScopeId());
        if (scopeConfig!=null){
            return AjaxResult.getErrorWithMsg("活动下已有此人群包，不可重复添加");
        }
        exchangeScopeConfig.setCreator(getAdminName());
        exchangeScopeConfigMapper.insertExchangeScopeConfig(exchangeScopeConfig);
        return AjaxResult.getOK(exchangeScopeConfig.getId());
    }

    @Override
    public AjaxResult deleteExchangeScopeConfig(Long id) {
        log.info(getAdminName()+"删除了换购换购活动范围，换购活动范围ID为："+id);
        if (id == null){
            return AjaxResult.getErrorWithMsg("id未传值");
        }
        exchangeScopeConfigMapper.deleteById(id);
        //同步删除购物车中数据
        MQData mqData = new MQData();
        mqData.setType(MType.DELETE_TROLLEY_EXCHANGE.name());
        ExchangeActivityDeleteDTO exchangeActivityDeleteDTO = new ExchangeActivityDeleteDTO();
        exchangeActivityDeleteDTO.setScopeId(id);
        mqData.setData(exchangeActivityDeleteDTO);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryExchangeScopeItem(Long id) {
        List<ExchangeItemConfig> exchangeItemConfigList = exchangeItemConfigMapper.selectByScopeId(id);
        if (CollectionUtil.isNotEmpty(exchangeItemConfigList)){
            for (ExchangeItemConfig exchangeItemConfig : exchangeItemConfigList) {
                if (exchangeItemConfig.getAdjustType() == 3){
                    exchangeItemConfig.setAmount(exchangeItemConfig.getAmount().multiply(new BigDecimal(100)));
                }
            }
        }
        return AjaxResult.getOK(exchangeItemConfigList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertExchangeScopeItem(List<ExchangeItemConfig> exchangeItemConfig) {
        if (!CollectionUtil.isEmpty(exchangeItemConfig)){
            if (exchangeItemConfig.size()>30){
                return AjaxResult.getErrorWithMsg("单个活动范围下商品不可大于30");
            }
            int i = exchangeItemConfigMapper.selectCountItemByScopeId(exchangeItemConfig.get(0).getScopeConfigId());
            for (ExchangeItemConfig itemConfig : exchangeItemConfig) {
                ExchangeScopeConfig scopeConfig = exchangeScopeConfigMapper.selectById(itemConfig.getScopeConfigId());
                if (scopeConfig == null){
                    return AjaxResult.getErrorWithMsg("活动范围不存在");
                }
                AjaxResult result = checkInventory(itemConfig.getSku());
                if (!AjaxResult.isSuccess(result)){
                    return result;
                }
                if (itemConfig.getAdjustType().equals(3)){
                    itemConfig.setAmount(itemConfig.getAmount().divide(new BigDecimal(100),4,BigDecimal.ROUND_UP));
                }
                if (itemConfig.getId() == null){
                    if (i > 30){
                        return AjaxResult.getErrorWithMsg("单个活动范围下商品不可大于30");
                    }
                    itemConfig.setCreator(getAdminName());
                    exchangeItemConfigMapper.insertExchangeItem(itemConfig);
                    i++;
                }else {
                    exchangeItemConfigMapper.updateExchangeItemById(itemConfig);
                }
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult updateExchangeScopeItem(ExchangeItemConfig exchangeItemConfig) {
        log.info(getAdminName()+"修改了换购活动范围下的商品，修改值为："+ JSON.toJSONString(exchangeItemConfig));
        if (exchangeItemConfig.getAdjustType() != null && exchangeItemConfig.getAdjustType().equals(3)){
            exchangeItemConfig.setAmount(exchangeItemConfig.getAmount().divide(new BigDecimal(100),4,BigDecimal.ROUND_UP));
        }
        exchangeItemConfigMapper.updateExchangeItemById(exchangeItemConfig);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteExchangeScopeItem(List<ExchangeItemConfig> exchangeItemConfig) {
        if (!CollectionUtil.isEmpty(exchangeItemConfig)){
            for (ExchangeItemConfig itemConfig : exchangeItemConfig) {
                ExchangeItemConfig query = exchangeItemConfigMapper.selectById(itemConfig.getId());
                if (query == null) {
                    log.warn("当前活动商品不存在，itemConfig:{}", JSON.toJSONString(itemConfig));
                    continue;
                }
                log.info(getAdminName()+"删除了换购活动范围下的商品，id："+itemConfig.getId()+",sku："+query.getSku());
                exchangeItemConfigMapper.deleteExchangeItemById(itemConfig.getId());
                //删除购物车中数据
                MQData mqData = new MQData();
                mqData.setType(MType.DELETE_TROLLEY_EXCHANGE.name());
                ExchangeActivityDeleteDTO exchangeActivityDeleteDTO = new ExchangeActivityDeleteDTO();
                exchangeActivityDeleteDTO.setExchangeItemConfig(query);
                mqData.setData(exchangeActivityDeleteDTO);
                mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectMerchantPoolInfo(String merchantName) {
        List<MerchantPoolInfo> merchantPoolInfoList = merchantPoolInfoMapper.selectByName(merchantName);
        return AjaxResult.getOK(merchantPoolInfoList);
    }


    @Override
    public AjaxResult openActivity(ExchangeBaseInfoVO exchangeBaseInfoVO) {
        ExchangeBaseInfoVO query = exchangeBaseInfoMapper.selectExchangeBaseInfoDetailById(exchangeBaseInfoVO.getId());
        if (query.getEffectTimeType().equals(ExchangeTypeEnum.FIXED_TIME.getId())){
            if (LocalDateTime.now().isAfter(query.getEndTime())){
                return AjaxResult.getErrorWithMsg("已超过结束时间不可再次开启");
            }
        }
        Integer queryStatus = getActivityStatus(query);
        if (ExchangeActivityRealStatusEnum.OVER.getId().equals(queryStatus)){
            return AjaxResult.getErrorWithMsg("该活动已结束，不可开启");
        }
        List<ExchangeScopeConfigVO> exchangeScopeConfigVOList = query.getExchangeScopeConfigVOList();
        if (!CollectionUtil.isEmpty(exchangeScopeConfigVOList)){
            for (ExchangeScopeConfigVO exchangeScopeConfigVO : exchangeScopeConfigVOList) {
                List<ExchangeBaseInfo> exchangeBaseInfos = exchangeBaseInfoMapper.selectExchangeScopeConfigCount(exchangeScopeConfigVO.getScopeId(), exchangeScopeConfigVO.getType());
                if (CollectionUtil.isNotEmpty(exchangeBaseInfos)){
                    for (ExchangeBaseInfo exchangeBaseInfo : exchangeBaseInfos) {
                        if (exchangeBaseInfo.getId().equals(exchangeBaseInfoVO.getId())){
                            continue;
                        }
                        //长期有效，则不可在其之后有
                        if (ExchangeTypeEnum.LONG_TERM_EFFECTIVE.getId().equals(query.getEffectTimeType())){
                            if (ExchangeActivityStatusEnum.ENABLE.getId().equals(exchangeBaseInfo.getStatus())){
                                return AjaxResult.getErrorWithMsg("人群包id为"+exchangeScopeConfigVO.getScopeId()+"的换购活动范围已存在至有效活动："+exchangeBaseInfo.getName()+"中");
                            }
                        }else {
                            if (exchangeBaseInfo.getStartTime().isBefore(query.getStartTime()) && exchangeBaseInfo.getEndTime().isAfter(query.getStartTime())
                            || exchangeBaseInfo.getStartTime().isBefore(query.getEndTime()) && exchangeBaseInfo.getEndTime().isAfter(query.getEndTime())){
                                return AjaxResult.getErrorWithMsg("人群包id为"+exchangeScopeConfigVO.getScopeId()+"的换购活动范围已存在至有效活动："+exchangeBaseInfo.getName()+"中");
                            }
                        }
                    }
                }
            }
        }
        ExchangeBaseInfoVO update = new ExchangeBaseInfoVO();
        update.setId(exchangeBaseInfoVO.getId());
        update.setStatus(ExchangeActivityStatusEnum.ENABLE.getId());
        exchangeBaseInfoMapper.updateById(update);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult closeActivity(ExchangeBaseInfoVO exchangeBaseInfoVO) {
        ExchangeBaseInfoVO query = exchangeBaseInfoMapper.selectExchangeBaseInfoDetailById(exchangeBaseInfoVO.getId());
        Integer realStatus = getActivityStatus(query);
        if (ExchangeActivityRealStatusEnum.OVER.getId().equals(realStatus)){
            return AjaxResult.getErrorWithMsg("该活动已处于结束状态");
        }
        ExchangeBaseInfoVO update = new ExchangeBaseInfoVO();
        update.setId(exchangeBaseInfoVO.getId());
        update.setStatus(ExchangeActivityStatusEnum.DISABLE.getId());
        exchangeBaseInfoMapper.updateById(update);
        //删除购物车商品
        MQData mqData = new MQData();
        mqData.setType(MType.DELETE_TROLLEY_EXCHANGE.name());
        ExchangeActivityDeleteDTO exchangeActivityDeleteDTO = new ExchangeActivityDeleteDTO();
        exchangeActivityDeleteDTO.setBaseInfoId(exchangeBaseInfoVO.getId());
        mqData.setData(exchangeActivityDeleteDTO);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult openExchangeScope(ExchangeScopeConfig exchangeScopeConfig) {
        Long id = exchangeScopeConfig.getId();
        ExchangeScopeConfig query = exchangeScopeConfigMapper.selectById(id);
        //范围已启动，则不验证
        if (query.getStatus().equals(ExchangeActivityStatusEnum.ENABLE.getId())){
            ExchangeBaseInfoVO exchangeBaseInfoVO = exchangeBaseInfoMapper.selectExchangeBaseInfoDetailById(query.getBaseInfoId());
            //范围在的活动未开启，则不需要验证
            if (ExchangeActivityStatusEnum.ENABLE.getId().equals(exchangeBaseInfoVO.getStatus())){
                List<ExchangeBaseInfo> exchangeBaseInfos = exchangeBaseInfoMapper.selectExchangeScopeConfigCount(query.getScopeId(), query.getType());
                if (CollectionUtil.isNotEmpty(exchangeBaseInfos)){
                    for (ExchangeBaseInfo exchangeBaseInfo : exchangeBaseInfos) {
                        if (!exchangeBaseInfo.getId().equals(exchangeBaseInfoVO.getId())){
                            if (ExchangeActivityStatusEnum.ENABLE.getId().equals(exchangeBaseInfo.getStatus())){
                                if (ExchangeTypeEnum.LONG_TERM_EFFECTIVE.getId().equals(exchangeBaseInfo.getEffectTimeType())){
                                    return AjaxResult.getErrorWithMsg("人群包id为"+query.getScopeId()+"的换购活动范围已存在");
                                }else {
                                    if (exchangeBaseInfoVO.getStartTime().isAfter(exchangeBaseInfo.getStartTime()) && exchangeBaseInfoVO.getStartTime().isBefore(exchangeBaseInfo.getEndTime())
                                    || exchangeBaseInfoVO.getEndTime().isAfter(exchangeBaseInfo.getStartTime()) && exchangeBaseInfoVO.getEndTime().isBefore(exchangeBaseInfo.getEndTime())){
                                        return AjaxResult.getErrorWithMsg("人群包id为"+query.getScopeId()+"的换购活动范围已存在");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        ExchangeScopeConfig update = new ExchangeScopeConfig();
        update.setStatus(ExchangeActivityRealStatusEnum.IN_FORCE.getId());
        update.setId(exchangeScopeConfig.getId());
        exchangeScopeConfigMapper.updateExchangeScopeConfig(update);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult closeExchangeScope(ExchangeScopeConfig exchangeScopeConfig) {
        Long id = exchangeScopeConfig.getId();
        ExchangeScopeConfig query = exchangeScopeConfigMapper.selectById(id);
        if (!query.getStatus().equals(ExchangeActivityRealStatusEnum.IN_FORCE.getId())){
            return AjaxResult.getErrorWithMsg("该范围已停用");
        }

        ExchangeScopeConfig update = new ExchangeScopeConfig();
        update.setStatus(ExchangeActivityRealStatusEnum.NOT_ACTIVE.getId());
        update.setId(exchangeScopeConfig.getId());
        exchangeScopeConfigMapper.updateExchangeScopeConfig(update);

        //删除购物车商品
        MQData mqData = new MQData();
        mqData.setType(MType.DELETE_TROLLEY_EXCHANGE.name());
        ExchangeActivityDeleteDTO exchangeActivityDeleteDTO = new ExchangeActivityDeleteDTO();
        exchangeActivityDeleteDTO.setScopeId(exchangeScopeConfig.getId());
        mqData.setData(exchangeActivityDeleteDTO);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
        return AjaxResult.getOK();
    }

    @Override
    public void endTimeUpdateActivity(ExchangeBaseInfoVO exchangeBaseInfoVO) {
        ExchangeBaseInfo exchangeBaseInfo = new ExchangeBaseInfo();
        exchangeBaseInfo.setId(exchangeBaseInfoVO.getId());
        exchangeBaseInfo.setStatus(ExchangeActivityRealStatusEnum.OVER.getId());
        exchangeBaseInfoMapper.updateById(exchangeBaseInfo);
    }

    private AjaxResult checkExchangeActivity(ExchangeBaseInfo exchangeBaseInfo){
        if (exchangeBaseInfo.getEffectTimeType() != null && ExchangeTypeEnum.FIXED_TIME.getId().equals(exchangeBaseInfo.getEffectTimeType())){
            LocalDateTime startTime = exchangeBaseInfo.getStartTime();
            LocalDateTime endTime = exchangeBaseInfo.getEndTime();
            if (startTime == null){
                return AjaxResult.getErrorWithMsg("请填写活动开始时间");
            }
            if (endTime == null){
                return AjaxResult.getErrorWithMsg("请填写活动结束时间");
            }
            if (LocalDateTime.now().isAfter(startTime)){
                return AjaxResult.getErrorWithMsg("活动开始时间不可在当前时间之前");
            }
            if (startTime.isEqual(endTime)){
                return AjaxResult.getErrorWithMsg("开始时间和结束时间不能相同");
            }
            if (endTime.isBefore(startTime)){
                return AjaxResult.getErrorWithMsg("活动结束时间不可在开始时间之前");
            }
        }
        return AjaxResult.getOK();
    }

    private AjaxResult checkInventory(String sku){
        Inventory inventory = inventoryMapper.queryBySku(sku);
        if (inventory == null){
            return AjaxResult.getErrorWithMsg("该SKU不存在:"+sku);
        }
        if (inventory.getType().equals(InventoryTypeEnum.ACTIVITY.ordinal())){
            return AjaxResult.getErrorWithMsg("代仓商品不可添加:"+sku);
        }
        return AjaxResult.getOK();
    }

    /**
     * 删除购物车中的换购SKU
     * @return
     */
    @Override
    public AjaxResult deleteTrolleyExchangeSku(ExchangeActivityDeleteDTO exchangeActivityDeleteDTO){
        //整改活动失效/删除
        if (exchangeActivityDeleteDTO != null) {
            log.info("开始删除购物车中换购商品数据：baseInfoId：{}，scopeId：{}，itemId：{}",exchangeActivityDeleteDTO.getBaseInfoId(),exchangeActivityDeleteDTO.getScopeId(),exchangeActivityDeleteDTO.getItemId());
            if (exchangeActivityDeleteDTO.getBaseInfoId() != null) {
                List<ExchangeScopeConfigVO> exchangeScopeConfigVOList = exchangeScopeConfigMapper.selectByBaseInfoId(exchangeActivityDeleteDTO.getBaseInfoId());
                    if (!CollectionUtil.isEmpty(exchangeScopeConfigVOList)) {
                        for (ExchangeScopeConfigVO exchangeScopeConfigVO : exchangeScopeConfigVOList) {
                            int i = 0;
                            do {
                                try {
                                    i = shoppingCartMapper.deleteByBizId(exchangeScopeConfigVO.getId());
                                } catch (Exception e) {
                                    log.info("删除购物车商品失败，baseInfoId：{}", exchangeActivityDeleteDTO.getBaseInfoId());
                                }
                            } while (i > 0);
                        }
                    }
                //范围失效/删除
            } else if (exchangeActivityDeleteDTO.getScopeId() != null) {
                int i = 0;
                do {
                    try {
                        i = shoppingCartMapper.deleteByBizId(exchangeActivityDeleteDTO.getScopeId());
                    } catch (Exception e) {
                        log.info("删除购物车商品失败，scopeId：{}", exchangeActivityDeleteDTO.getScopeId());
                    }
                } while (i > 0);
                //商品失效/删除
            } else if (exchangeActivityDeleteDTO.getExchangeItemConfig() != null) {
                ExchangeItemConfig exchangeItemConfig = exchangeActivityDeleteDTO.getExchangeItemConfig();
                if (exchangeItemConfig == null) {
                    return AjaxResult.getErrorWithMsg("暂无换购活动商品信息");
                }
                int i = 0;
                do {
                    try {
                        i = shoppingCartMapper.deleteByBizIdAndSku(exchangeItemConfig.getScopeConfigId(), exchangeItemConfig.getSku());
                    } catch (Exception e) {
                        log.info("删除购物车商品失败：scope：{}，sku：{}", exchangeItemConfig.getScopeConfigId(), exchangeItemConfig.getSku());
                    }
                } while (i > 0);
            } else {
                log.error("删除换购活动规则ID数据错误");
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryCreator(String creator) {
        List<String> creatorList=exchangeBaseInfoMapper.selectCreator(creator);
        return AjaxResult.getOK(creatorList);
    }

    @Override
    public ExchangeActivityBatchSkuDTO uploadSkuList(ExchangeActivitySkuUploadReq uploadReq) {
        //解析文件数据
        InputStream inputStream = OssGetUtil.getInputStream(uploadReq.getKey());
        List<ExchangeItemImportDTO> exchangeItemImportDTOS;
        try {
            exchangeItemImportDTOS = EasyExcel.read(inputStream,
                    ExchangeItemImportDTO.class, null).doReadAllSync();
        } catch (Exception e) {
            throw new BizException("上传换购商品模版解析异常！");
        }

        if (CollectionUtil.isEmpty(exchangeItemImportDTOS)) {
            throw new BizException("上传换购商品模版数据为空！");
        }
        Set<String> skuCollect = exchangeItemImportDTOS.stream().map(ExchangeItemImportDTO::getSku).collect(Collectors.toSet());
        List<InventoryVO> inventoryVOS = inventoryMapper.batchSelectSkuInfo(skuCollect);
        if (CollectionUtils.isEmpty(inventoryVOS)) {
            throw new BizException("上传换购商品有效数据为空！");
        }

        Map<String, InventoryVO> inventoryVOMap = inventoryVOS.stream().collect(Collectors.toMap(InventoryVO::getSku, Function.identity()));
        ExchangeActivityBatchSkuDTO exchangeActivityBatchSkuDTO = new ExchangeActivityBatchSkuDTO();
        List<ExchangeItemDTO> exchangeItemDTOS = new ArrayList<>();
        List<String> failedSkus = new ArrayList<>();
        Set<String> skus = new HashSet<>();
        exchangeItemImportDTOS.forEach(item -> {
            if (skus.contains(item.getSku())) {
                return;
            }
            if (inventoryVOMap.containsKey(item.getSku())) {
                InventoryVO inventoryVO = inventoryVOMap.get(item.getSku());
                ExchangeItemDTO exchangeItemDTO = new ExchangeItemDTO();
                exchangeItemDTO.setSku(item.getSku());
                exchangeItemDTO.setPriority(item.getPriority());
                exchangeItemDTO.setProductName(inventoryVO.getPdName());
                if (item.getAmount() != null) {
                    exchangeItemDTO.setAdjustType(AdjustTypeEnum.FIXED_PRICE.getCode());
                    exchangeItemDTO.setAmount(item.getAmount());
                }
                exchangeItemDTOS.add(exchangeItemDTO);
            } else {
                failedSkus.add(item.getSku());
            }
            skus.add(item.getSku());
        });
        exchangeActivityBatchSkuDTO.setExchangeItemDTOS(exchangeItemDTOS);
        exchangeActivityBatchSkuDTO.setFailedSkus(failedSkus);
        return exchangeActivityBatchSkuDTO;
    }

    /**
     * 封装价格调整数据
     * @param exchangeBaseInfo 活动sku对象
     * @return
     */
    private PriceStrategy initPriceStrategy(ExchangeBaseInfo exchangeBaseInfo) {
        PriceStrategy priceStrategy = new PriceStrategy();
        priceStrategy.setBusinessId(exchangeBaseInfo.getId());
        priceStrategy.setAdjustType(3);
        priceStrategy.setAmount(exchangeBaseInfo.getDiscountPercentage().divide(new BigDecimal(100),4,0));
        priceStrategy.setRoundingMode(0);
        //需用枚举类替换
        priceStrategy.setType(7);
        priceStrategy.setCreator(super.getAdminId());
        priceStrategy.setUpdater(super.getAdminId());
        return priceStrategy;
    }

    private Integer getActivityStatus(ExchangeBaseInfo exchangeBaseInfoVO) {
        Integer status = null;
        if (ExchangeTypeEnum.FIXED_TIME.getId().equals(exchangeBaseInfoVO.getEffectTimeType())){
            //固定时间间隔到期
            LocalDateTime startTime = exchangeBaseInfoVO.getStartTime();
            LocalDateTime entTime = exchangeBaseInfoVO.getEndTime();
            if (ExchangeActivityStatusEnum.ENABLE.getId().equals(exchangeBaseInfoVO.getStatus())){
                //启用
                if(startTime.isAfter(LocalDateTime.now())){
                    status = ExchangeActivityRealStatusEnum.NOT_ACTIVE.getId();
                }
                if(entTime.isBefore(LocalDateTime.now())){
                    status = ExchangeActivityRealStatusEnum.OVER.getId();
                }
                if(startTime.isBefore(LocalDateTime.now()) && entTime.isAfter(LocalDateTime.now())){
                    status = ExchangeActivityRealStatusEnum.IN_FORCE.getId();
                }
            }else {
                //停用
                if(startTime.isAfter(LocalDateTime.now())){
                    status = ExchangeActivityRealStatusEnum.NOT_ACTIVE.getId();
                }
                if(entTime.isBefore(LocalDateTime.now())){
                    status = ExchangeActivityRealStatusEnum.OVER.getId();
                }
                if(startTime.isBefore(LocalDateTime.now()) && entTime.isAfter(LocalDateTime.now())){
                    status = ExchangeActivityRealStatusEnum.NOT_ACTIVE.getId();
                }
            }
        }else {
            //长期有效
            if (ExchangeActivityStatusEnum.ENABLE.getId().equals(exchangeBaseInfoVO.getStatus())){
                //启用
                status = ExchangeActivityRealStatusEnum.IN_FORCE.getId();
            }
            if (ExchangeActivityStatusEnum.DISABLE.getId().equals(exchangeBaseInfoVO.getStatus())){
                //停用
                status = ExchangeActivityRealStatusEnum.NOT_ACTIVE.getId();
            }
        }
        return  status;
    }
}
