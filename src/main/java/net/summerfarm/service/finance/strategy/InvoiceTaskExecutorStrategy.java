package net.summerfarm.service.finance.strategy;

import net.summerfarm.biz.finance.enums.FinancialInvoiceAsyncTaskEnum;
import net.summerfarm.service.finance.executor.InvoiceTaskExecutor;
import net.summerfarm.service.finance.executor.impl.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.security.ProviderException;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: George
 * @date: 2024-05-15
 **/
@Component
public class InvoiceTaskExecutorStrategy {

    @Resource
    private BlueInvoiceTaskExecutor blueInvoiceTaskExecutor;
    @Resource
    private RedInvoiceConfirmTaskExecutor redInvoiceConfirmTaskExecutor;
    @Resource
    private DownloadInvoiceTaskExecutor downloadInvoiceTaskExecutor;
    @Resource
    private RedInvoiceConfirmPreparationTaskExecutor redInvoiceConfirmPreparationTaskExecutor;
    @Resource
    private RedInvoiceQueryTaskExecutor redInvoiceQueryTaskExecutor;

    // 定义任务类型对应的执行器映射关系
    private static Map<Integer, InvoiceTaskExecutor> executorMap = new HashMap<>(2);

    @PostConstruct
    public void init() {
        executorMap.put(FinancialInvoiceAsyncTaskEnum.TaskType.BLUE.getType(), blueInvoiceTaskExecutor);
        executorMap.put(FinancialInvoiceAsyncTaskEnum.TaskType.RED.getType(), redInvoiceConfirmTaskExecutor);
        // 作废走红冲
        executorMap.put(FinancialInvoiceAsyncTaskEnum.TaskType.INVALID.getType(), redInvoiceConfirmTaskExecutor);
        executorMap.put(FinancialInvoiceAsyncTaskEnum.TaskType.DOWNLOAD.getType(), downloadInvoiceTaskExecutor);
        executorMap.put(FinancialInvoiceAsyncTaskEnum.TaskType.RED_INVOICE_PREPARATION.getType(), redInvoiceConfirmPreparationTaskExecutor);
        executorMap.put(FinancialInvoiceAsyncTaskEnum.TaskType.INVALID_INVOICE_PREPARATION.getType(), redInvoiceConfirmPreparationTaskExecutor);
        executorMap.put(FinancialInvoiceAsyncTaskEnum.TaskType.RED_INVOICE_QUERY.getType(), redInvoiceQueryTaskExecutor);
    }

    public static InvoiceTaskExecutor getExecutor(int type) {
        InvoiceTaskExecutor executor = executorMap.get(type);
        if (executor == null) {
            throw new ProviderException("发票异步任务未找到对应的执行器");
        }
        return executor;
    }
}
