package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.PriceAdjustment;
import net.summerfarm.model.domain.ProductPriceAdjustment;
import net.summerfarm.model.vo.PriceAdjustRuleVo;
import net.summerfarm.model.vo.PriceAdjustVO;
import net.summerfarm.model.vo.PriceAdjustmentPoolVO;
import net.summerfarm.model.vo.StoreRecordVO;
import net.summerfarm.model.vo.price.PriceAdjustDetailVO;
import net.xianmu.common.result.CommonResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Package: net.summerfarm.service
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/11/8
 */
public interface PriceAdjustService {

    AjaxResult selectSku(int pageIndex, int pageSize, PriceAdjustVO selectKeys);

    AjaxResult selectSimple(String sku, Integer status, Integer areaNo, Integer poolId);

    AjaxResult<PriceAdjustDetailVO> selectDetail(String sku, Integer status, Integer areaNo, Integer poolId);

    AjaxResult operationAdjustSave(PriceAdjustment priceAdjustment);

    AjaxResult purchaseAdjust(PriceAdjustment priceAdjustment);

    AjaxResult select(int pageIndex, int pageSize, PriceAdjustVO selectKeys);

    /**
     * 调价单审核
     * @param priceAdjustment 商品价格调整数据
     * @return 商品调价审核结果
     */
    AjaxResult auditAdjust(PriceAdjustment priceAdjustment);


    AjaxResult cancelAdjust(int id);

    AjaxResult selectLeftInStock(Integer storeNo, String sku);

//    /**
//     * 定时任务初始化
//     */
//    void init();

    /**
     * 执行定时任务
     * @param id 调价id
     *
     */
    void executePriceAdjust(Integer id);

    /**
     * 超时未审批处理
     */
    void handleOvertime();

    void sendDingDingMsg(BigDecimal costPrice, BigDecimal price, Integer areaNo, String sku, Integer warehouseNo,
                         String batchNo);

    AjaxResult selectRule();

    AjaxResult saveRule(PriceAdjustRuleVo priceAdjustRuleVo);

    AjaxResult initialization();

    /**
     * 定时任务
     * @return
     */

    AjaxResult ruleTimedTask();

    /**
     * 处理周期成本
     * @param storeRecordVOS
     */
    void handleCycleInventoryCost(List<StoreRecordVO> storeRecordVOS);

    /**
     * 处理价格调整
     */
    List<PriceAdjustment> handlePriceAdjustmentPool(List<Integer> areaNos, List<PriceAdjustmentPoolVO> priceAdjustmentPoolVOS);
    /**
     * 查询某个sku在所有区域中的最高售价
     * @param sku 商品编号
     * @return 商品最高售价
     */
    BigDecimal findSkuMaxPriceInAllArea(String sku);


    /**
     * 保存调价单数据
     * @param productPriceAdjustmentList 调价单集合数据
     * @return
     */
    AjaxResult saveAdjustSheet(List<ProductPriceAdjustment> productPriceAdjustmentList);

    /**
     * 通过成本价调整营销业务价格
     * @param sku sku
     * @param areaNo 城市编号
     * @param updater 操作人记录
     */
    void changeMarketPrice(String sku, Integer areaNo, String updater);

    /**
     * 常规价格变动，临保/破袋品自动根据常规品价格变动
     *
     * @param pdId
     * @param sku    常规商品编号
     * @param areaNo 运营区域
     * @param price  商品价格
     */
    void bindSkuAutoUpdatePrice(long pdId, String sku, int areaNo, BigDecimal price);
    void autoUpdatePrice(String sku, int areaNo, BigDecimal price,BigDecimal interestRate);
}
