package net.summerfarm.service.helper.market;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.ErrorCode;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.ConfigValueEnum;
import net.summerfarm.enums.InventoryExtTypeEnum;
import net.summerfarm.enums.market.activity.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.AreaInfoDTO;
import net.summerfarm.model.DTO.market.*;
import net.summerfarm.model.DTO.purchase.SkuBaseInfoDTO;
import net.summerfarm.model.converter.ActivityNewConverter;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.market.*;
import net.summerfarm.model.vo.CostChangeVo;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.PriceStrategyAuditRecordVO;
import net.summerfarm.service.CycleInventoryCostService;
import net.summerfarm.service.PriceStrategyService;
import net.summerfarm.service.helper.excel.ActivitySkuDataListener;
import net.summerfarm.warehouse.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2022/12/2
 */
@Slf4j
@Component
public class ActivityNewServiceHelper {

    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;

    @Resource
    private ActivityItemConfigMapper activityItemConfigMapper;

    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;

    @Resource
    private ActivityScopeConfigMapper activityScopeConfigMapper;

    @Resource
    private ActivitySkuPriceMapper activitySkuPriceMapper;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private LargeAreaMapper largeAreaMapper;

    @Resource
    private MerchantPoolInfoMapper merchantPoolInfoMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private PriceStrategyService priceStrategyService;

    @Resource
    private CycleInventoryCostService cycleInventoryCostService;

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Resource
    private AreaStoreMapper areaStoreMapper;

    @Resource
    private WarehouseInventoryMappingMapper warehouseInventoryMappingMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private FenceMapper fenceMapper;


    /**
     * 活动新增、修改前校验
     *
     * @param activityNewDTO
     */
    public void check(ActivityNewDTO activityNewDTO) {
        ActivityBasicInfoDTO basicInfoDTO = activityNewDTO.getBasicInfoDTO();
//        ActivityItemConfigDTO itemConfigDTO = activityNewDTO.getItemConfigDTO();
        ActivityScopeConfigDTO scopeConfigDTO = activityNewDTO.getScopeConfigDTO();

        //临保活动不支持创建
        /*if (Objects.equals(basicInfoDTO.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改");
        }*/

        if (basicInfoDTO.getIsPermanent() == 0) {
            if ((basicInfoDTO.getStartTime() == null
                    || basicInfoDTO.getEndTime() == null)) {
                throw new BizException("请填写活动时间");
            }
        }

        if (basicInfoDTO.getPlatform() != null && PlatformEnum.containLive(basicInfoDTO.getPlatform()) && Objects.equals(
                scopeConfigDTO.getScopeType(), ScopeTypeEnum.MERCHANT_POOL.getCode())) {
            throw new BizException("当前渠道不支持人群包");
        }
//        //活动时间校验,活动范围一致的情况下
//        ActivityBasicQueryDTO queryDTO = ActivityNewConverter.INSTANCE.dtoToQueryDTO(basicInfoDTO);
//        queryDTO.setScopeType(scopeConfigDTO.getScopeType());
//        queryDTO.setScopeIds(scopeConfigDTO.getScopeIds());
//        //创建的如果活动时间是永久的，需要转换一下时间去比较,endTime为2099-12-31 23:59:59
//        if (queryDTO.getIsPermanent() == 1) {
//            queryDTO.setStartTime(new Date());
//            queryDTO.setEndTime(new Date(4102415999000L));
//        }
//
//        List<ActivityScopeConfig> scopeConfigs = activityBasicInfoMapper.selectByScope(queryDTO);
//        if (CollectionUtil.isEmpty(scopeConfigs)) {
//            log.info("本次活动不存在与其他活动重复的活动范围,queryDTO:{}", queryDTO);
//            return;
//        }
//        List<Long> basicInfoIds = scopeConfigs.stream().map(x -> x.getBasicInfoId()).distinct()
//                .collect(Collectors.toList());
//        //还需要根据活动id重新查询一下活动范围
//        List<ActivityScopeConfig> allScopeConfigs = activityScopeConfigMapper.listByBasicInfoIds(
//                basicInfoIds);
//
//        Map<Long, List<ActivityScopeConfig>> basicToScopeMap = allScopeConfigs.stream()
//                .collect(Collectors.groupingBy(ActivityScopeConfig::getBasicInfoId));
//        List<Long> scopeIds = scopeConfigDTO.getScopeIds();
//        List<String> skus = itemConfigDTO.getSkuDetailList().stream().map(x -> x.getSku())
//                .distinct().collect(Collectors.toList());
//        for (Entry<Long, List<ActivityScopeConfig>> entry : basicToScopeMap.entrySet()) {
//            List<ActivityScopeConfig> scopes = entry.getValue();
//            List<Long> existScopeIds = scopes.stream().map(x -> x.getScopeId())
//                    .collect(Collectors.toList());
//            //范围完全一致，时间有重合校验
//            boolean scopeAllMatch = existScopeIds.containsAll(scopeIds);
//            if (scopeAllMatch && scopes.size() == scopeIds.size()) {
//                throw new BizException("本活动与活动id:" + entry.getKey() + "存在时间重合，不允许创建");
//            }
//
//            //范围部分一致，时间有重合，商品有重合
//            List<Long> scopeAnyMatch = scopeIds.stream().filter(x -> existScopeIds.contains(x))
//                    .collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(scopeAnyMatch)) {
//                //判断是否有商品重复
//                ActivityItemConfig itemConfig = activityItemConfigMapper.getByInfoId(
//                        entry.getKey());
//                List<ActivitySkuDetail> skuDetails = activitySkuDetailMapper.selectByItemConfig(
//                        itemConfig.getId());
//                List<String> existSkus = skuDetails.stream()
//                        .filter(x -> skus.contains(x.getSku())).map(x -> x.getSku())
//                        .collect(Collectors.toList());
//                if (CollectionUtil.isNotEmpty(existSkus)) {
//                    //根据活动范围类型，查询范围对应的名称
//                    List<ActivityScopeDTO> scopeDTOList = getScopes(scopeConfigDTO, scopeAnyMatch);
//                    String scopeMatchName = scopeDTOList.stream().map(x -> x.getScopeName())
//                            .collect(Collectors.joining(","));
//                    String skuStr = existSkus.stream().collect(Collectors.joining(","));
//                    throw new BizException(
//                            "本活动与活动id:" + entry.getKey() + "存在冲突:" + skuStr + "在" + scopeMatchName + "已存在");
//                }
//            }
//        }

    }

    /**
     * 判断活动状态
     *
     * @param switchStatus 开关状态
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param isPermanent  永久
     * @return
     */
    public ActivityStatusEnum checkActivityStatus(Integer switchStatus, Date startTime,
            Date endTime, Integer isPermanent) {
        //永久
        if (isPermanent == 1) {
            if (switchStatus == 1) {
                return ActivityStatusEnum.EFFECTING;
            } else {
                return ActivityStatusEnum.NOT_VALID;
            }
        }
        //固定时间
        Date now = new Date();
        if (now.before(startTime)) {
            return ActivityStatusEnum.NOT_VALID;
        }

        if (now.after(startTime) && now.before(endTime)) {
            if (switchStatus == 1) {
                return ActivityStatusEnum.EFFECTING;
            } else {
                return ActivityStatusEnum.NOT_VALID;
            }
        }

        if (now.after(endTime)) {
            return ActivityStatusEnum.FINISHED;
        }
        return null;
    }

    /**
     * 校验并获取活动范围
     *
     * @param basicInfoId
     * @param sku
     * @return
     */
    public List<ActivityScopeConfig> checkAndGetScopes(Long basicInfoId, String sku) {
        //校验sku是否在活动中
        List<ActivitySkuDetail> skuDetailList = activitySkuDetailMapper.listByBasicInfoIds(
                Lists.newArrayList(basicInfoId), sku);
        if (CollectionUtil.isEmpty(skuDetailList)) {
            throw new BizException("当前活动中不存在此sku");
        }
        List<ActivityScopeConfig> scopeConfigs = activityScopeConfigMapper.selectByInfoId(
                basicInfoId, null);
        if (CollectionUtil.isEmpty(scopeConfigs)) {
            throw new BizException("当前活动范围缺失");
        }
        return scopeConfigs;
    }

    /**
     * 构建活动范围信息
     *
     * @param basicInfoId
     * @param activityNewDTO
     */
    public void buildScopeConfig(Long basicInfoId, ActivityNewDTO activityNewDTO,
            boolean needDetail) {
        List<ActivityScopeConfig> scopeConfigs = activityScopeConfigMapper.selectByInfoId(
                basicInfoId, null);
        ActivityScopeConfigDTO scopeConfigDTO = new ActivityScopeConfigDTO();
        scopeConfigDTO.setScopeType(scopeConfigs.get(0).getScopeType());
        List<Long> scopeIds = scopeConfigs.stream().map(x -> x.getScopeId())
                .collect(Collectors.toList());
        scopeConfigDTO.setScopeIds(scopeIds);
        activityNewDTO.setScopeConfigDTO(scopeConfigDTO);
        if (!needDetail) {
            return;
        }
        List<ActivityScopeDTO> scopes = getScopes(scopeConfigDTO, scopeIds);
        scopeConfigDTO.setScopes(scopes);

    }

    private List<ActivityScopeDTO> getScopes(ActivityScopeConfigDTO scopeConfigDTO,
            List<Long> scopeIds) {
        List<ActivityScopeDTO> scopes = Lists.newArrayList();
        switch (ScopeTypeEnum.getByCode(scopeConfigDTO.getScopeType())) {
            case ALL:
                break;
            case MERCHANT_POOL:
                List<MerchantPoolInfo> poolInfos = merchantPoolInfoMapper.getNameByIds(scopeIds);
                scopes = poolInfos.stream().map(t -> {
                    ActivityScopeDTO scopeDTO = new ActivityScopeDTO();
                    scopeDTO.setScopeId(t.getId());
                    scopeDTO.setScopeName(t.getName());
                    return scopeDTO;
                }).collect(Collectors.toList());
                break;
            case AREA:
                List<Integer> areaNos = scopeIds.stream()
                        .map(t -> Integer.valueOf(String.valueOf(t))).collect(
                                Collectors.toList());
                List<Area> areas = areaMapper.selectAreaNos(areaNos);
                scopes = areas.stream().map(t -> {
                    ActivityScopeDTO scopeDTO = new ActivityScopeDTO();
                    scopeDTO.setScopeId(t.getAreaNo().longValue());
                    scopeDTO.setScopeName(t.getAreaName());
                    return scopeDTO;
                }).collect(Collectors.toList());
                break;
            case LARGE_AREA:
                List<Integer> largeAreaNos = scopeIds.stream()
                        .map(t -> Integer.valueOf(String.valueOf(t))).collect(
                                Collectors.toList());
                List<LargeArea> largeAreas = largeAreaMapper.getByAreaNos(largeAreaNos);
                scopes = largeAreas.stream().map(t -> {
                    ActivityScopeDTO scopeDTO = new ActivityScopeDTO();
                    scopeDTO.setScopeId(t.getLargeAreaNo().longValue());
                    scopeDTO.setScopeName(t.getLargeAreaName());
                    return scopeDTO;
                }).collect(Collectors.toList());
                break;
        }
        return scopes;
    }

    /**
     * 构建商品配置信息
     *
     * @param basicInfoId
     * @param activityNewDTO
     */
    public void buildItemConfig(Long basicInfoId, ActivityNewDTO activityNewDTO,
            boolean needDetail) {
        ActivityItemConfig itemConfig = activityItemConfigMapper.getByInfoId(basicInfoId);

        ActivityItemConfigDTO itemConfigDTO = ActivityNewConverter.INSTANCE.itemConfigToDto(
                itemConfig);
        Long itemConfigId = itemConfig.getId();
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), itemConfig.getGoodSelectWay())) {
            List<ActivitySkuDetail> skuDetailList = activitySkuDetailMapper.selectByItemConfig(
                    itemConfigId);
            if (CollectionUtil.isEmpty(skuDetailList)) {
                activityNewDTO.setItemConfigDTO(itemConfigDTO);
                return;
            }
            List<ActivitySkuDetailDTO> skuDetailDTOList = ActivityNewConverter.INSTANCE.skuDetailListToDtoList(
                    skuDetailList);
            itemConfigDTO.setSkuDetailList(skuDetailDTOList);
            activityNewDTO.setItemConfigDTO(itemConfigDTO);
            if (!needDetail) {
                return;
            }
            //组装商品信息数据,批量查询商品
            List<String> skus = skuDetailDTOList.stream().map(x -> x.getSku())
                    .collect(Collectors.toList());
            List<SkuBaseInfoDTO> skuBaseInfoDTOS = inventoryMapper.selectSkuBaseInfosBySku(
                    skus);
            Map<String, SkuBaseInfoDTO> skuBaseInfoDTOMap = skuBaseInfoDTOS.stream()
                    .collect(Collectors.toMap(x -> x.getSku(), Function.identity()));
            skuDetailDTOList.stream().forEach(x -> {
                SkuBaseInfoDTO skuBaseInfoDTO = skuBaseInfoDTOMap.get(x.getSku());
                if(skuBaseInfoDTO != null) {
                    x.setSkuName(skuBaseInfoDTO.getPdName());
                    x.setWeight(skuBaseInfoDTO.getWeight());
                    x.setUnit(skuBaseInfoDTO.getPackaging());
                    x.setLogo(skuBaseInfoDTO.getPicturePath());
                }
            });
        } else {
            //类目或者标签处理

        }
    }

    /**
     * 处理计算活动价&&倒挂监控处理
     *
     * @param activityNewDTO
     * @param skuDetailListFinal
     */
    public void dealActivitySkuPrice(ActivityNewDTO activityNewDTO,
            List<ActivitySkuDetail> skuDetailListFinal) {
        log.info("营销活动价开始异步处理,activityNewDTO:{}, skuDetailListFinal:{}", JSON.toJSONString(activityNewDTO), JSON.toJSONString(skuDetailListFinal));
        Long basicInfoId = activityNewDTO.getBasicInfoDTO().getBasicInfoId();
        try {
            Integer adminId = activityNewDTO.getAdminId();
            Integer scopeType = activityNewDTO.getScopeConfigDTO().getScopeType();

            log.info("营销活动价开始异步处理,活动id:{}", basicInfoId);
            //人群包暂不处理价格
            if (Objects.equals(scopeType, ScopeTypeEnum.MERCHANT_POOL.getCode())) {
                log.warn("活动id:{},活动范围为【人群包】,跳过活动价计算处理", basicInfoId);
                return;
            }
            //获取运营城市
            List<Integer> areaNos = activityNewDTO.getScopeConfigDTO().getScopeIds().stream()
                    .map(x -> x.intValue()).collect(Collectors.toList());
            if (Objects.equals(scopeType, ScopeTypeEnum.LARGE_AREA.getCode())) {
                areaNos = areaMapper.selectByLargeAreaNos(areaNos);
            }
            if (Objects.equals(GoodSelectWayEnum.SKU.getCode(),
                    activityNewDTO.getItemConfigDTO().getGoodSelectWay())) {
                if (CollectionUtil.isEmpty(skuDetailListFinal)) {
                    skuDetailListFinal =
                            activitySkuDetailMapper.selectByItemConfig(
                                    activityNewDTO.getItemConfigDTO().getId());
                }
                List<String> skus = skuDetailListFinal.stream().map(x -> x.getSku())
                        .collect(Collectors.toList());
                log.info("活动id:{}开始计算活动价格,areaNos:{},skus:{}", basicInfoId, areaNos, skus);
                //按sku维度处理
                for (ActivitySkuDetail skuDetail : skuDetailListFinal) {
                    List<ActivitySkuPrice> list = Lists.newArrayList();
                    String sku = skuDetail.getSku();
                    String ladderConfig = skuDetail.getLadderConfig();
                    List<ActivityLadderConfigDTO> ladderConfigDTOS = JSON.parseArray(ladderConfig, ActivityLadderConfigDTO.class);

                    if(CollectionUtil.isEmpty(ladderConfigDTOS)) {
                        log.warn("活动id:{},sku活动详情还未配置!商品sku:{} ,运营区域:{} ", basicInfoId, sku, JSON.toJSONString(areaNos));
                        continue;
                    }

                    List<ActivitySkuPrice> lowReminderPrice = Lists.newArrayList();
                    for (Integer areaNo : areaNos) {
                        // 每个areaNo独立捕获异常
                        this.dealSingleActivitySkuPrice(list, lowReminderPrice, areaNo, basicInfoId, sku, ladderConfigDTOS, skuDetail, adminId);
                    }
                    if (CollectionUtil.isEmpty(list)) {
                        log.warn("活动id:{},活动商品sku:{} 在配置的运营区域:{} 下都不存在", basicInfoId, sku, areaNos);
                        continue;
                    }
                    if (CollectionUtil.isNotEmpty(lowReminderPrice)) {
                        log.info("开始处理特价倒挂提醒,sku:{},倒挂信息:{}", sku, lowReminderPrice);
                        //集中处理倒挂
                        try {
                            activityPriceLowReminder(lowReminderPrice, skuDetail);
                        } catch (Exception e) {
                            log.error("处理特价倒挂提醒异常,跳过处理,活动id:{},sku:{}", basicInfoId, sku);
                        }
                    }
                    log.info("开始保存活动价信息,sku:{},活动价信息:{}", sku, list);
                    //批量插入价格数据，按sku维度执行一次批量
                    activitySkuPriceMapper.insertBatch(list);
                }
            }
        } catch (Exception e) {
            log.error("创建营销活动,处理活动价异常,活动id:{},cause:{}", basicInfoId,
                    Throwables.getStackTraceAsString(e));
        }

    }

    private void dealSingleActivitySkuPrice(List<ActivitySkuPrice> list, List<ActivitySkuPrice> lowReminderPrice, Integer areaNo, Long basicInfoId, String sku, List<ActivityLadderConfigDTO> ladderConfigDTOS, ActivitySkuDetail skuDetail, Integer adminId){
        try {
            AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
            if (areaSku == null) {
                log.warn("活动id:{},活动商品sku:{} 在运营区域:{} 下不存在", basicInfoId, sku, areaNo);
                return;
            }

            //成本价查询,可以优化成库存仓去查成本
            BigDecimal costPrice = cycleInventoryCostService.selectCostByAreaNo(sku,
                    areaNo);
            PriceStrategyAuditRecordVO vo = null;
            BigDecimal minActivityPrice = null;
            List<ActivityLadderPriceDTO> activityLadderPriceDTOList = new ArrayList<>();
            for (ActivityLadderConfigDTO dto : ladderConfigDTOS) {
                PriceStrategy strategy = new PriceStrategy();
                strategy.setAdjustType(dto.getAdjustType());
                strategy.setAmount(dto.getAmount());
                strategy.setRoundingMode(dto.getRoundingMode());
                vo = priceStrategyService.calcStrategyPrice(
                        strategy,
                        costPrice, areaSku.getPrice());
                if (vo == null || vo.getNewPrice() == null) {
                    log.error("活动价计算异常,sku={},areaNo:{}, price:{}", sku, areaNo, JSON.toJSONString(vo));
                    continue;
                }
                minActivityPrice = minActivityPrice == null ? vo.getNewPrice() : minActivityPrice.min(vo.getNewPrice());
                ActivityLadderPriceDTO priceDTO = new ActivityLadderPriceDTO();
                priceDTO.setUnit(dto.getUnit());
                priceDTO.setPrice(vo.getNewPrice());
                activityLadderPriceDTOList.add(priceDTO);
            }
            ActivitySkuPrice activitySkuPrice = new ActivitySkuPrice();
            activitySkuPrice.setBasicInfoId(basicInfoId);
            activitySkuPrice.setSkuDetailId(skuDetail.getId());
            activitySkuPrice.setSku(sku);
            activitySkuPrice.setAreaNo(areaNo);
            activitySkuPrice.setSalePrice(areaSku.getPrice());
            //activitySkuPrice.setActivityPrice(vo.getNewPrice());
            activitySkuPrice.setLadderPrice(JSON.toJSONString(activityLadderPriceDTOList));
            activitySkuPrice.setMinActivityPrice(minActivityPrice);
            activitySkuPrice.setUpdaterId(adminId);
            list.add(activitySkuPrice);
            //记录下倒挂的价格信息,获取不到成本的不处理倒挂提醒
            if (costPrice != null && minActivityPrice != null && costPrice.compareTo(minActivityPrice) == 1) {
                lowReminderPrice.add(activitySkuPrice);
            }
        } catch (Exception e) {
            log.error("创建营销活动,处理活动价异常,活动id:{},sku={},areaNo:{},cause:{}", basicInfoId,sku, areaNo,
                    Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 构建补充列表页信息
     *
     * @param respDTO
     */
    public void buildPageRespDTO(ActivityPageRespDTO respDTO) {
        //获取活动范围
        List<ActivityScopeConfig> scopeConfigs = activityScopeConfigMapper
                .selectByInfoId(respDTO.getBasicInfoId(), null);
        respDTO.setTotalScope(scopeConfigs.size());
        Integer scopeType = scopeConfigs.get(0).getScopeType();
        respDTO.setScopeType(scopeType);
        List<Long> scopeIds = scopeConfigs.stream().map(t -> t.getScopeId()).limit(5)
                .collect(Collectors.toList());
        //查询活动范围名称
        List<String> scopeNameList = Lists.newArrayList();
        switch (ScopeTypeEnum.getByCode(scopeType)) {
            case ALL:
                break;
            case MERCHANT_POOL:
                List<MerchantPoolInfo> poolInfos = merchantPoolInfoMapper.getNameByIds(
                        scopeIds);
                scopeNameList = poolInfos.stream().map(t -> t.getName())
                        .collect(Collectors.toList());
                break;
            case AREA:
                List<Integer> areaNos = scopeIds.stream()
                        .map(t -> Integer.valueOf(String.valueOf(t))).collect(
                                Collectors.toList());
                scopeNameList = areaMapper.getNameByAreaNos(areaNos);
                break;
            case LARGE_AREA:
                List<Integer> largeAreaNos = scopeIds.stream()
                        .map(t -> Integer.valueOf(String.valueOf(t))).collect(
                                Collectors.toList());
                //这里展示的时候还是会把失效的展示
                List<LargeArea> largeAreas = largeAreaMapper.getByAreaNos(largeAreaNos);
                scopeNameList = largeAreas.stream().map(t -> t.getLargeAreaName())
                        .collect(Collectors.toList());
                break;
        }
        respDTO.setScopeNameList(scopeNameList);

        if (respDTO.getCreatorId() == 0) {
            respDTO.setCreatorName("系统");
        } else {
            //查询创建人
            Admin admin = adminMapper.selectByAid(respDTO.getCreatorId());
            respDTO.setCreatorName(admin.getRealname());
        }

        //判断活动状态
        ActivityStatusEnum activityStatusEnum = checkActivityStatus(
                respDTO.getStatus(), respDTO.getStartTime(), respDTO.getEndTime(),
                respDTO.getIsPermanent());
        respDTO.setActivityStatus(activityStatusEnum == null ? null : activityStatusEnum.getCode());
    }

    /**
     * @param skus
     * @param areaNo
     * @return
     */
    public List<ActivityItemScopeDTO> listActivityItemConfigs(List<String> skus, Integer areaNo) {
        //查询运营城市生效中的临保或者特价活动,不需要考虑人群包的活动
        List<ActivityScopeQueryDTO> scopeList = buildScopeList(areaNo);
        List<ActivityItemScopeDTO> itemConfigs = Lists.newArrayList();
        //特价活动查询
        List<ActivityItemScopeDTO> specialItemConfigs = activityBasicInfoMapper.listByScope(
                scopeList,
                ActivityTypeEnum.SPECIAL_PRICE.getCode(), ActivityStatusEnum.EFFECTING.getCode());
        itemConfigs.addAll(specialItemConfigs);

        if (skus.size() == 1) {
            //临保活动查询（非临保品可跳过）
            Inventory inventory = inventoryMapper.queryBySku(skus.get(0));
            //有可能因为sku是失效的查不到
            if (inventory == null) {
                return itemConfigs;
            }
            if (Objects.equals(inventory.getExtType(),
                    InventoryExtTypeEnum.TEMPORARY_INSURANCE.type())) {
                List<ActivityItemScopeDTO> extItemConfigs = activityBasicInfoMapper.listByScope(
                        scopeList,
                        ActivityTypeEnum.NEAR_EXPIRED.getCode(),
                        ActivityStatusEnum.EFFECTING.getCode());
                itemConfigs.addAll(extItemConfigs);
            }
        } else {
            List<ActivityItemScopeDTO> extItemConfigs = activityBasicInfoMapper.listByScope(
                    scopeList,
                    ActivityTypeEnum.NEAR_EXPIRED.getCode(),
                    ActivityStatusEnum.EFFECTING.getCode());
            itemConfigs.addAll(extItemConfigs);
        }

        return itemConfigs;
    }

    /**
     * 查询所有未失效的特价活动商品信息
     *
     * @param sku
     * @param areaNo
     * @return
     */
    public List<ActivitySkuDetail> listValidActivitySkuDetails(String sku, Integer areaNo) {
        //查询未失效的特价活动,不需要考虑人群包的活动
        List<ActivityScopeQueryDTO> scopeList = buildScopeList(areaNo);
        //特价活动查询
        List<ActivityItemScopeDTO> specialItemConfigs = activityBasicInfoMapper.listValidByScope(
                scopeList, ActivityTypeEnum.SPECIAL_PRICE.getCode());
        List<Long> itemConfigIds = specialItemConfigs.stream().map(x -> x.getItemConfigId())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(itemConfigIds)) {
            return Lists.newArrayList();
        }
        List<ActivitySkuDetail> skuDetails = activitySkuDetailMapper.listByItemConfigs(
                itemConfigIds, sku);
        return skuDetails;
    }

    private List<ActivityScopeQueryDTO> buildScopeList(Integer areaNo) {
        Area area = areaMapper.selectByAreaNo(areaNo);
        Integer largeAreaNo = area.getLargeAreaNo();
        List<ActivityScopeQueryDTO> scopeList = Lists.newArrayList();
        scopeList.add(
                new ActivityScopeQueryDTO(Long.valueOf(areaNo), ScopeTypeEnum.AREA.getCode()));
        scopeList.add(new ActivityScopeQueryDTO(Long.valueOf(largeAreaNo),
                ScopeTypeEnum.LARGE_AREA.getCode()));
        return scopeList;
    }

    /**
     * 只处理一个sku
     *
     * @param skuPrices
     * @param skuDetail
     */
    private void activityPriceLowReminder(List<ActivitySkuPrice> skuPrices, ActivitySkuDetail skuDetail) {
        if (CollectionUtil.isEmpty(skuPrices)) {
            log.info("无需处理特价倒挂提醒");
            return;
        }
        if (skuDetail == null) {
            log.error("skuDetail为空,无法处理特价倒挂提醒");
            return;
        }
        String sku = skuPrices.get(0).getSku();
        Long basicInfoId = skuPrices.get(0).getBasicInfoId();
        //聚合处理,根据运营城市获取到围栏
        List<Integer> areaNos = skuPrices.stream().map(x -> x.getAreaNo()).distinct()
                .collect(Collectors.toList());
        //就算一个运营城市多个围栏，最终sku+城配仓 也只会对应到一个库存仓
        List<Fence> fences = fenceMapper.listByAreaNos(areaNos);
        if (CollectionUtil.isEmpty(fences)) {
            log.error("无需处理特价倒挂提醒,围栏获取失败,活动id:{}", basicInfoId);
            return;
        }
        Map<Integer, Integer> areaStoreMap = fences.stream()
                .collect(Collectors.toMap(x -> x.getAreaNo(), x -> x.getStoreNo()));
        List<Integer> storeNos = fences.stream().map(x -> x.getStoreNo()).distinct()
                .collect(Collectors.toList());
        List<WarehouseInventoryMapping> inventoryMappings = warehouseInventoryMappingMapper.listWareHouseNos(
                storeNos, sku);
        Map<Integer, Integer> storeWareHouseMap = inventoryMappings.stream()
                .collect(Collectors.toMap(x -> x.getStoreNo(), x -> x.getWarehouseNo()));
        Map<Integer, List<ActivitySkuPrice>> listMap = skuPrices.stream().collect(
                Collectors.toMap(x -> {
                    Integer areaNo = x.getAreaNo();
                    Integer storeNo = areaStoreMap.get(areaNo);
                    Integer wareHouseNo = storeWareHouseMap.get(storeNo);
                    return wareHouseNo;
                }, x -> Lists.newArrayList(x), (a, b) -> {
                    a.addAll(b);
                    return a;
                }));
        //按库存仓维度去处理
        sendDingDingMsg(basicInfoId, sku, listMap, skuDetail);

    }

    private void sendDingDingMsg(Long baseInfoId, String sku,
                                 Map<Integer, List<ActivitySkuPrice>> listMap, ActivitySkuDetail skuDetail) {
        try {
            List<ActivitySkuPrice> listAll = Lists.newArrayList();
            listMap.entrySet().stream().forEach(x -> {
                List<ActivitySkuPrice> skuPrices = x.getValue();
                listAll.addAll(skuPrices);
            });
            List<Integer> areaNos = listAll.stream().map(x -> x.getAreaNo()).distinct()
                    .collect(Collectors.toList());
            List<AreaInfoDTO> areaInfoDTOS = areaMapper.listByAreaNos(areaNos);
            Map<Integer, AreaInfoDTO> areaLargeAreaMap = areaInfoDTOS.stream()
                    .collect(Collectors.toMap(x -> x.getAreaNo(), Function.identity()));

            Config config = configMapper.selectOne(
                    ConfigValueEnum.SPECIAL_PRICE_ACTIVITY_INVERSION_ROBOT.getKey());
            InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);

            ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(baseInfoId);
            DecimalFormat decimalFormat = new DecimalFormat("#0.00");
            // 查询sku在所有区域
            String title = "【特价倒挂提醒】";
            StringBuilder content = new StringBuilder("##### " + title + "\n");
            content.append("> ###### 活动信息：")
                    .append(Optional.ofNullable(basicInfo).map(x -> x.getName()).orElse(""))
                    .append(":").append(baseInfoId).append("\n");
            content.append("> ###### 商品名称：").append(inventoryVO.getPdName()).append("\n");
            content.append("> ###### 商品规格：").append(inventoryVO.getWeight()).append("\n");
            content.append("> ###### 商品SKU：").append(sku).append("\n");

            skuDetail = activitySkuDetailMapper.selectByPrimaryKey(skuDetail.getId());
            if (skuDetail != null) {
                if (Objects.equals(skuDetail.getAccountLimit(), AccountLimitEnum.NOT_LIMIT.getCode())) {
                    content.append("> ###### 是否限购：").append("不限购").append("\n");
                } else if (Objects.equals(skuDetail.getAccountLimit(), AccountLimitEnum.NUMBER_LIMIT.getCode())) {
                    content.append("> ###### 是否限购：限购").append(skuDetail.getLimitQuantity()).append("件").append("\n");
                } else if (Objects.equals(skuDetail.getAccountLimit(), AccountLimitEnum.DAILY_NUMBER_LIMIT.getCode())) {
                    content.append("> ###### 是否限购：限购").append(skuDetail.getLimitQuantity()).append("件/日").append("\n");
                }
            }
            content.append("> ###### 倒挂城市如下：").append("\n\n");

            for (Entry<Integer, List<ActivitySkuPrice>> entry : listMap.entrySet()) {
                Integer warehouseNo = entry.getKey();
                String warehouseName = Global.warehouseMap.get(entry.getKey());
                if (StringUtils.isEmpty(warehouseName)) {
                    log.error("未从Global.warehouseMap中获取到仓库名称,仓库编号:{}", warehouseNo);
                }
                // 1.获取最新批次成本信息
                CostChangeVo costChangeVo = areaStoreMapper.selectLastBatchCostPriceBySkuAndAreaNo(
                        warehouseNo, sku);
                if (costChangeVo == null || costChangeVo.getCostPrice() == null) {
                    log.warn("最新批次成本信息异常,warehouseNo:{},sku:{}", warehouseNo, sku);
                }
                String newestCostStr = Optional.ofNullable(costChangeVo)
                        .map(CostChangeVo::getCostPrice)
                        .map(x -> decimalFormat.format(x)).orElse("-");
                String batch = Optional.ofNullable(costChangeVo).map(CostChangeVo::getBatch)
                        .orElse("-");
                BigDecimal costPrice = cycleInventoryCostService.selectCostByWarehouseNo(sku,
                        warehouseNo);
                String cycleCostStr = Optional.ofNullable(costPrice)
                        .map(x -> decimalFormat.format(x))
                        .orElse("-");
                content.append("> ###### 库存仓：").append(warehouseName).append("\n")
                        .append("> ###### 日周期库存成本：")
                        .append(cycleCostStr).append("\n")
                        .append("> ###### 最新批次成本：")
                        .append(newestCostStr).append("\n")
                        .append("> ###### 批次号：").append(batch)
                        .append("\n");
                for (int i = 0; i < entry.getValue().size(); i++) {
                    ActivitySkuPrice skuPrice = entry.getValue().get(i);
                    if (CollectionUtil.isEmpty(areaLargeAreaMap) || !areaLargeAreaMap.containsKey(skuPrice.getAreaNo())) {
                        log.info("sendDingDingMsg--未从areaLargeAreaMap中获取到大区信息,areaNo:{}", skuPrice.getAreaNo());
                        continue;
                    }
                    AreaInfoDTO areaInfoDTO = areaLargeAreaMap.get(skuPrice.getAreaNo());
                    content.append("> ###### ")
                            .append(i + 1).append(".")
                            .append(areaInfoDTO.getLargeAreaName()).append(" - ")
                            .append(areaInfoDTO.getAreaName()).append(" - ")
                            .append("活动价：").append(skuPrice.getMinActivityPrice()).append("\n");
                }
            }

            Map<String, String> msg = new HashMap<>();
            msg.put("title", title);
            msg.put("text", content.toString());
            DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> msg, null);
        } catch (Exception e) {
            log.error("倒挂钉钉提醒异常,sku:{}, listMap:{}", sku, JSON.toJSONString(listMap), e);
        }
    }

    public Map<Integer, Integer> buildAreaWarehouseMap(String sku, Set<Integer> areaNos) {
        List<Fence> fences = fenceMapper.listByAreaNos(areaNos);
        if (CollectionUtil.isEmpty(fences)) {
            log.warn("围栏获取失败,sku:{},areaNos:{}", sku, areaNos);
            return Maps.newHashMap();
        }
        Map<Integer, Integer> areaStoreMap = fences.stream()
                .collect(Collectors.toMap(x -> x.getAreaNo(), x -> x.getStoreNo()));
        List<Integer> storeNos = fences.stream().map(x -> x.getStoreNo()).distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(storeNos)) {
            return Maps.newHashMap();
        }
        List<WarehouseInventoryMapping> inventoryMappings = warehouseInventoryMappingMapper.listWareHouseNos(
                storeNos, sku);
        Map<Integer, Integer> storeWareHouseMap = inventoryMappings.stream()
                .collect(Collectors.toMap(x -> x.getStoreNo(), x -> x.getWarehouseNo()));
        Map<Integer, Integer> areaNoWarehouseNoMap = areaNos.stream()
                .filter(x -> areaStoreMap.get(x) != null
                        && storeWareHouseMap.get(areaStoreMap.get(x)) != null)
                .collect(Collectors.toMap(x -> x, x -> {
                    Integer storeNo = areaStoreMap.get(x);
                    Integer wareHouseNo = storeWareHouseMap.get(storeNo);
                    return wareHouseNo;
                }));
        return areaNoWarehouseNoMap;
    }

    /**
     * 读取批量导入的excel
     *
     * @param file
     * @return
     */
    public ActivitySkuBatchDTO dealWithExcel(MultipartFile file) {
        ActivitySkuBatchDTO activitySkuBatchDTO = new ActivitySkuBatchDTO();
        try {
            InputStream inputStream = file.getInputStream();
            EasyExcel.read(inputStream, ActivitySkuImportDTO.class,
                    new ActivitySkuDataListener(this, activitySkuBatchDTO)).sheet().doRead();
        } catch (Exception e) {
            log.error("【营销活动】excel批量处理活动sku失败,cause:{}", Throwables.getStackTraceAsString(e));
            throw new BizException(ErrorCode.EXCEL_PARSE_ERROR.getMessage(), e);
        }
        return activitySkuBatchDTO;
    }

    /**
     * excel读取构建对象
     *
     * @param list
     * @return
     */
    public ActivitySkuBatchDTO buildActivitySkuBatchDTO(List<ActivitySkuImportDTO> list) {
        ActivitySkuBatchDTO activitySkuBatchDTO = new ActivitySkuBatchDTO();
        List<ActivitySkuDetailDTO> skuDetailDTOList = Lists.newArrayList();
        List<String> failedSkus = Lists.newArrayList();
        List<String> skus = list.stream().filter(x -> !StringUtils.isEmpty(x.getSku()))
                .map(x -> x.getSku()).distinct().collect(Collectors.toList());
        List<SkuBaseInfoDTO> skuBaseInfoDTOS = inventoryMapper.selectSkuBaseInfosBySku(
                skus);
        Map<String, SkuBaseInfoDTO> skuBaseInfoDTOMap = skuBaseInfoDTOS.stream()
                .collect(Collectors.toMap(x -> x.getSku(), Function.identity()));

        Map<String, List<ActivitySkuImportDTO>> collect = list.stream().collect(Collectors.groupingBy(ActivitySkuImportDTO::getSku));
        collect.forEach((sku, dtoList) -> {
            SkuBaseInfoDTO skuBaseInfoDTO = skuBaseInfoDTOMap.get(sku);
            if (skuBaseInfoDTO == null) {
                log.warn("【营销活动】未获取到sku信息,sku:{}", sku);
                failedSkus.add(sku);
                return;
            }

            ActivitySkuDetailDTO skuDetailDTO = new ActivitySkuDetailDTO();
            skuDetailDTO.setSku(sku);
            skuDetailDTO.setSkuName(skuBaseInfoDTO.getPdName());
            skuDetailDTO.setWeight(skuBaseInfoDTO.getWeight());
            skuDetailDTO.setUnit(skuBaseInfoDTO.getPackaging());
            skuDetailDTO.setLogo(skuBaseInfoDTO.getPicturePath());

            List<ActivityLadderConfigDTO> ladderConfigDTOS = new ArrayList<>();
            Integer actualQuantity = null;
            Integer limitQuantity = null;
            for (ActivitySkuImportDTO activitySkuImportDTO : dtoList) {
                ActivityLadderConfigDTO ladderConfigDTO = new ActivityLadderConfigDTO();
                // 默认阶梯配置
                ladderConfigDTO.setUnit(activitySkuImportDTO.getUnit());
                ladderConfigDTO.setRoundingMode(0);
                ladderConfigDTO.setAdjustType(AdjustTypeEnum.FIXED_PRICE.getCode());
                ladderConfigDTO.setAmount(activitySkuImportDTO.getAmount());
                ladderConfigDTOS.add(ladderConfigDTO);
                // 库存和限购数量只取第一个
                actualQuantity = actualQuantity == null ? activitySkuImportDTO.getActualQuantity() : actualQuantity;
                limitQuantity = limitQuantity == null ? activitySkuImportDTO.getLimitQuantity() : limitQuantity;
            }
            skuDetailDTO.setActivityLadderConfigDTOList(ladderConfigDTOS);
            //默认为正常定价、指定价形式、不限购
            if(limitQuantity == null) {
                skuDetailDTO.setAccountLimit(AccountLimitEnum.NOT_LIMIT.getCode());
                skuDetailDTO.setLimitQuantity(0);
            } else {
                skuDetailDTO.setAccountLimit(AccountLimitEnum.NUMBER_LIMIT.getCode());
                skuDetailDTO.setLimitQuantity(limitQuantity);
            }
            skuDetailDTO.setActualQuantity(
                    Optional.ofNullable(actualQuantity).orElse(100000));
            skuDetailDTOList.add(skuDetailDTO);
        });
        activitySkuBatchDTO.setFailedSkus(failedSkus);
        activitySkuBatchDTO.setSkuDetailDTOList(skuDetailDTOList);
        return activitySkuBatchDTO;
    }


}
