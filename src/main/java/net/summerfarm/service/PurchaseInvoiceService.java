package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.FinancePurchaseInvoiceWallets;
import net.summerfarm.model.input.FinanceAccountStatementQuery;
import net.summerfarm.model.input.FinancePurchaseInvoiceWalletsInput;
import net.summerfarm.model.input.PurchaseInvoiceQuery;
import net.summerfarm.model.input.StockTaskWalletsInput;
import net.summerfarm.model.vo.InvoiceSupplierVO;
import net.summerfarm.model.vo.PurchaseInvoiceVO;
import net.summerfarm.model.vo.bms.BmsPaymentDocumentVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @title: PurchaseInvoiceService
 * @date 2021/8/414:26
 */
public interface PurchaseInvoiceService {

    /**
     * 待匹配列表查询
     *
     * @param pageIndex
     * @param pageSize
     * @param purchaseInvoiceQuery
     * @return
     */
    AjaxResult selectCanMatch(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 已归档的票夹
     * @param pageIndex
     * @param pageSize
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult selectArchived(Integer pageIndex, Integer pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 待提交状态的列表查询
     *
     * @param pageIndex
     * @param pageSize
     * @param purchaseInvoiceQuery
     * @return
     */
    AjaxResult selectToBeSubmitted(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 待复核的票夹
     * @param pageIndex
     * @param pageSize
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult selectToBeFiled(Integer pageIndex, Integer pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 待归档的票夹
     * @param pageIndex
     * @param pageSize
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult selectWaitingArchiving(Integer pageIndex, Integer pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 除待提交外采购发票信息excel导出
     *
     * @param purchaseInvoiceQuery
     * @throws IOException
     */
    void upload(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 含税金额总计
     *
     * @param purchaseInvoiceQuery
     * @return
     */
    AjaxResult includedTaxAmount(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 新增发票
     *
     * @param purchaseInvoiceVO
     * @return
     */
    AjaxResult save(PurchaseInvoiceVO purchaseInvoiceVO);

    /**
     * srm供应商添加发票
     * @param purchaseInvoiceVO
     * @return
     */
    String srmSave(PurchaseInvoiceVO purchaseInvoiceVO);

    /**
     * srm查询发票详情
     * @param id
     * @return
     */
    PurchaseInvoiceVO queryInvoiceDetail(Integer id);

    /**
     * 删除采购发票
     * @param id
     * @param supplierName
     */
    void deleteInvoice(Integer id, String supplierName);

    /**
     * 检验发票编码
     *
     * @param invoiceCode
     * @param invoiceNumber
     * @return
     */
    AjaxResult checkInvoice(String invoiceCode, String invoiceNumber);

    /**
     * 模板下载
     * @param response
     * @throws IOException
     */
    void download(HttpServletResponse response) throws IOException;

    /**
     * 【待匹配】采购发票批量导入模板下载
     * @return
     */
    AjaxResult selectCanMatchDownload( );

    /**
     * 采购发票新增发票导入
     *
     * @param file
     * @return
     */
    AjaxResult importFile(MultipartFile file);

    /**
     * 【待匹配】采购发票批量导入
     *
     * @param file
     * @param invoiceType
     * @param invoiceForm
     * @return
     */
    AjaxResult batchImport(MultipartFile file, Integer invoiceType, Integer invoiceForm);

    /**
     * 供应商信息
     *
     * @return
     */
    AjaxResult selectSupplier();

    /**
     * 展示导入excel解析结果 0 成功结果 1失败结果
     *
     * @param pageIndex
     * @param pageSize
     * @param analysisType
     * @return
     */
    AjaxResult analysis(Integer pageIndex, Integer pageSize, Integer analysisType);

    /**
     * 解析excel含税金额总计
     *
     * @param analysisType
     * @return
     */
    AjaxResult includedTaxSum(Integer analysisType);

    /**
     * 新增excel导入成功数据
     *
     * @return
     */
    AjaxResult analysisSave();

    /**
     * 采购发票新增发票批量导出解析失败excel
     *
     * @return
     */
    void analysisFail();

    /**
     * 发票详情
     *
     * @param purchaseInvoiceId
     * @param status
     * @return
     */
    AjaxResult check(Integer purchaseInvoiceId, Integer status);

    /**
     * 采购发票修改
     *
     * @param purchaseInvoiceVO
     * @return
     */
    AjaxResult update(PurchaseInvoiceVO purchaseInvoiceVO);

    /**
     * 复核通过（归档修改）
     *
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult reviewPassed(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 回溯(各个阶段回到上个阶段)
     *
     * @param purchaseInvoiceQuery
     * @return
     */
    AjaxResult backTracking(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 供应商信息和其待匹配发票还有待匹配对账单信息
     * @param supplierId 供应商id
     * @return
     */
    AjaxResult selectBySupplier(Integer supplierId,String supplierName);

    /**
     * 可匹配的采购发票
     * @param pageIndex
     * @param pageSize
     * @param purchaseInvoiceQuery
     * @return
     */
    AjaxResult selectByInvoice(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * 可匹配的对账单
     * @param pageIndex
     * @param pageSize
     * @param financeAccountStatementQuery
     * @return
     */
    AjaxResult selectByBill(Integer pageIndex, Integer pageSize, FinanceAccountStatementQuery financeAccountStatementQuery);

    /**
     * 生成票夹，将采购发票与对账单关联
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult saveWalletsWithBill(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 票夹设置展示
     * @return
     */
    AjaxResult showWallets();

    /**
     * 修改票夹的设置内容
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult updateWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 检验发票的匹配情况
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult checkInvoiceWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 票夹详情
     * @param id
     * @param status
     * @param expenseType
     * @return
     */
    AjaxResult checkWallets(Long id, Integer status, Integer expenseType);

    /**
     * 票夹入库单展示
     * @param pageIndex
     * @param pageSize
     * @param stockTaskWalletsInput
     * @return
     */
    AjaxResult showWarehousingOrder(Integer pageIndex, Integer pageSize, StockTaskWalletsInput stockTaskWalletsInput);

    /**
     * 票夹入库单对账单数据
     * @param stockTaskWalletsInput
     * @return
     */
    AjaxResult show(StockTaskWalletsInput stockTaskWalletsInput);

    /**
     * 待复核票夹解散
     *
     * @param id
     * @param expenseType
     * @param remark
     * @return
     */
    AjaxResult backTrackingWallets(Long id, Integer expenseType, String remark);

    /**
     * 待复核票夹发票数据
     * @param pageIndex
     * @param pageSize
     * @param id
     * @return
     */
    AjaxResult showInvoice(Integer pageIndex, Integer pageSize, Long id);

    /**
     * 票夹含税金额总计
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult statisticsWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 已归档发票数据导出
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult downloadWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 已归档票夹数据下载
     * @param ids
     * @param adminId
     * @param fileName
     */
    void archivedDownloads(List<Long> ids, Integer adminId, String fileName);

    /**
     * 票夹匹配明细数据下载
     * @param id
     * @param adminId
     * @param fileName
     */
    void matchingDetailsDownloads(Long id, Integer adminId, String fileName);

    /**
     * 批量票夹匹配明细数据下载
     * @param financePurchaseInvoiceWalletsInput
     * @param adminId
     * @param fileName
     */
    void matchingDetailsAllDownloads(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput, Integer adminId, String fileName);

    /**
     * 票夹匹配明细下载
     * @param financePurchaseInvoiceWallets
     * @return
     */
    AjaxResult downloadShow(FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets);

    /**
     * 票夹明细批量导出
     * @param financePurchaseInvoiceWalletsInput
     * @return
     */
    AjaxResult allWalletsDetailedDownload(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput);

    /**
     * 自动匹配发票
     * @param purchaseInvoiceId
     * @return
     */
    AjaxResult autoMatchInvoice(Integer purchaseInvoiceId);

    /**
     * 新增excel导入成功数据(待匹配)
     *
     * @return
     */
    AjaxResult newSave();

    /**
     * srm对账单匹配发票
     * @param purchaseInvoiceIdList
     * @param financeAccountStatementId
     * @param totalIncludedTax
     * @param supplierName
     * @param supplierId
     * @return
     */
    String addWallets(List<Integer> purchaseInvoiceIdList, Long financeAccountStatementId, BigDecimal totalIncludedTax, String supplierName, Integer supplierId);

    /**
     * srm对账单查询发票信息
     * @param pageIndex
     * @param pageSize
     * @param purchaseInvoiceQuery
     * @return
     */
    List<PurchaseInvoiceVO> selectSrmInvoice(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * srm对账单查询发票信息
     * @param pageIndex
     * @param pageSize
     * @param walletsId
     * @return
     */
    List<PurchaseInvoiceVO> selectSrmInvoiceByWalletsId(Integer pageIndex, Integer pageSize, Long walletsId);
    /**
     * 发票销售方信息
     * @return
     * @param purchaseInvoiceQuery 根据supplierName查询
     */
    AjaxResult<List<InvoiceSupplierVO>> invoiceSellerMessage(PurchaseInvoiceQuery purchaseInvoiceQuery);

    /**
     * BMS打款单匹配发票生成票夹
     * @param invoiceIdList
     * @param paymentDocumentId
     */
    void matchBmsInvoice(List<Integer> invoiceIdList, BmsPaymentDocumentVO bmsPaymentDocumentVO);

    /**
     * Bms承运商可匹配发票查询
     * @param invoiceSearchKey
     * @param taxNumber
     * @return
     */
    List<PurchaseInvoiceVO> invoiceBms(String invoiceSearchKey,String taxNumber);

    /**
     * 根据id查询发票信息
     * @param id
     * @return
     */
    PurchaseInvoiceVO queryByInvoiceId(Integer id);
}
