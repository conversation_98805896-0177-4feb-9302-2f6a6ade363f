package net.summerfarm.service.bms.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.*;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.bms.BmsCalculateTypeEnum;
import net.summerfarm.enums.bms.BmsQuotaFormEnum;
import net.summerfarm.mapper.bms.*;
import net.summerfarm.mapper.manage.CarrierMapper;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.model.DTO.bms.BmsPathParam;
import net.summerfarm.model.DTO.bms.BmsSiteDTO;
import net.summerfarm.model.domain.Carrier;
import net.summerfarm.model.domain.CarrierQuotationArea;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.bms.*;
import net.summerfarm.model.domain.easyexcel.bms.BmsSettleAccountListExcel;
import net.summerfarm.model.input.bms.BmsCostAdjustmentQuery;
import net.summerfarm.model.input.bms.BmsDeliveryQuotationQuery;
import net.summerfarm.model.input.bms.BmsSettleAccountQuery;
import net.summerfarm.model.vo.ContactVO;
import net.summerfarm.model.vo.bms.*;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.infrastructure.facade.pms.PmsCarrierQueryFacade;
import net.summerfarm.service.bms.BmsSettleAccountService;
import net.summerfarm.service.bms.CostAdjustmentService;
import net.summerfarm.service.bms.ExpressRunnerService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySectionDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.provider.delivery.DeliveryBatchQueryProvider;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;
import net.summerfarm.warehouse.mapper.WarehouseLogisticsCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.vo.WarehouseLogisticsCenterVO;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.Global.*;

/**
 * <AUTHOR>
 * @create 2022/8/23
 */
@Service
public class BmsSettleAccountServiceImpl extends BaseService implements BmsSettleAccountService {

    @Resource
    private BmsSettleAccountMapper bmsSettleAccountMapper;
    @Resource
    private BmsCalculationDetailsMapper bmsCalculationDetailsMapper;
    @Resource
    private BmsCostAdjustmentMapper bmsCostAdjustmentMapper;
    @Resource
    private BmsDeliveryReconciliationMapper bmsDeliveryReconciliationMapper;
    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;
    @Resource
    private BmsDeliveryQuotationMapper bmsDeliveryQuotationMapper;
    @Resource
    private BmsDeliveryQuoteCalculateCostMapper bmsDeliveryQuoteCalculateCostMapper;
    @Resource
    private BmsDeliveryQuotationDetailMapper bmsDeliveryQuotationDetailMapper;
    @Resource
    private ExpressRunnerService expressRunnerService;
    @Resource
    private BmsDeliverySettleAccountsDetailMapper bmsDeliverySettleAccountsDetailMapper;
    @Resource
    private BmsSettleAccountItemMapper bmsSettleAccountItemMapper;
    @Resource
    private BmsDeliveryReconciliationAdjustmentMapper bmsReconciliationAdjustmentMapper;
    @Resource
    private BmsQuotationProcessMapper bmsQuotationProcessMapper;
    @Resource
    private BmsDeliveryQuotationRegionMapper bmsDeliveryQuotationRegionMapper;
    @Resource
    private CarrierMapper carrierMapper;
    @Resource
    private BmsDeliveryQuotationAreaMapper bmsDeliveryQuotationAreaMapper;
    @Resource
    private BmsCalculationItemMapper bmsCalculationItemMapper;
    @Resource
    private CostAdjustmentService costAdjustmentService;
    @Resource
    private BmsDeliveryQuotaCalculateCostRangeMapper bmsDeliveryQuotaCalculateCostRangeMapper;
    @Resource
    private PmsCarrierQueryFacade pmsCarrierQueryFacade;
    @Resource
    private ConfigMapper configMapper;
    @DubboReference
    private DeliveryBatchQueryProvider deliveryBatchQueryProvider;
    private BmsSettleAccountService selfService;
    @Resource
    private DynamicConfig dynamicConfig;

    @PostConstruct
    private void setSelf() {
        selfService = getContext().getBean(BmsSettleAccountService.class);
    }

    @Override
    public AjaxResult selectSettleAccount(BmsSettleAccountQuery param) {
        List<CarrierQuotationArea> quotationAreas = param.getQuotationAreas();
        // 处理省市多选
        if (!CollectionUtils.isEmpty(quotationAreas)) {
            List<String> cities = quotationAreas.stream().map(CarrierQuotationArea::getCity).collect(Collectors.toList());
            param.setCities(cities);
        }

        if (Objects.nonNull(param.getHaveExamineTask())) {
            List<Integer> caAccountIds = bmsCostAdjustmentMapper.selectAllInReview();
            List<Integer> raAccountIds = bmsReconciliationAdjustmentMapper.selectAllInReview(1);
            caAccountIds.addAll(raAccountIds);
            if (Objects.equals(0, param.getHaveExamineTask())) {
                if (!CollectionUtils.isEmpty(caAccountIds)) {
                    param.setFilterSettleAccountIds(caAccountIds);
                }
            } else {
                if (CollectionUtils.isEmpty(caAccountIds)) {
                    return AjaxResult.getOK(PageInfoHelper.createPageInfo(new ArrayList<>()));
                }
                param.setSettleAccountIds(caAccountIds);
            }
        }
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<BmsSettleAccountVO> result = bmsSettleAccountMapper.selectSettleAccount(param);
        result.forEach(settleAccount -> {
            settleAccount.setStoreName(storeMap.get(settleAccount.getStoreNo()));
            settleAccount.setDeliveryDate(settleAccount.getDeliveryEndDate());
            settleAccount.setHaveReconciliation(Objects.nonNull(settleAccount.getReconciliationNo()) ? 1 : 0);
            // 查询审批中调整单
            BmsDeliveryReconciliationAdjustment adjustment = bmsReconciliationAdjustmentMapper.selectLast(settleAccount.getId(), 1);
            if (Objects.nonNull(adjustment) && Objects.equals(0, adjustment.getStatus())) {
                settleAccount.setHaveExamineTask(1);
            } else {
                Integer count = bmsCostAdjustmentMapper.selectInReviewByAccountId(settleAccount.getId());
                if (count > NumberUtils.INTEGER_ZERO) {
                    settleAccount.setHaveExamineTask(1);
                } else {
                    settleAccount.setHaveExamineTask(0);
                }
            }
            // 明细单报价详情
            List<BmsCalculationDetailsVO> calculationDetails = bmsCalculationDetailsMapper.selectByAccountId(settleAccount.getId());
            // 应付费用
            BigDecimal accountPayable = calculationDetails.stream().filter(details -> !"扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 加上卸货费用
            BigDecimal totalAmount = accountPayable.add(settleAccount.getDischargeAmount());
            settleAccount.setPayableAmount(totalAmount);
            // 扣减费用
            BigDecimal deductionPayable = calculationDetails.stream().filter(details -> "扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            settleAccount.setDeductionAmount(deductionPayable);
        });
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(result));
    }

    @Override
    public AjaxResult checkSettleAccount(List<Integer> settleAccountIds) {
        if (CollectionUtils.isEmpty(settleAccountIds)) {
            return AjaxResult.getErrorWithMsg("未选中结算明细单");
        }
        for (Integer accountId : settleAccountIds) {
            BmsSettleAccountVO accountVO = bmsSettleAccountMapper.selectByPrimaryKey(accountId);
            // 查询审核中费用调整单
            Integer count = bmsCostAdjustmentMapper.selectInReviewByAccountId(accountId);
            if (count > NumberUtils.INTEGER_ZERO) {
                return AjaxResult.getErrorWithMsg((Objects.nonNull(accountVO) ? accountVO.getDeliveryDate() + accountVO.getServiceAreaName() + storeMap.get(accountVO.getStoreNo()) + accountVO.getCarrierName() + accountVO.getProvince() + accountVO.getCity() : "") + "任务中存在审核中的调整单，请审核通过后再完成核对");
            }
            // 是否存在审批中的卸货费用调整单
            BmsDeliveryReconciliationAdjustment adjustment = bmsReconciliationAdjustmentMapper.selectSettlementInApproval(accountId, 1);
            if (Objects.nonNull(adjustment)) {
                return AjaxResult.getErrorWithMsg((Objects.nonNull(accountVO) ? accountVO.getDeliveryDate() + accountVO.getServiceAreaName() + storeMap.get(accountVO.getStoreNo()) + accountVO.getCarrierName() + accountVO.getProvince() + accountVO.getCity() : "") + "任务中存在审核中的调整单，请审核通过后再完成核对");
            }
        }
        bmsSettleAccountMapper.checkSettleAccount(settleAccountIds);
        // 记录操作记录
        BmsQuotationProcess process = new BmsQuotationProcess();
        settleAccountIds.forEach(settleAccountId -> {
            process.setSourceId(settleAccountId);
            process.setCreator(getAdminName());
            process.setType(0);
            process.setOperationContent("结算核对");
            bmsQuotationProcessMapper.insert(process);
        });

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult reconciliationSettleAccount(BmsSettleAccountQuery param) {
        if (CollectionUtils.isEmpty(param.getSettleAccountIds())) {
            return AjaxResult.getErrorWithMsg("未选中需要对账的结算明细单");
        }
        if (StringUtils.isEmpty(param.getReconciliationProofUrl())) {
            return AjaxResult.getErrorWithMsg("请上传对账凭证");
        }

        List<BmsSettleAccountVO> accountVos = bmsSettleAccountMapper.selectByIds(param.getSettleAccountIds());
        Set<Integer> storeNoSet = accountVos.stream().map(BmsSettleAccountVO::getStoreNo).collect(Collectors.toSet());
        Set<Integer> carrierSet = accountVos.stream().map(BmsSettleAccountVO::getCarrierId).collect(Collectors.toSet());
        if (storeNoSet.size() > NumberUtils.INTEGER_ONE || carrierSet.size() > NumberUtils.INTEGER_ONE) {
            return AjaxResult.getErrorWithMsg("城配仓承运商不统一,请仔细检查");
        }
        //校验数据
        AjaxResult result = checkReconciliationSettleAccount(accountVos);
        if (!result.isSuccess()) {
            return result;
        }
        if (CollectionUtils.isEmpty(accountVos)) {
            return AjaxResult.getErrorWithMsg("结算单数据异常");
        }
        //前置仓费用
        BigDecimal advanceWarehouseAmount = BigDecimal.ZERO;
        for (BmsSettleAccountVO accountVo : accountVos) {
            if (!Objects.equals(accountVo.getStatus(), 1)) {
                return AjaxResult.getErrorWithMsg("该明细单未核对");
            }
            List<BmsDeliveryReconciliation> reconciliations = bmsDeliveryReconciliationMapper.selectInReconciliationByAccountId(accountVo.getId());
            if (!CollectionUtils.isEmpty(reconciliations)) {
                return AjaxResult.getErrorWithMsg("该明细单存在对账中的对账单,请对账完成后再发起对账");
            }
            List<BmsDeliveryQuotation> quotations = bmsDeliveryQuotationMapper.selectBySettleId(accountVo.getId());
            for (BmsDeliveryQuotation quotation : quotations) {
                BigDecimal amount = bmsDeliveryQuotationDetailMapper.selectAdvanceByQuotationId(quotation.getId());
                if (advanceWarehouseAmount.compareTo(BigDecimal.ZERO) == 0 || advanceWarehouseAmount.compareTo(amount) > 0) {
                    advanceWarehouseAmount = amount;
                }
            }
        }

        BigDecimal payableAmount = bmsSettleAccountMapper.sumPayableAmount(param.getSettleAccountIds());
        BigDecimal amount = bmsSettleAccountMapper.sumDischargeAmount(param.getSettleAccountIds());
        BigDecimal totalAmount = payableAmount.add(amount);
        BigDecimal sumDischargeAmount = bmsSettleAccountMapper.sumDeductionAmount(param.getSettleAccountIds());
        if (totalAmount.compareTo(sumDischargeAmount) < NumberUtils.INTEGER_ZERO) {
            return AjaxResult.getErrorWithMsg("扣减金额大于应付金额");
        }
        BmsDeliveryReconciliation insert = new BmsDeliveryReconciliation();
        LocalDate today = LocalDate.now();
        String number = "01" + today.toString().replaceAll("-", "");
        int sum = bmsDeliveryReconciliationMapper.count(number) + 1;
        BmsSettleAccountVO accountVO = accountVos.get(NumberUtils.INTEGER_ZERO);
        insert.setReconciliationNo(number + sum)
                .setCarrierId(accountVO.getCarrierId())
                .setActualAmount(totalAmount)
                .setDeductionAmount(sumDischargeAmount)
                .setReconciliationProofUrl(param.getReconciliationProofUrl())
                .setPayableAmount(totalAmount)
                .setRemake(param.getRemake())
                .setStoreNo(accountVO.getStoreNo())
                .setServiceAreaId(accountVO.getQuotationAreaId())
                .setCreator(getAdminId())
                .setAdvanceWarehouseAmount(advanceWarehouseAmount)
                .setSettleMonth(String.valueOf(accountVO.getDeliveryStartDate()).substring(0,7));
        bmsDeliveryReconciliationMapper.insert(insert);

        BmsQuotationProcess process = new BmsQuotationProcess();
        accountVos.forEach(account -> {
            bmsSettleAccountMapper.relationReconciliation(account.getId(), insert.getId());

            // 记录明细单操作记录
            process.setSourceId(account.getId());
            process.setCreator(getAdminName());
            process.setType(0);
            process.setOperationContent("发起对账");
            bmsQuotationProcessMapper.insert(process);
        });

        // 记录对账单操作记录
        process.setSourceId(insert.getId());
        process.setCreator(getAdminName());
        process.setType(1);
        process.setOperationContent("生成对账单");
        bmsQuotationProcessMapper.insert(process);

        return AjaxResult.getOK();
    }

    @Override
    public void exportSettleAccountList(BmsSettleAccountQuery param) throws IOException {

        if (Objects.nonNull(param.getDeliveryStartDate()) && Objects.nonNull(param.getDeliveryEndDate())) {
            Duration duration = Duration.between(param.getDeliveryStartDate().atStartOfDay(), param.getDeliveryEndDate().atStartOfDay());
            if (duration.toDays() + 1 > 31) {
                throw new ParamsException("导出结算单数据超过一个月");
            }
        }

        List<CarrierQuotationArea> quotationAreas = param.getQuotationAreas();
        // 处理省市多选
        if (!CollectionUtils.isEmpty(quotationAreas)) {
            List<String> cities = quotationAreas.stream().map(CarrierQuotationArea::getCity).collect(Collectors.toList());
            param.setCities(cities);
        }

        if (Objects.nonNull(param.getHaveExamineTask())) {
            List<Integer> caAccountIds = bmsCostAdjustmentMapper.selectAllInReview();
            List<Integer> raAccountIds = bmsReconciliationAdjustmentMapper.selectAllInReview(1);
            caAccountIds.addAll(raAccountIds);
            if (Objects.equals(0, param.getHaveExamineTask())) {
                if (CollectionUtils.isEmpty(caAccountIds)) {
                    throw new ParamsException("无可导出数据");
                }
                param.setFilterSettleAccountIds(caAccountIds);
            } else {
                if (CollectionUtils.isEmpty(caAccountIds)) {
                    throw new ParamsException("无可导出数据");
                }
                param.setSettleAccountIds(caAccountIds);
            }
        }
        List<BmsSettleAccountVO> result = bmsSettleAccountMapper.selectSettleAccount(param);
        result.forEach(settleAccount -> {
            settleAccount.setStoreName(storeMap.get(settleAccount.getStoreNo()));
            settleAccount.setDeliveryDate(settleAccount.getDeliveryEndDate());
            settleAccount.setHaveReconciliation(Objects.nonNull(settleAccount.getReconciliationNo()) ? 1 : 0);
            // 查询审批中调整单
            BmsDeliveryReconciliationAdjustment adjustment = bmsReconciliationAdjustmentMapper.selectLast(settleAccount.getId(), 1);
            if (Objects.nonNull(adjustment) && Objects.equals(0, adjustment.getStatus())) {
                settleAccount.setHaveExamineTask(1);
            } else {
                Integer count = bmsCostAdjustmentMapper.selectInReviewByAccountId(settleAccount.getId());
                if (count > NumberUtils.INTEGER_ZERO) {
                    settleAccount.setHaveExamineTask(1);
                } else {
                    settleAccount.setHaveExamineTask(0);
                }
            }
            // 明细单报价详情
            List<BmsCalculationDetailsVO> calculationDetails = bmsCalculationDetailsMapper.selectByAccountId(settleAccount.getId());
            // 应付费用
            BigDecimal accountPayable = calculationDetails.stream().filter(details -> !"扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 加上卸货费用
            BigDecimal totalAmount = accountPayable.add(settleAccount.getDischargeAmount());
            settleAccount.setPayableAmount(totalAmount);
            // 扣减费用
            BigDecimal deductionPayable = calculationDetails.stream().filter(details -> "扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            settleAccount.setDeductionAmount(deductionPayable);
        });

        List<BmsSettleAccountListExcel> excelDatas = Lists.newArrayList();
        for (BmsSettleAccountVO accountVO : result) {
            BmsSettleAccountListExcel excel = new BmsSettleAccountListExcel();
            excel.setServiceArea(Objects.isNull(accountVO.getServiceAreaName()) ? "" : accountVO.getServiceAreaName());
            excel.setProvinceCity(accountVO.getProvince() + "/" + accountVO.getCity());
            excel.setStoreName(storeMap.get(accountVO.getStoreNo()));
            excel.setCarrierName(accountVO.getCarrierName());
            excel.setStatus(Objects.equals(0, accountVO.getStatus()) ? "待核对" : "已核对");
            excel.setHaveReconciliation(Objects.isNull(accountVO.getReconciliationNo()) ? "否" : "是");
            excel.setHaveExamineTask(Objects.equals(0, accountVO.getHaveExamineTask()) ? "否" : "是");
            excel.setDeliveryDate(accountVO.getDeliveryDate().toString());
            excel.setPayableAmount(accountVO.getPayableAmount());
            excel.setDeductionAmount(accountVO.getDeductionAmount());
            excel.setActualAmount(accountVO.getPayableAmount().subtract(accountVO.getDeductionAmount()));
            excel.setReconciliationNo(accountVO.getReconciliationNo());
            excelDatas.add(excel);
        }
        HttpServletResponse response = RequestHolder.getResponse();
        String fileName = "结算明细单.xls";
        ExcelUtils.setResponseHeader(response, fileName);
        String[] fieldNames = {"配送日期", "服务区域", "城配仓", "承运商", "服务省市", "应付费用", "扣减费用", "实际应付", "是否存在审核中的任务", "明细单状态", "是否生成对账单", "对账单编号"};
        List<String> list = new ArrayList<>(Arrays.asList(fieldNames));
        EasyExcel.write(response.getOutputStream())
                .head(detailExcelDynamicHead(list)).sheet("结算明细单").doWrite(excelDatas);
    }

    @Override
    public void exportSettleAccountDetail(BmsSettleAccountQuery param) throws IOException {
        BmsSettleAccountVO accountVO = bmsSettleAccountMapper.selectByPrimaryKey(param.getId());

        // 处理服务省市区
        if (!CollectionUtils.isEmpty(param.getQuotationAreas())) {
            List<String> districts = param.getQuotationAreas().stream().map(CarrierQuotationArea::getArea).collect(Collectors.toList());
            param.setPaths(districts);
        }
        if (Objects.nonNull(param.getHaveExamineTask())) {
            List<Integer> accountDetailsIds = bmsCostAdjustmentMapper.selectPathInReview(param.getId());
            if (CollectionUtils.isEmpty(accountDetailsIds)) {
                throw new ParamsException("无可导出数据");
            }
            if (Objects.equals(1, param.getHaveExamineTask())) {
                param.setSettleAccountIds(accountDetailsIds);
            } else {
                param.setFilterSettleAccountIds(accountDetailsIds);
            }
        }
        List<BmsDeliverySettleAccountsDetailVO> accountsDetails = bmsDeliverySettleAccountsDetailMapper.selectByCondition(param);
        Set<String> calculateNameSet = new HashSet<>();
        accountsDetails.forEach(detail -> {
            if (Objects.nonNull(detail.getQuotationId())) {
                BmsDeliveryQuotationArea area = bmsDeliveryQuotationAreaMapper.selectById(detail.getServiceAreaId());
                if (Objects.nonNull(area)) {
                    detail.setServiceAreaName(area.getArea());
                }
            }
            detail.setStoreName(storeMap.get(detail.getStoreNo()));
            if (Objects.nonNull(detail.getCarrierId())) {
                Carrier carrier = carrierMapper.selectByPrimaryKey(Long.valueOf(detail.getCarrierId()));
                detail.setCarrierName(carrier.getCarrierName());
            }

            detail.setDistricts(Arrays.asList(detail.getPassingArea().split(SEPARATING_SYMBOL)));

            BmsDeliveryQuotationVO quotationVO = bmsDeliveryQuotationMapper.selectById(detail.getQuotationId());
            if (Objects.nonNull(quotationVO)) {
                detail.setProvince(quotationVO.getProvince());
                detail.setCity(quotationVO.getCity());
            }
            List<BmsCalculationDetailsVO> calculationDetails = bmsCalculationDetailsMapper.selectByAccountsDetailId(detail.getId());
            // 应付费用
            BigDecimal accountPayable = calculationDetails.stream().filter(details -> !"扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setPayableAmount(accountPayable);
            // 扣减费用
            BigDecimal deductionPayable = calculationDetails.stream().filter(details -> "扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setDeductionAmount(deductionPayable);
            // 查询审核中费用调整单
            Integer count = bmsCostAdjustmentMapper.selectInReviewByAccountsDetailId(detail.getId());
            detail.setHaveExamineTask(count > NumberUtils.INTEGER_ZERO ? 1 : 0);
            Set<String> calculateNames = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(detail.getQuotationId()).stream().map(BmsDeliveryQuoteCalculateCost::getCalculateName).collect(Collectors.toSet());
            calculateNameSet.addAll(calculateNames);
            calculateNameSet.add("打车费");
            calculateNameSet.add("帮采费");
            calculateNameSet.add("扣减费用");
            calculateNameSet.add("过路费");
            calculateNameSet.add("通行证费");
            calculateNameSet.add("线路补贴费");
            calculateNameSet.add("干线用车费");
            calculateNameSet.add("核酸费");
            calculateNameSet.add("加车费");
            calculateNameSet.add("疫情补贴费");
            calculateNameSet.add("专车费");

            List<BmsDeliveryQuotationRegion> regions = bmsDeliveryQuotationRegionMapper.selectByQuotationId(detail.getQuotationId());
            StringJoiner joiner = new StringJoiner("/");
            regions.forEach(region -> {
                joiner.add(region.getArea());
            });
            detail.setStringDistricts(joiner.toString());
        });

        String[] calculateNameList = calculateNameSet.toArray(new String[0]);
        Workbook workbook = new HSSFWorkbook();

        Sheet trafficSheet = workbook.createSheet("结算明细单费用详情");
        Row trafficTitle = trafficSheet.createRow(0);
        trafficTitle.createCell(0).setCellValue("配送日期");
        trafficTitle.createCell(1).setCellValue("服务区域");
        trafficTitle.createCell(2).setCellValue("城配仓");
        trafficTitle.createCell(3).setCellValue("路线");
        trafficTitle.createCell(4).setCellValue("配送司机");
        trafficTitle.createCell(5).setCellValue("承运商");
        trafficTitle.createCell(6).setCellValue("服务省市");
        trafficTitle.createCell(7).setCellValue("服务行政区/县");
        trafficTitle.createCell(8).setCellValue("是否路线跨区");
        trafficTitle.createCell(9).setCellValue("是否报价冲突");
        trafficTitle.createCell(10).setCellValue("总点位数");
        trafficTitle.createCell(11).setCellValue("打车点位数");
        trafficTitle.createCell(12).setCellValue("非当日配送点位数");
        trafficTitle.createCell(13).setCellValue("系统公里数");
        trafficTitle.createCell(14).setCellValue("实际公里数");
        trafficTitle.createCell(15).setCellValue("系统推荐公里数");
        trafficTitle.createCell(16).setCellValue("满载率");
        int titleIndex = 17;
        for (String s : calculateNameList) {
            trafficTitle.createCell(titleIndex).setCellValue(s);
            titleIndex++;
        }
        int rowIndex = 1;
        for (BmsDeliverySettleAccountsDetailVO accountsDetail : accountsDetails) {
            Row row = trafficSheet.createRow(rowIndex);
            row.createCell(0).setCellValue(accountsDetail.getDeliveryDate().toString());
            row.createCell(1).setCellValue(accountsDetail.getServiceAreaName());
            row.createCell(2).setCellValue(storeMap.get(accountVO.getStoreNo()));
            row.createCell(3).setCellValue(accountsDetail.getPath());
            row.createCell(4).setCellValue(accountsDetail.getDriver());
            row.createCell(5).setCellValue(accountVO.getCarrierName());
            row.createCell(6).setCellValue(accountVO.getProvince() + "/" + accountVO.getCity());
            StringJoiner stringJoiner = new StringJoiner(Global.SEPARATING_SYMBOL);
            accountsDetail.getDistricts().forEach(stringJoiner::add);
            row.createCell(7).setCellValue(stringJoiner.toString());
            row.createCell(8).setCellValue(Objects.equals(0, accountsDetail.getHaveSpannedArea()) ? "否" : "是");
            row.createCell(9).setCellValue(Objects.equals(0, accountsDetail.getHaveQuotationConflict()) ? "否" : "是");
            row.createCell(10).setCellValue(accountsDetail.getTotalPosition());
            row.createCell(11).setCellValue(accountsDetail.getTaxiPosition());
            row.createCell(12).setCellValue(accountsDetail.getNoDeliveryTimePosition());
            row.createCell(13).setCellValue(String.valueOf(accountsDetail.getSystemKilometers()));
            row.createCell(14).setCellValue(String.valueOf(accountsDetail.getActualKilometers()));
            row.createCell(15).setCellValue(String.valueOf(accountsDetail.getSystemRecommendedKilometers()));
            row.createCell(16).setCellValue(String.valueOf(accountsDetail.getLoadFactor()));
            int rowCellIndex = 17;
            Map<String, BmsCalculationDetailsVO> map = bmsCalculationDetailsMapper.selectByAccountsDetailId(accountsDetail.getId()).stream().collect(Collectors.toMap(BmsCalculationDetailsVO::getCalculateName, Function.identity(), (o1, o2) -> o1));
            String stringCellValue = trafficSheet.getRow(0).getCell(rowCellIndex).getStringCellValue();
            while (Objects.nonNull(stringCellValue)) {
                BmsCalculationDetailsVO detailsVO = map.get(stringCellValue);
                if (Objects.nonNull(detailsVO)) {
                    row.createCell(rowCellIndex).setCellValue(String.valueOf(detailsVO.getAmount()));
                } else {
                    row.createCell(rowCellIndex).setCellValue("0.00");
                }
                rowCellIndex++;
                try {
                    stringCellValue = trafficSheet.getRow(0).getCell(rowCellIndex).getStringCellValue();
                } catch (NullPointerException ignored) {
                    break;
                }
            }
            rowIndex++;
        }

        String fileName = "结算明细单费用详情.xls";
        ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());

    }

    @Override
    public AjaxResult selectSettleAccountBase(BmsSettleAccountQuery param) {
        BmsSettleAccountVO accountVO = bmsSettleAccountMapper.selectByPrimaryKey(param.getId());
        // 是否存在审批中的卸货费用调整单
        BmsDeliveryReconciliationAdjustment adjustment = bmsReconciliationAdjustmentMapper.selectSettlementInApproval(param.getId(), 1);
        accountVO.setHaveExamineTask(Objects.nonNull(adjustment) ? 1 : 0);
        BmsDeliveryReconciliationAdjustment lastAdjustment = bmsReconciliationAdjustmentMapper.selectLast(param.getId(), 1);
        if (Objects.nonNull(lastAdjustment)) {
            accountVO.setAdjustmentStatus(lastAdjustment.getStatus());
        }
        accountVO.setStoreName(storeMap.get(accountVO.getStoreNo()));
        accountVO.setStoreNo(accountVO.getStoreNo());
        // 明细单报价详情
        List<BmsCalculationDetailsVO> calculationDetails = bmsCalculationDetailsMapper.selectByAccountId(param.getId());
        // 应付费用
        BigDecimal accountPayable = calculationDetails.stream().filter(details -> !"扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        accountVO.setPayableAmount(accountPayable);
        // 扣减费用
        BigDecimal deductionPayable = calculationDetails.stream().filter(details -> "扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        accountVO.setDeductionAmount(deductionPayable);

        return AjaxResult.getOK(accountVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void settleAccountSingle(Integer storeNo, LocalDate deliveryDate) {
        logger.info("结算明细单生成开始，仓库：{}，配送日期：{}", storeNo, deliveryDate);
        int count = bmsSettleAccountMapper.selectByStoreNoAndDeliveryDate(storeNo, deliveryDate);
        if(SpringContextUtil.isProduct() && count != 0){
            logger.info("结算明细单已生成！仓库：{}，配送日期：{}", storeNo, deliveryDate);
            return;
        }

        //根据城配仓，时间查出配送路线列表，核心
        DeliverySectionQuery deliverySectionQuery = new DeliverySectionQuery();
        deliverySectionQuery.setDeliveryTime(deliveryDate);
        deliverySectionQuery.setStoreNo(String.valueOf(storeNo));
        DubboResponse<List<DeliveryBatchDTO>> tmsDeliveryPathInfo = deliveryBatchQueryProvider.queryBatch(deliverySectionQuery);
        if (Objects.isNull(tmsDeliveryPathInfo) || CollectionUtils.isEmpty(tmsDeliveryPathInfo.getData())) {
            logger.error("{}无配送路线", storeMap.get(storeNo));
            return;
        }
        //只筛选待捡货、配送中、配送完成的数据
        List<DeliveryBatchDTO> tmsPathInfo = tmsDeliveryPathInfo.getData().stream()
                .filter(f->f.getStatus().equals(DeliveryBatchStatusEnum.TO_BE_PICKED.getCode())
                        || f.getStatus().equals(DeliveryBatchStatusEnum.IN_DELIVERY.getCode())
                        || f.getStatus().equals(DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode())).collect(Collectors.toList());

        //0.定义关键数据
        //构造一个新的集合，key为路线名称，value为路线上所有点位，数据源由tms提供加工而来
        Map<String, List<BmsSiteDTO>> deliveryBatchMap = Maps.newHashMap();

        //所有点位数据
        List<BmsSiteDTO> siteDTOList = new ArrayList<>();
        // 所有匹配报价单
        //预置一个Map，因为deliverySite里才有配送信息和拦截信息而siteDto中没有，但是要根据拦截信息和特殊计算公里费，TMS返回公里相关的路段信息中只有SiteDto
        //这里构造一个map放在下面用到的时候get
        Map<Long, List<BmsSiteDTO>> prepareSiteMap = Maps.newHashMap();
        // 1.匹配报价单
        List<BmsDeliveryQuotationVO> wholeQuotations = matchQuotation(tmsPathInfo, siteDTOList, deliveryBatchMap, storeNo, prepareSiteMap);
        // 1.1结算单落库
        Integer accountId = saveBmsSettleAccount(siteDTOList, wholeQuotations, tmsPathInfo, storeNo, deliveryDate);


        // 根据路线分组
        deliveryBatchMap.forEach((path, pathList) -> {
            logger.info("开始计算路线path:{}",path);
            //new一个参数存储用到的各种初始化集合
            BmsPathParam bmsPathParam = new BmsPathParam();
            bmsPathParam.setPathPointList(pathList);
            //设置该路线是正常配送还是专车配送
            long normalSend = pathList.stream().filter(f -> f.getSendWay().equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())).count();
            List<BmsSiteDTO> loneSizeList = pathList.stream().filter(f -> f.getSendWay().equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())).collect(Collectors.toList());
            long specialSend = CollUtil.size(loneSizeList);
            //正常配送为0，专车配送不为0，为专车配送，否则正常配送
            bmsPathParam.setNormalOrSpecial((normalSend == 0 && specialSend != 0) ? DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue() : DeliverySiteEnums.SendWay.NORMAL_SEND.getValue());

            //根据路线名称查出路线详情
            DeliveryBatchDTO deliveryBatchDTO = tmsPathInfo.stream().filter(f -> f.getPathCode().equals(path)).findFirst().orElseThrow(() -> new ProviderException("无法根据路线名称" + path + "找到该路线"));
            bmsPathParam.setMinTotalDistance(findMinTotalDistance(deliveryBatchDTO));
            //孤点路线判断
            bmsPathParam.setLoneSitePath(normalSend != 0 && specialSend != 0);
            if(bmsPathParam.isLoneSitePath()){
                bmsPathParam.setLoneSiteTotalQuantity(specialSend);
                calcLoneSiteDistance(bmsPathParam, deliveryBatchDTO,prepareSiteMap);
                matchingLoneSiteQuotation(storeNo, loneSizeList, bmsPathParam);
            }
            //2.1匹配报价单
            List<BmsDeliveryQuotationVO> quotationVOList;
            if(Objects.equals(bmsPathParam.getNormalOrSpecial(),DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())){
                List<BmsSiteDTO> filterSiteList = pathList.stream()
                        .filter(f -> f.getSendWay().equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()))
                        .filter(f -> f.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())).collect(Collectors.toList());
                quotationVOList  = queryQuoteBySiteList(storeNo,filterSiteList);
            }else{
                List<BmsSiteDTO> filterSiteList = pathList.stream()
                        .filter(f -> f.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())).collect(Collectors.toList());
                quotationVOList  = queryQuoteBySiteList(storeNo,filterSiteList);
            }
            matchingQuotation(deliveryBatchDTO, quotationVOList, bmsPathParam, deliveryDate);

            //2.3计费，填充费用项map
            calculate(bmsPathParam, deliveryBatchDTO, prepareSiteMap);
            // 3.结算单路线明细落库
            saveBmsSettleAccountDetail(bmsPathParam, accountId,  deliveryBatchDTO, path);
            //4.计费模型费用落库
            for (String quotaName : bmsPathParam.getCalculateCostAmountsMap().keySet()) {
                BmsDeliveryQuoteCalculateCostVO costVO = bmsPathParam.getCalculateCostAmountsMap().get(quotaName);
                costVO.setBmsDeliveryQuotationId(bmsPathParam.getAccountsDetailVO().getId());

                if (costVO.getAmount().compareTo(BigDecimal.ZERO) < NumberUtils.INTEGER_ZERO) {
                    costVO.setAmount(BigDecimal.ZERO);
                }

                // 新增结算项信息
                 bmsCalculationDetailsMapper.insert(costVO);
            }
        });
        logger.info("结算明细单生成结束，仓库：{}，配送日期：{}", storeNo, deliveryDate);
    }


    public List<BmsDeliveryQuotationVO> queryQuoteBySiteList(Integer storeNo,List<BmsSiteDTO> filteredBmsSiteDTOList){
        List<BmsDeliveryQuotationVO> wholeQuotations = Lists.newArrayList();
        Map<String, ContactVO> addressMap = new HashMap<>();
        for (SiteDTO siteDTO : filteredBmsSiteDTOList) {
            ContactVO contact = new ContactVO();
            contact.setArea(siteDTO.getArea());
            if (StringUtils.isEmpty(siteDTO.getArea())){
                contact.setArea("默认区");
            }
            contact.setCity(siteDTO.getCity());
            addressMap.putIfAbsent(siteDTO.getCity() + siteDTO.getArea(), contact);
        }

        Iterator<Map.Entry<String, ContactVO>> iterator = addressMap.entrySet().iterator();
        BmsDeliveryQuotationQuery query = new BmsDeliveryQuotationQuery();
        ///根据addressMap，查出对应区域所有的报价单
        while (iterator.hasNext()) {
            Map.Entry<String, ContactVO> entry = iterator.next();
            ContactVO contact = entry.getValue();
            query.setCity(contact.getCity());
            query.setStoreNo(storeNo);
            query.setDistrict(Objects.equals("默认区", contact.getArea()) ? null : contact.getArea());
            query.setStatus(0);
            List<BmsDeliveryQuotationVO> quotationVos = bmsDeliveryQuotationMapper.selectAllDistrict(query);
            // 无行政服务区县设置默认区
            quotationVos.forEach(quotation -> {
                if (StringUtils.isEmpty(quotation.getDistrict())) {
                    quotation.setDistrict("默认区");
                }
            });
            wholeQuotations.addAll(quotationVos);
        }

        return wholeQuotations;

    }

    private void saveBmsSettleAccountDetail(BmsPathParam bmsPathParam,  Integer accountId, DeliveryBatchDTO deliveryBatchDTO, String path) {
        if (CollectionUtils.isEmpty(bmsPathParam.getPathPointList())) {
            logger.warn("路线点位信息为空：path:{}, 配送时间:{}， 起点点位名称:{}", path, deliveryBatchDTO.getDeliveryTime(), deliveryBatchDTO.getBeginSiteName());
        }
        /// 路线结算详情赋值
        BmsDeliverySettleAccountsDetailVO accountsDetailVO = bmsPathParam.getAccountsDetailVO();
        accountsDetailVO.setPath(path);
        accountsDetailVO.setTotalPosition(bmsPathParam.getPathPointList().size());
        accountsDetailVO.setTaxiPosition((Integer) bmsPathParam.getPresetFieldMap().get(TAXI_POSITION));
        int normalSend =(int) bmsPathParam.getPathPointList().stream().filter(f -> Objects.equals(f.getSendWay(), DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())).count();
        int specialSend =(int) bmsPathParam.getPathPointList().stream().filter(f -> Objects.equals(f.getSendWay(), DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())).count();
        accountsDetailVO.setNoDeliveryTimePosition(normalSend == 0 ? (specialSend == 0 ? 0 :  (Integer) bmsPathParam.getPresetFieldMap().get(TOTAL_POINT_QUANTITY)  - (Integer) bmsPathParam.getPresetFieldMap().get(SPECIAL_POINT_QUANTITY)) : (Integer) bmsPathParam.getPresetFieldMap().get(TOTAL_POINT_QUANTITY) - (Integer) bmsPathParam.getPresetFieldMap().get(POINT_QUANTITY));
        accountsDetailVO.setSystemKilometers(deliveryBatchDTO.getPlanTotalDistance());
        accountsDetailVO.setActualKilometers(deliveryBatchDTO.getRealTotalDistance());
        accountsDetailVO.setSystemRecommendedKilometers(deliveryBatchDTO.getIntelligenceTotalDistance());
        // 满载率
        accountsDetailVO.setLoadFactor(Objects.nonNull(deliveryBatchDTO.getPathFullLoadRatio()) && deliveryBatchDTO.getPathFullLoadRatio().compareTo(new BigDecimal(1000)) > 0?  new BigDecimal(1000) : deliveryBatchDTO.getPathFullLoadRatio());

        accountsDetailVO.setSkuQuantity(bmsPathParam.getNormalOrSpecial().equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()) ? (Integer) bmsPathParam.getPresetFieldMap().get(WARES_QUANTITY) :  (Integer) bmsPathParam.getPresetFieldMap().get(SPECIAL_WARES_QUANTITY));
        accountsDetailVO.setQuotationId(Objects.isNull(bmsPathParam.getDiffQuotation()) ? null : (Objects.nonNull(bmsPathParam.getDiffQuotation().getId()) ? bmsPathParam.getDiffQuotation().getId() : null));

        //填充司机/途径区县信息
        accountsDetailVO.setPassingArea(bmsPathParam.getPassingArea());
        accountsDetailVO.setDriver(deliveryBatchDTO.getDriver());
        String province = bmsPathParam.getPathPointList().stream().map(BmsSiteDTO::getProvince).findFirst().orElse(null);
        String city = bmsPathParam.getPathPointList().stream().map(BmsSiteDTO::getCity).findFirst().orElse(null);
        accountsDetailVO.setProvince(province);
        accountsDetailVO.setCity(city);

        bmsDeliverySettleAccountsDetailMapper.insert(accountsDetailVO);

        BmsSettleAccountItem accountItem = new BmsSettleAccountItem();
        accountItem.setSettleAccountId(accountId);
        accountItem.setSettleAccountsDetailsId(accountsDetailVO.getId());
        bmsPathParam.setAccountsDetailVO(accountsDetailVO);
        bmsSettleAccountItemMapper.insert(accountItem);
    }
    private void calculate(BmsPathParam bmsPathParam,  DeliveryBatchDTO deliveryBatchDTO, Map<Long, List<BmsSiteDTO>> prepareSiteMap) {
        // 路线取用的报价单
        bmsPathParam.setDiffQuotation(new BmsDeliveryQuotationVO());
        // 无匹配报价单，生成费用为空的结算单
        if (CollectionUtils.isEmpty(bmsPathParam.getMateQuotations())) {
            //生成系统费用项报价单
//            generateSystemFee(bmsPathParam, cost);
        } else {
            // 最佳费用
            Map<String, BmsDeliveryQuoteCalculateCostVO> bestCalculateCostAmountMap = new HashMap<>();
            //若不跨区，匹配的报价单只有一个走线路计费逻辑，添加限制，之前没有跨区的判断
            if (bmsPathParam.getAccountsDetailVO().getHaveQuotationConflict().equals(NumberUtils.INTEGER_ZERO)
                && Objects.equals(bmsPathParam.getAccountsDetailVO().getHaveSpannedArea(),NumberUtils.INTEGER_ZERO)) {
                bestCalculateCostAmountMap = generateBySingleQuotation(bmsPathParam, bestCalculateCostAmountMap);
            }
            //跨区且有多个报价单,需要计费模型分开计算，择优的同名计费集合取最低值，组合的同名计费集合取累加值，最后再将其同名项累加得到最终结果
            else {

                //获取系统公里数，实际公里数和系统推荐公里数

                //路段全量距离信息
                DubboResponse<List<DeliverySectionDTO>> tmsRoadInfo = deliveryBatchQueryProvider.queryDeliverySection(
                        DeliverySectionQuery.builder().batchId(deliveryBatchDTO.getDeliveryBatchId())
                                .finishDeliveryTime(LocalDateTime.now())
                                //根据排线公里数和实际公里数比较的结果取查询tms路段信息
                                .type(queryType(deliveryBatchDTO))
                                .build());

                if (Objects.isNull(tmsRoadInfo) || !tmsRoadInfo.isSuccess() || CollectionUtils.isEmpty(tmsRoadInfo.getData())) {
                    logger.error("无路段信息");
                } else {

                    List<DeliverySectionDTO> sectionDTOList = ConvertUtils.convert(tmsRoadInfo.getData(), DeliverySectionDTO.class);
                    bmsPathParam.setSectionDTOList(sectionDTOList);
                    sectionDTOList.forEach(f -> logger.info("路段信息数据: 起点：{},起点city：{},终点：{},终点city：{},距离：{}km", f.getBeginSite().getCompleteAddress(),f.getBeginSite().getCity(), f.getEndSite().getCompleteAddress(), f.getEndSite().getCity(), f.getDistance().divide(new BigDecimal(1000)).setScale(2, RoundingMode.HALF_UP)));
                    //构造跨区map
                    // key为跨区的路段信息，value为跨区距离
                    Map<DeliverySectionDTO, BigDecimal> spannedAreaMap = buildSpannedAreaMap(sectionDTOList);
                    bmsPathParam.setSpannedAreaMap(spannedAreaMap);
                    // 区域报价单Map
                    Map<BmsDeliveryQuotationVO, String> metaMap = new HashMap<>(16);
                    Map<String, BmsDeliveryQuotationVO> reverseMetaMap = new HashMap<>(16);
                    bmsPathParam.getMateQuotations().forEach(mate ->  metaMap.put(mate, mate.getCity() + "-" + mate.getDistrict()));
                    bmsPathParam.getMateQuotations().forEach(mate ->  reverseMetaMap.put((mate.getCity() + "-" + mate.getDistrict()), mate));

                    bmsPathParam.setMetaMap(metaMap);
                    bmsPathParam.setReverseMetaMap(reverseMetaMap);
                    //key为城市+区域，value时这个区域里面的所有点位
                    Map<String, List<BmsSiteDTO>> siteAreaMap = new HashMap<>();

                    if(Objects.equals(bmsPathParam.getNormalOrSpecial(),DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())){
                        for (BmsSiteDTO bmsSiteDTO : bmsPathParam.getPathPointList()) {
                            if(!Objects.equals(bmsSiteDTO.getStatus(),DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())){
                                continue;
                            }
                            String cityAreaKey = bmsSiteDTO.getCity() + "-" + bmsSiteDTO.getArea();
                            //如果map中，没有这个区域，返回一个空数组并将这个点位add进去，否则直接add
                            siteAreaMap.computeIfAbsent(cityAreaKey, k -> new ArrayList<>()).add(bmsSiteDTO);
                        }
                    }
                    if(Objects.equals(bmsPathParam.getNormalOrSpecial(),DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())){
                        for (BmsSiteDTO bmsSiteDTO : bmsPathParam.getPathPointList()) {
                            if(Objects.equals(bmsSiteDTO.getSendWay(),DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())){
                                continue;
                            }
                            if(!Objects.equals(bmsSiteDTO.getStatus(),DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())){
                                continue;
                            }
                            String cityAreaKey = bmsSiteDTO.getCity() + "-" + bmsSiteDTO.getArea();
                            //如果map中，没有这个区域，返回一个空数组并将这个点位add进去，否则直接add
                            siteAreaMap.computeIfAbsent(cityAreaKey, k -> new ArrayList<>()).add(bmsSiteDTO);
                        }
                    }

                    bmsPathParam.setSiteAreaMap(siteAreaMap);
                    //计算其他费用,直接根据公式计算
                    bmsPathParam.getDiffQuotation().setId(bmsPathParam.getMateQuotations().get(NumberUtils.INTEGER_ZERO).getId());
                    calculateRegular(bmsPathParam, prepareSiteMap);
                }
            }
            bmsPathParam.getCalculateCostAmountsMap().putAll(bestCalculateCostAmountMap);
        }
    }

    /**
     * 5为查询孤点公里特用类型
     */
    private final static Integer TMS_LONE_DISTANCE_KEY = 5;

    private void calcLoneSiteDistance(BmsPathParam bmsPathParam, DeliveryBatchDTO deliveryBatchDTO, Map<Long, List<BmsSiteDTO>> prepareSiteMap) {
        try {
            BigDecimal distance = BigDecimal.ZERO;
            bmsPathParam.setLoneSiteTotalDistance(distance);

            //孤点路段全量距离信息 5为查询孤点公里特用类型
            DubboResponse<List<DeliverySectionDTO>> loneRoad = deliveryBatchQueryProvider.queryDeliverySection(
                    DeliverySectionQuery.builder().batchId(deliveryBatchDTO.getDeliveryBatchId())
                            .finishDeliveryTime(LocalDateTime.now())
                            .type(TMS_LONE_DISTANCE_KEY)
                            .build());
            if (Objects.isNull(loneRoad) || !loneRoad.isSuccess() || CollectionUtils.isEmpty(loneRoad.getData())) {
                logger.error("无孤点数据");
                return;
            }
            for (DeliverySectionDTO datum : loneRoad.getData()) {
                List<BmsSiteDTO> collection = prepareSiteMap.get(datum.getEndSite().getId());
                if(CollUtil.isNotEmpty(collection)
                        && Objects.nonNull(collection.get(NumberUtils.INTEGER_ZERO))
                        && Objects.equals(collection.get(NumberUtils.INTEGER_ZERO).getStatus(),DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())){
                    distance = distance.add(datum.getDistance());
                }
            }
            bmsPathParam.setLoneSiteTotalDistance(distance.divide(new BigDecimal("1000"),2, RoundingMode.HALF_UP));
            logger.info("孤点数据总距离：{}",bmsPathParam.getLoneSiteTotalDistance());
            logger.info("孤点数据总数：{}",bmsPathParam.getLoneSiteTotalQuantity());
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
    }

    /**
     * 根据tms配送数据找出最低的，用于查询路段信息
     * @param deliveryBatchDTO tms配送信息
     * @return 查询type 0系统排线数据， 1实际配送数据，2系统推进配送数据
     */
    private int queryType(DeliveryBatchDTO deliveryBatchDTO) {
        BigDecimal planTotalDistance = deliveryBatchDTO.getPlanTotalDistance();
        BigDecimal realTotalDistance = deliveryBatchDTO.getRealTotalDistance();
        BigDecimal intelligenceTotalDistance = deliveryBatchDTO.getIntelligenceTotalDistance();
        if (planTotalDistance.compareTo(realTotalDistance) <= 0 && planTotalDistance.compareTo(intelligenceTotalDistance) <= 0)  {
            return 0;
        }else if (realTotalDistance.compareTo(planTotalDistance) <= 0 && realTotalDistance.compareTo(intelligenceTotalDistance) <= 0 ) {
            return 1;
        }
        return 2;
    }

    /**
     * 根据tms配送数据找出最低的，用于查询路段信息
     * @param deliveryBatchDTO tms配送信息
     * @return 查询type 0系统排线数据， 1实际配送数据，2系统推进配送数据
     */
    private BigDecimal findMinTotalDistance(DeliveryBatchDTO deliveryBatchDTO) {
        BigDecimal planTotalDistance = deliveryBatchDTO.getPlanTotalDistance();
        BigDecimal realTotalDistance = deliveryBatchDTO.getRealTotalDistance();
        BigDecimal intelligenceTotalDistance = deliveryBatchDTO.getIntelligenceTotalDistance();
        if (planTotalDistance.compareTo(realTotalDistance) <= 0 && planTotalDistance.compareTo(intelligenceTotalDistance) <= 0)  {
            return planTotalDistance;
        }else if (realTotalDistance.compareTo(planTotalDistance) <= 0 && realTotalDistance.compareTo(intelligenceTotalDistance) <= 0 ) {
            return realTotalDistance;
        }
        return intelligenceTotalDistance;
    }

    @NotNull
    private Map<DeliverySectionDTO, BigDecimal> buildSpannedAreaMap(List<DeliverySectionDTO> sectionDTOList) {
        Map<DeliverySectionDTO, BigDecimal> spannedAreaMap = Maps.newHashMap();
        Config noAreaCity = configMapper.selectOne("NO_AREA_CITY");

        for (DeliverySectionDTO sectionDTO : sectionDTOList) {
            SiteDTO beginSite = sectionDTO.getBeginSite();
            SiteDTO endSite = sectionDTO.getEndSite();
            if (StringUtils.isBlank(beginSite.getArea()) || noAreaCity.getValue().contains(beginSite.getCity())){
                sectionDTO.getBeginSite().setArea("默认区");
            }
            if (StringUtils.isBlank(endSite.getArea()) || noAreaCity.getValue().contains(endSite.getCity())){
                sectionDTO.getEndSite().setArea("默认区");
            }
            if (StringUtils.isBlank(beginSite.getCity())) {
                logger.error("起点城市为空，需要维护点位数据" + beginSite.getCompleteAddress());
                throw new ProviderException("起点城市为空，需要维护点位数据" + beginSite.getCompleteAddress());
            }
            if (StringUtils.isBlank(endSite.getCity())) {
                logger.error("终点城市为空，需要维护点位数据" + beginSite.getCompleteAddress());
                throw new ProviderException("终点城市为空，需要维护点位数据" + endSite.getCompleteAddress());
            }
            if (!beginSite.getCity().equals(endSite.getCity()) || !beginSite.getArea().equals(endSite.getArea())) {
                //跨区，储存到另一个map中
                spannedAreaMap.put(sectionDTO, sectionDTO.getDistance());
            }
        }
        return spannedAreaMap;
    }

    private Map<String, BmsDeliveryQuoteCalculateCostVO> generateBySingleQuotation(BmsPathParam bmsPathParam, Map<String, BmsDeliveryQuoteCalculateCostVO> bestCalculateCostAmountMap) {
        // 比较匹配的报价单实际应付 取最低费用的报价单
        for (BmsDeliveryQuotationVO pathQuotation : bmsPathParam.getMateQuotations()) {
            DefaultContext<String, Object> temporaryFieldMap = new DefaultContext<>();
            List<BmsDeliveryQuotationDetailVO> quotationDetailVos = bmsDeliveryQuotationDetailMapper.selectByQuotationId(pathQuotation.getId());
            quotationDetailVos.forEach(detail -> temporaryFieldMap.put(detail.getQuoteName(), detail.getAmount()));
            // 这份报价单应付费用
            BigDecimal totalAmount = BigDecimal.ZERO;
            // 报价单计费模型
            List<BmsDeliveryQuoteCalculateCostVO> calculateCosts = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(pathQuotation.getId());
            Map<String, BmsDeliveryQuoteCalculateCostVO> thisCalculateCostAmountMap = new HashMap<>();
            for (BmsDeliveryQuoteCalculateCostVO calculateCost : calculateCosts) {
                DefaultContext<String, Object> calculationFieldMap = new DefaultContext<>();
                calculationFieldMap.putAll(temporaryFieldMap);
                calculationFieldMap.putAll(bmsPathParam.getPresetFieldMap());
                String formula = matchingRange(calculateCost, bmsPathParam);
                BigDecimal amount = expressRunnerService.calculation(formula, calculationFieldMap);
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    amount = BigDecimal.ZERO;
                }
                totalAmount = totalAmount.add(amount);
                calculateCost.setAmount(amount);
                calculateCost.setType(1);
                calculateCost.setSourceType(0);
                thisCalculateCostAmountMap.put(calculateCost.getCalculateName(), calculateCost);
            }
            //第一次循环，路线取的报价单diffQuotation的应付费用CopeAmount肯定为null，进入
            if (Objects.isNull(bmsPathParam.getDiffQuotation().getCopeAmount()) ||
                    ((totalAmount.compareTo(BigDecimal.ZERO) > 0) && totalAmount.compareTo(bmsPathParam.getDiffQuotation().getCopeAmount()) < 0)) {
                //设置应付费用和此次循环的报价单id
                bmsPathParam.getDiffQuotation().setCopeAmount(totalAmount);
                bmsPathParam.getDiffQuotation().setId(pathQuotation.getId());
                //最佳费用就是这个thisCalculateCostAmountMap的费用，将其赋值给bestCalculateCostAmountMap
                bestCalculateCostAmountMap = thisCalculateCostAmountMap;
                //临时费用集合添加预置费用集合
                temporaryFieldMap.putAll(bmsPathParam.getPresetFieldMap());
                //最佳报价字段= 临时报价字段
                bmsPathParam.setBestFieldMap(new DefaultContext<>());
                bmsPathParam.getBestFieldMap().putAll(temporaryFieldMap);
            }
        }
        //孤点计费
        BmsDeliveryQuoteCalculateCostVO costVO = calculateLoneSite(bmsPathParam);
        if(costVO != null){
            logger.info("孤点计费：{}", JSON.toJSONString(costVO));
            bestCalculateCostAmountMap.put(costVO.getCalculateName(), costVO);
        }
        return bestCalculateCostAmountMap;
    }

    private void generateSystemFee(BmsPathParam bmsPathParam, BmsDeliveryQuoteCalculateCost cost) {
        List<BmsCalculationItem> items = bmsCalculationItemMapper.selectSystemItem();
        for (BmsCalculationItem item : items) {
            cost.setCalculateName(item.getQuoteName());
            cost.setFormula(item.getQuoteName());
            cost.setCalculateType(BmsCalculateTypeEnum.PATH.getCode());
            bmsDeliveryQuoteCalculateCostMapper.insert(cost);
            BmsDeliveryQuoteCalculateCostVO costVO = new BmsDeliveryQuoteCalculateCostVO();
            costVO.setAmount(BigDecimal.ZERO);
            costVO.setCalculateName(item.getQuoteName());
            costVO.setType(1);
            costVO.setSourceType(0);
            costVO.setId(cost.getId());
            bmsPathParam.getCalculateCostAmountsMap().put(item.getQuoteName(), costVO);
        }
    }

    private void matchingLoneSiteQuotation(Integer storeNo,List<BmsSiteDTO> loneSiteList,BmsPathParam bmsPathParam){
        if(CollectionUtils.isEmpty(loneSiteList) || Objects.isNull(storeNo)){
            logger.info("孤点站点为空");
            return;
        }
        logger.info("孤点站点：{}", JSON.toJSONString(loneSiteList));
        List<BmsDeliveryQuotationVO> bmsDeliveryQuotationVOS = queryQuoteBySiteList(storeNo, loneSiteList);
        if(CollectionUtils.isEmpty(bmsDeliveryQuotationVOS)){
            logger.info("孤点报价单为空");
            return;
        }

        HashSet<BmsSiteDTO> serviceAreaSet = loneSiteList.stream().filter(f->f.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())).collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BmsSiteDTO::getCity).thenComparing(BmsSiteDTO::getArea))), HashSet::new));

        //如果区域为空则设置为默认区
        serviceAreaSet.forEach(s->{
            if (StringUtils.isBlank(s.getArea())){
                s.setArea("默认区");
            }
        });

        List<BmsDeliveryQuotationVO> mateQuotationVO = Lists.newArrayList();
        for (BmsDeliveryQuotationVO quotationVO : bmsDeliveryQuotationVOS) {
            for (BmsSiteDTO bmsSiteDTO : serviceAreaSet) {
                if(bmsSiteDTO.getArea().equals(quotationVO.getDistrict())
                        && bmsSiteDTO.getCity().equals(quotationVO.getCity())){
                    mateQuotationVO.add(quotationVO);
                }
            }
        }
        logger.info("孤点匹配报价单：{}", JSON.toJSONString(mateQuotationVO));
        bmsPathParam.setLoneSiteMateQuotations(mateQuotationVO);
    }

    /**
     * 每条路线匹配报价单
     *  @param deliveryBatchDTO
     * @param wholeQuotations
     * @param deliveryDate
     * @param
     */
    private void matchingQuotation(DeliveryBatchDTO deliveryBatchDTO, List<BmsDeliveryQuotationVO> wholeQuotations, BmsPathParam bmsPathParam, LocalDate deliveryDate) {
        List<BmsSiteDTO> pathPointList = bmsPathParam.getPathPointList();
        //点位数
            int pointQuantity = (int) pathPointList.stream().filter(f->
                (Objects.nonNull(f.getSignInTime())
                && f.getSignInTime().toLocalDate().equals(deliveryDate))).count();

        HashSet<BmsSiteDTO> serviceAreaSet = pathPointList.stream().filter(f->f.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())).collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BmsSiteDTO::getCity).thenComparing(BmsSiteDTO::getArea))), HashSet::new));

        //如果区域为空则设置为默认区
        serviceAreaSet.forEach(s->{
            if (StringUtils.isBlank(s.getArea())){
                s.setArea("默认区");
            }
        });
        
        bmsPathParam.setServiceAreaSet(serviceAreaSet);
        //路线途径区县信息
        bmsPathParam.setPassingArea(serviceAreaSet.stream().map(SiteDTO::getArea).collect(Collectors.joining(Global.SEPARATING_SYMBOL)));

        List<BmsDeliveryQuotationVO> mateQuotationVO = Lists.newArrayList();
        for (BmsDeliveryQuotationVO quotationVO : wholeQuotations) {
            for (BmsSiteDTO bmsSiteDTO : serviceAreaSet) {
                if(bmsSiteDTO.getArea().equals(quotationVO.getDistrict())
                        && bmsSiteDTO.getCity().equals(quotationVO.getCity())){
                    mateQuotationVO.add(quotationVO);
                }
            }
        }
        bmsPathParam.setMateQuotations(mateQuotationVO);

        BmsDeliverySettleAccountsDetailVO bmsDeliverySettleAccountsDetailVO = new BmsDeliverySettleAccountsDetailVO();

        // 是否存在跨区 0否 1是
        bmsDeliverySettleAccountsDetailVO.setHaveSpannedArea(serviceAreaSet.size() > NumberUtils.INTEGER_ONE ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO);
        // 是否存在报价冲突 0否 1是
        bmsDeliverySettleAccountsDetailVO.setHaveQuotationConflict(CollectionUtils.isEmpty(bmsPathParam.getMateQuotations()) ? NumberUtils.INTEGER_ZERO : (bmsPathParam.getMateQuotations().size() > NumberUtils.INTEGER_ONE ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO));
        bmsPathParam.setAccountsDetailVO(bmsDeliverySettleAccountsDetailVO);
        // 计算结算项

        //2.公里数 行驶公里数 取预计公里数以实际公里数中最小值（在外面）

        //3. 线路数
        Integer pathQuantity = 1;

        //4. 商品数
        Integer waresQuantity = (int) deliveryBatchDTO.getSkuNum();

        //5. 打车点位数 取tms中配送方式为专车配送的点位数据
        Integer taxiPosition = (int) pathPointList.stream().filter(f->f.getSendWay().equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())).count();

        //5. 拦截点位数 取tms系统点位任务状态为拦截关闭的点位数据
        Integer interceptPosition = (int) pathPointList.stream().filter(f->Objects.equals(f.getInterceptState(),DeliverySiteInterceptStateEnum.closeIntecept.getCode())).count();

        //6. 帮采费
        BigDecimal purchaseAmount = deliveryBatchDTO.getBuyMoney();

        //7. 打车费
        BigDecimal trafficAmount = deliveryBatchDTO.getTaxiMoney();


        DefaultContext<String, Object> presetFieldMap = new DefaultContext<>();
        if (bmsPathParam.getNormalOrSpecial().equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())) {
            presetFieldMap.put(KM, bmsPathParam.getMinTotalDistance());
            //不跨区时 点位数剔除掉专车点位
            if(Objects.equals(NumberUtils.INTEGER_ZERO,bmsDeliverySettleAccountsDetailVO.getHaveQuotationConflict())){
                int num = (int) pathPointList.stream()
                        .filter(f-> Objects.equals(f.getSendWay(), DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()) && f.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode()) && (Objects.nonNull(f.getSignInTime()) && f.getSignInTime().toLocalDate().equals(deliveryDate)))
                        .count();
                presetFieldMap.put(POINT_QUANTITY, num);
            }else{
                presetFieldMap.put(POINT_QUANTITY, pointQuantity);
            }
            presetFieldMap.put(PATH_QUANTITY, pathQuantity);
            presetFieldMap.put(WARES_QUANTITY, waresQuantity);
        }else if (bmsPathParam.getNormalOrSpecial().equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())) {
            presetFieldMap.put(SPECIAL_KM, bmsPathParam.getMinTotalDistance());
            presetFieldMap.put(SPECIAL_POINT_QUANTITY, pointQuantity);
            presetFieldMap.put(SPECIAL_PATH_QUANTITY, pathQuantity);
            presetFieldMap.put(SPECIAL_WARES_QUANTITY, waresQuantity);
        }
        presetFieldMap.put(PURCHASE_AMOUNT, purchaseAmount);
        presetFieldMap.put(TRAFFIC_AMOUNT, trafficAmount);

        presetFieldMap.put(TAXI_POSITION, taxiPosition);
        presetFieldMap.put(INTERCEPT_POSITION, interceptPosition);
        presetFieldMap.put(PATH_KM, bmsPathParam.getMinTotalDistance());
        presetFieldMap.put(PATH_POINT_QUANTITY, pointQuantity);
        presetFieldMap.put(TOTAL_POINT_QUANTITY, pathPointList.size());

        BmsDeliveryQuoteCalculateCost cost = new BmsDeliveryQuoteCalculateCost();
        bmsPathParam.setPresetFieldMap(presetFieldMap);

        bmsPathParam.setCalculateCostAmountsMap(new HashMap<>());


        putSystemField(bmsPathParam.getCalculateCostAmountsMap(), cost, "帮采费", purchaseAmount);
        putSystemField(bmsPathParam.getCalculateCostAmountsMap(), cost, "打车费", trafficAmount);
        
    }

    /**
     * 生成明细单bms_settle_account
     * @param siteDTOList
     * @param wholeQuotations
     * @param tmsPathInfo
     * @param storeNo
     * @param deliveryDate
     * @return
     */
    private Integer saveBmsSettleAccount(List<BmsSiteDTO> siteDTOList, List<BmsDeliveryQuotationVO> wholeQuotations, List<DeliveryBatchDTO> tmsPathInfo, Integer storeNo, LocalDate deliveryDate) {
        BmsSettleAccount account = new BmsSettleAccount();
        // 取一份点位信息
        SiteDTO siteDTO = siteDTOList.get(NumberUtils.INTEGER_ZERO);
        if (wholeQuotations.size() > NumberUtils.INTEGER_ZERO) {
            BmsDeliveryQuotationVO quotationVO = wholeQuotations.get(NumberUtils.INTEGER_ZERO);
            account.setQuotationAreaId(quotationVO.getServiceAreaId());
            // 路线承运商取报价单上承运商
            account.setCarrierId(quotationVO.getCarrierId());
        } else {
            //默认取TMS的承运商和华东作为服务区域
            account.setCarrierId(tmsPathInfo.get(NumberUtils.INTEGER_ZERO).getCarrierId().intValue());
            account.setQuotationAreaId(4);
            account.setQuotationAreaName("华东");
        }
        // 创建结算单
        account.setDeliveryStartDate(deliveryDate);
        account.setDeliveryEndDate(deliveryDate);
        account.setStoreNo(storeNo);
        account.setProvince(siteDTO.getProvince());
        account.setCity(siteDTO.getCity());
        account.setBusinessType(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name());
        account.setBidderId(account.getCarrierId());
        account.setBidderName(Objects.nonNull(account.getCarrierId()) ? pmsCarrierQueryFacade.queryCarrierByIdInterrupt((long) account.getCarrierId()).getCarrierName() : null);
        account.setSettleTargetName(storeMap.get(storeNo));
        account.setSettleTargetId(storeNo);
        BmsDeliveryQuotationArea bmsDeliveryQuotationArea = bmsDeliveryQuotationAreaMapper.selectById(account.getQuotationAreaId());
        account.setQuotationAreaName(Objects.nonNull(bmsDeliveryQuotationArea) ? bmsDeliveryQuotationArea.getArea() : null);
        account.setSettleAccountNo(String.valueOf(SnowflakeUtil.nextId()));
        bmsSettleAccountMapper.insert(account);

        BmsQuotationProcess process = new BmsQuotationProcess();
        process.setSourceId(account.getId());
        process.setCreator(SYSTEM_NAME);
        process.setType(0);
        process.setOperationContent("生成明细单");
        bmsQuotationProcessMapper.insert(process);

        return account.getId();
    }

    /**
     *
     * @param tmsPathInfo tms路线信息
     * @param siteDTOList 所有点位信息
     * @param deliveryBatchMap
     * @param storeNo
     * @param prepareSiteMap
     */
    private List<BmsDeliveryQuotationVO>  matchQuotation(List<DeliveryBatchDTO> tmsPathInfo, List<BmsSiteDTO> siteDTOList, Map<String, List<BmsSiteDTO>> deliveryBatchMap, Integer storeNo, Map<Long, List<BmsSiteDTO>> prepareSiteMap) {
        List<BmsDeliveryQuotationVO> wholeQuotations = Lists.newArrayList();
        for (DeliveryBatchDTO deliveryBatchDTO : tmsPathInfo) {
            List<BmsSiteDTO> tempSiteDTOList = new ArrayList<>();
            for (DeliverySiteDTO deliverySiteDTO : deliveryBatchDTO.getDeliverySiteDTOList()){
                SiteDTO siteDTO = deliverySiteDTO.getSiteDTO();
                BmsSiteDTO bmsSiteDTO = ConvertUtils.convert(siteDTO, BmsSiteDTO.class);
                bmsSiteDTO.setStatus(deliverySiteDTO.getStatus());
                bmsSiteDTO.setInterceptState(deliverySiteDTO.getInterceptState());
                bmsSiteDTO.setSendWay(deliverySiteDTO.getSendWay());
                bmsSiteDTO.setSignInTime(deliverySiteDTO.getSignInTime());
                //前端页面选择不了没有区的市：如东莞，而saas订单存在东莞市常平镇的订单，这种时候需要处理为默认区，否则不计费
                Config noAreaCity = configMapper.selectOne("NO_AREA_CITY");
                if (StringUtils.isEmpty(siteDTO.getArea()) || noAreaCity.getValue().contains(bmsSiteDTO.getCity())){
                    bmsSiteDTO.setArea("默认区");
                }
                tempSiteDTOList.add(bmsSiteDTO);
            }
            siteDTOList.addAll(tempSiteDTOList);
            //获取正常配送且配送完成的点位集合
            List<BmsSiteDTO> normalSend = tempSiteDTOList.stream()
                    .filter(f -> Objects.equals(f.getSendWay(), DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()))
                    .collect(Collectors.toList());
            List<BmsSiteDTO> specialSendList = tempSiteDTOList.stream()
                    .filter(f -> Objects.equals(f.getSendWay(), DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(normalSend)) {
                if (!CollectionUtils.isEmpty(specialSendList)) {
                    normalSend = specialSendList;
                }
            }else {
                normalSend.addAll(specialSendList);
            }
            deliveryBatchMap.put(deliveryBatchDTO.getPathCode(), normalSend);
        }

        prepareSiteMap.putAll(siteDTOList.stream().collect(Collectors.groupingBy(BmsSiteDTO::getId)));

        //配送完成的点位集合
        List<BmsSiteDTO> filteredBmsSiteDTOList = siteDTOList.stream()
                .filter(f -> Objects.equals(f.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode()))
                .collect(Collectors.toList());

        //给无区域的点位设置默认区并放到addressMap中
        //数据结构为（”市+区“， ContactVO）
        Map<String, ContactVO> addressMap = new HashMap<>();
        for (SiteDTO siteDTO : filteredBmsSiteDTOList) {
            ContactVO contact = new ContactVO();
            contact.setArea(siteDTO.getArea());
            if (StringUtils.isEmpty(siteDTO.getArea())){
                contact.setArea("默认区");
            }
            contact.setCity(siteDTO.getCity());
            addressMap.putIfAbsent(siteDTO.getCity() + siteDTO.getArea(), contact);
        }

        Iterator<Map.Entry<String, ContactVO>> iterator = addressMap.entrySet().iterator();
        BmsDeliveryQuotationQuery query = new BmsDeliveryQuotationQuery();
        ///根据addressMap，查出对应区域所有的报价单
        while (iterator.hasNext()) {
            Map.Entry<String, ContactVO> entry = iterator.next();
            ContactVO contact = entry.getValue();
            query.setCity(contact.getCity());
            query.setStoreNo(storeNo);
            query.setDistrict(Objects.equals("默认区", contact.getArea()) ? null : contact.getArea());
            query.setStatus(0);
            List<BmsDeliveryQuotationVO> quotationVos = bmsDeliveryQuotationMapper.selectAllDistrict(query);
            // 无行政服务区县设置默认区
            quotationVos.forEach(quotation -> {
                if (StringUtils.isEmpty(quotation.getDistrict())) {
                    quotation.setDistrict("默认区");
                }
            });
            wholeQuotations.addAll(quotationVos);
        }

        return wholeQuotations;
    }

    /**
     * 组合计费计算逻辑
     * @param bmsPathParam 路线参数
     * @param prepareSiteMap key为city+area value为该区域所有点位的map
     */
    private void calculateRegular(BmsPathParam bmsPathParam, Map<Long, List<BmsSiteDTO>> prepareSiteMap) {
        //组合计费集合（累加), key为费用名称，value为费用值
        Map<String, BigDecimal> unionMap = Maps.newHashMap();
        //线路计费集合（取最小值）
        Map<String, BigDecimal> minMap = Maps.newHashMap();
        //计费详情对应的id map,key为费用名称，value为对应id
        Map<String, Integer> idMap = Maps.newHashMap();
        //线路计费公里集合
        Map<String, BigDecimal> minKmMap = Maps.newHashMap();
        ///线路计费点位集合
        Map<String, Integer> minPointMap = Maps.newHashMap();

        //首先算出哪些区域是按照线路计费的，择优报价的区域的计费项必定是线路计费的，组合报价的计费项有可能存在线路计费的计费项
        if (!CollectionUtils.isEmpty(bmsPathParam.getMateQuotations())) {
            Map<String, Set<String>> minCalculateMap = generateMinCalculateMap(bmsPathParam.getMateQuotations());
            logger.info("根据线路计费的区域计算对应的公里数和点位数Map:{}", JSON.toJSONString(minCalculateMap));
            generateMinKmAndPointMap(minCalculateMap, minKmMap, minPointMap, bmsPathParam, prepareSiteMap);

        }
        //key为计费名称+报价单id，处理区域计费情况
        //一个报价单对应3个区，系统处理为3个报价单，除了区域不同其他数据完全相同，导致区域计费时，如果匹配了2个区，计费重复，构建一个计算名+报价单id的map
        for (BmsDeliveryQuotationVO pathQuotation : bmsPathParam.getMateQuotations()) {
            DefaultContext<String, Object> temporaryFieldMap = new DefaultContext<>();
            List<BmsDeliveryQuotationDetailVO> quotationDetailVos = bmsDeliveryQuotationDetailMapper.selectByQuotationId(pathQuotation.getId());
            quotationDetailVos.forEach(detail -> temporaryFieldMap.put(detail.getQuoteName(), detail.getAmount()));
            // 报价单计费模型
            List<BmsDeliveryQuoteCalculateCostVO> calculateCosts = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(pathQuotation.getId());
            //计费模型根据计费逻辑0线路计费，1区域计费 分组
            Map<Integer, List<BmsDeliveryQuoteCalculateCostVO>> calculateMap = calculateCosts.stream().collect(Collectors.groupingBy(BmsDeliveryQuoteCalculateCostVO::getCalculateType));
            //择优取最低，放入minMap
            List<BmsDeliveryQuoteCalculateCostVO> pathCalculate = calculateMap.get(BmsCalculateTypeEnum.PATH.getCode());
            if (!CollectionUtils.isEmpty(pathCalculate)) {
                calculatePathType(pathCalculate, temporaryFieldMap, bmsPathParam, minKmMap, minPointMap, minMap, idMap);
            }
            //区域计费逻辑
            List<BmsDeliveryQuoteCalculateCostVO> areaCalculateCost = calculateMap.get(BmsCalculateTypeEnum.AREA.getCode());
            if (!CollectionUtils.isEmpty(areaCalculateCost)) {
                calculateAreaType(areaCalculateCost, temporaryFieldMap, bmsPathParam, pathQuotation, prepareSiteMap, unionMap, idMap);
            }
        }

        //以unionMap为主Map，将相同费用名称的结果聚合
        logger.info("组合计费计算逻辑，区域计费组合Map：{}",JSON.toJSONString(unionMap));
        logger.info("组合计费计算逻辑，线路计费组合Map：{}",JSON.toJSONString(minMap));
        unionMap.forEach((key, value) ->{
            if (Objects.nonNull(minMap.get(key))){
                BigDecimal newValue = value.add(minMap.get(key));
                unionMap.put(key, newValue);
                minMap.remove(key);
            }
            bmsPathParam.getCalculateCostAmountsMap().put(key, new BmsDeliveryQuoteCalculateCostVO(idMap.get(key), unionMap.get(key), key, 1, 0));
        });
        minMap.forEach((key, value) -> bmsPathParam.getCalculateCostAmountsMap().put(key, new BmsDeliveryQuoteCalculateCostVO(idMap.get(key), value, key, 1, 0)));
        //孤点计费
        BmsDeliveryQuoteCalculateCostVO costVO = calculateLoneSite(bmsPathParam);
        if(costVO != null){
            logger.info("孤点计费：{}", JSON.toJSONString(costVO));
            bmsPathParam.getCalculateCostAmountsMap().put(costVO.getCalculateName(), costVO);
        }
    }

    /**
     * 孤点计费，命中多个报价单时取最低费用
     * @param bmsPathParam
     * @return
     */
    private BmsDeliveryQuoteCalculateCostVO calculateLoneSite(BmsPathParam bmsPathParam) {
        BigDecimal loneSiteAmount = null;
        BmsDeliveryQuoteCalculateCostVO result = null;
        //计算孤点费
        if(bmsPathParam.isLoneSitePath() && CollUtil.isNotEmpty(bmsPathParam.getLoneSiteMateQuotations())){
            for (BmsDeliveryQuotationVO pathQuotation : bmsPathParam.getLoneSiteMateQuotations()) {
                //处理前置参数
                DefaultContext<String, Object> calculationFieldMap = new DefaultContext<>();
                List<BmsDeliveryQuotationDetailVO> quotationDetailVos = bmsDeliveryQuotationDetailMapper.selectByQuotationId(pathQuotation.getId());
                quotationDetailVos.forEach(detail -> calculationFieldMap.put(detail.getQuoteName(), detail.getAmount()));
                calculationFieldMap.putAll(bmsPathParam.getPresetFieldMap());
                calculationFieldMap.put(Global.LONE_SITE_KM,bmsPathParam.getLoneSiteTotalDistance());
                calculationFieldMap.put(Global.LONE_SITE_QUANTITY,bmsPathParam.getLoneSiteTotalQuantity());

                List<BmsDeliveryQuoteCalculateCostVO> bmsDeliveryQuoteCalculateCostVOS = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(pathQuotation.getId());
                for (BmsDeliveryQuoteCalculateCostVO calculateCost : bmsDeliveryQuoteCalculateCostVOS) {
                    logger.info("计算孤点计费逻辑，计算模型：{} ，参数:{}", JSON.toJSONString(calculateCost),calculationFieldMap);
                    if(Objects.equals(calculateCost.getCalculateName(), LONE_SITE_FEE_UNIT)){
                        //不支持区间，开始计算孤点费 取价格最低的孤点费
                        BigDecimal amount = expressRunnerService.calculation(calculateCost.getFormula(), calculationFieldMap);
                        if(loneSiteAmount == null || amount.compareTo(loneSiteAmount) < 0){
                            loneSiteAmount = amount;
                            result = new BmsDeliveryQuoteCalculateCostVO(calculateCost.getId(), amount, calculateCost.getCalculateName(), 1, 0);
                        }
                    }
                }
            }
            logger.info("计算孤点计费逻辑，计算结果：{}", JSON.toJSONString(result));
        }
        return result;
    }

    private void calculateAreaType(List<BmsDeliveryQuoteCalculateCostVO> areaCalculateCost, DefaultContext<String, Object> temporaryFieldMap, BmsPathParam bmsPathParam, BmsDeliveryQuotationVO pathQuotation, Map<Long, List<BmsSiteDTO>> prepareSiteMap, Map<String, BigDecimal> unionMap, Map<String, Integer> idMap) {
        for (BmsDeliveryQuoteCalculateCostVO calculateCost : areaCalculateCost) {
            BigDecimal amount;
            DefaultContext<String, Object> calculationFieldMap = new DefaultContext<>();
            calculationFieldMap.putAll(temporaryFieldMap);
            calculationFieldMap.putAll(bmsPathParam.getPresetFieldMap());

            String formula = matchingRange(calculateCost, bmsPathParam);

            if (bmsPathParam.getNormalOrSpecial().equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())){
                //找到这个报价单包含的区域内有多少km
                String cityAreaKey = bmsPathParam.getMetaMap().get(pathQuotation);
                BigDecimal areaDistance = countAreaDistance(cityAreaKey, bmsPathParam.getSectionDTOList(), prepareSiteMap);
                BigDecimal spannedAreaDistance = countSpannedAreaDistance(cityAreaKey, bmsPathParam.getSpannedAreaMap(), bmsPathParam.getReverseMetaMap(), prepareSiteMap);
                BigDecimal totalDistance = areaDistance.add(spannedAreaDistance);
                calculationFieldMap.put(KM, totalDistance);
                //找到这个报价单包含的区域内有多少个点位
                Integer pointQuantity = countAreaPoint(cityAreaKey, bmsPathParam.getSiteAreaMap());
                if(Objects.equals(pointQuantity,0)){
                    logger.info("正常配送，区域计费，地区:{}, 点位数:{}", cityAreaKey, pointQuantity);
                    continue;
                }
                calculationFieldMap.put(POINT_QUANTITY, new BigDecimal(String.valueOf(pointQuantity)));
                amount = expressRunnerService.calculation(formula, calculationFieldMap);
                logger.info("正常配送，区域计费，地区:{},区域内公里数:{},跨区公里数:{}, 总公里数:{}, 费用名称:{} ,费用:{}", cityAreaKey, areaDistance, spannedAreaDistance, totalDistance,calculateCost.getCalculateName(), amount);
                logger.info("正常配送，区域计费，地区:{}, 点位数:{}, 费用名称:{} ,费用:{}", cityAreaKey, pointQuantity, calculateCost.getCalculateName() ,amount);
            }
            else if (bmsPathParam.getNormalOrSpecial().equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())) {
                //找到这个报价单包含的区域内有多少km
                String cityAreaKey = bmsPathParam.getMetaMap().get(pathQuotation);
                BigDecimal areaDistance = countAreaDistance(cityAreaKey, bmsPathParam.getSectionDTOList(), prepareSiteMap);
                BigDecimal spannedAreaDistance = countSpannedAreaDistance(cityAreaKey, bmsPathParam.getSpannedAreaMap(), bmsPathParam.getReverseMetaMap(), prepareSiteMap);
                BigDecimal totalDistance = areaDistance.add(spannedAreaDistance);
                calculationFieldMap.put(SPECIAL_KM, totalDistance);
                //找到这个报价单包含的区域内有多少个点位
                Integer pointQuantity = countAreaPoint(cityAreaKey, bmsPathParam.getSiteAreaMap());
                calculationFieldMap.put(SPECIAL_POINT_QUANTITY, new BigDecimal(String.valueOf(pointQuantity)));
                amount = expressRunnerService.calculation(formula, calculationFieldMap);
                logger.info("专车配送，区域计费, 区域名称：{}， 费用名称:{} ,费用:{}， 专车点位数:{}，专车公里数:{}，专车商品数:{}", cityAreaKey, calculateCost.getCalculateName(), amount, calculationFieldMap.get(SPECIAL_POINT_QUANTITY), calculationFieldMap.get(SPECIAL_KM),calculationFieldMap.get(SPECIAL_WARES_QUANTITY));

            }
            else {
                amount = expressRunnerService.calculation(formula, calculationFieldMap);
            }
            BigDecimal oldAmount = unionMap.get(calculateCost.getCalculateName());
            //如果存在同名区域报价的，累加
            if (Objects.nonNull(oldAmount)) {
                BigDecimal newAmount = amount.add(oldAmount);
                unionMap.put(calculateCost.getCalculateName(), newAmount);
                idMap.put(calculateCost.getCalculateName(), calculateCost.getId());
            } else {
                //map中不存在则放入区域map
                unionMap.put(calculateCost.getCalculateName(), amount);
                idMap.put(calculateCost.getCalculateName(), calculateCost.getId());
            }
        }
    }

    /**
     * 根据报价单查出对应计费项和公式计算最低的费用，并放入minMap中
     * @param pathCalculate 线路计费集合
     * @param temporaryFieldMap 临时费用项集合
     * @param bmsPathParam 线路参数
     * @param minKmMap 线路计费公里数map
     * @param minPointMap 线路计费点位数map
     * @param minMap 线路计费集合
     * @param idMap 用于匹配线路计费和区域计费同名费用名称累加
     */
    private void calculatePathType(List<BmsDeliveryQuoteCalculateCostVO> pathCalculate, DefaultContext<String, Object> temporaryFieldMap, BmsPathParam bmsPathParam, Map<String, BigDecimal> minKmMap, Map<String, Integer> minPointMap, Map<String, BigDecimal> minMap, Map<String, Integer> idMap) {
        for (BmsDeliveryQuoteCalculateCostVO calculateCost : pathCalculate) {
            BigDecimal amount;
            DefaultContext<String, Object> calculationFieldMap = new DefaultContext<>();
            calculationFieldMap.putAll(temporaryFieldMap);
            calculationFieldMap.putAll(bmsPathParam.getPresetFieldMap());

            if (bmsPathParam.getNormalOrSpecial().equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())){
                //线路计费公里数累加，然后取计费模型，不分区
                //找到这个报价单包含的区域内有多少个点位
                Integer pointQuantity = minPointMap.get(calculateCost.getCalculateName());
                if (Objects.nonNull(pointQuantity)) {
                    calculationFieldMap.put(POINT_QUANTITY, pointQuantity);
                }
                BigDecimal km = minKmMap.get(calculateCost.getCalculateName());
                if (Objects.nonNull(km)) {
                    calculationFieldMap.put(KM, km);
                }
                String formula = matchingRange(calculateCost, bmsPathParam);
                amount = expressRunnerService.calculation(formula, calculationFieldMap);
                logger.info("线路计费, 费用名称:{} ,费用:{}", calculateCost.getCalculateName(), amount);

            }else if (bmsPathParam.getNormalOrSpecial().equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())){
                String formula = matchingRange(calculateCost, bmsPathParam);
                //找到这个报价单包含的区域内有多少个点位
                if (Objects.nonNull(minPointMap.get(calculateCost.getCalculateName()))) {
                    calculationFieldMap.put(SPECIAL_POINT_QUANTITY, minPointMap.get(calculateCost.getCalculateName()));
                    amount = expressRunnerService.calculation(formula, calculationFieldMap);
                    logger.info("线路计费, 费用名称:{} ,费用:{}", calculateCost.getCalculateName(), amount);
                }
                else if (Objects.nonNull(minKmMap.get(calculateCost.getCalculateName()))) {
                    calculationFieldMap.put(SPECIAL_KM, minKmMap.get(calculateCost.getCalculateName()));
                    amount = expressRunnerService.calculation(formula, calculationFieldMap);
                    logger.info("线路计费, 费用名称:{} ,费用:{}", calculateCost.getCalculateName(), amount);
                }
                else {
                    amount = expressRunnerService.calculation(formula, calculationFieldMap);
                }

            }else{
                String formula = matchingRange(calculateCost, bmsPathParam);
                amount = expressRunnerService.calculation(formula, calculationFieldMap);
            }
            BigDecimal oldAmount = minMap.get(calculateCost.getCalculateName());
            //如果新的线路报价的值比旧的同名数值少，替换
            if (Objects.nonNull(oldAmount)) {
                if (amount.compareTo(oldAmount) < 0){
                    minMap.put(calculateCost.getCalculateName(), amount);
                    idMap.put(calculateCost.getCalculateName(), calculateCost.getId());
                }
            } else {
                //map中不存在则放入线路map
                minMap.put(calculateCost.getCalculateName(), amount);
                idMap.put(calculateCost.getCalculateName(), calculateCost.getId());
            }
        }
    }

    /**
     * 将值和区间进行匹配，返回对应区间的公式
     * @param calculateCost 计费模型
     * @param bmsPathParam 预置集合
     * @return 公式
     */
    private String matchingRange(BmsDeliveryQuoteCalculateCostVO calculateCost,BmsPathParam bmsPathParam) {
        //若为正常配送，则不计算专车公里数
        if (Objects.equals(bmsPathParam.getNormalOrSpecial(),DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()) && Objects.equals(calculateCost.getCalculateName(), SPECIAL_KM_FEE)){
            return null;
        }
        //专车配送情况下，不计算点位费，公里费，超点费，超公里费
        Config specialOnly = configMapper.selectOne("SPECIAL_ONLY");
        if (Objects.equals(bmsPathParam.getNormalOrSpecial(),DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue()) && (specialOnly.getValue().contains(calculateCost.getCalculateName()))){
            return null;
        }
        //根据值查询对应公式，若存在区间计费,取区间计费的公式进行计算，否则按照原逻辑计算
        List<BmsDeliveryQuotaCalculateCostRange> bmsDeliveryQuotaCalculateCostRanges = bmsDeliveryQuotaCalculateCostRangeMapper.selectByQuoteCalculateCostId(calculateCost.getId());
        //定义公式，不同条件取不同的值,若存在区间但值未匹配任何区间，则公式为null，计算结果为0
        String formula = null;
        BigDecimal value = BigDecimal.ZERO;
        //若存在区间计费，匹配数值对应区间
        if (!CollectionUtils.isEmpty(bmsDeliveryQuotaCalculateCostRanges)) {
            //匹配到对应区间，按照区间公式进行计算
            for (BmsDeliveryQuotaCalculateCostRange bmsDeliveryQuotaCalculateCostRange : bmsDeliveryQuotaCalculateCostRanges) {
                if (bmsDeliveryQuotaCalculateCostRange.getQuoteCalculateCostName().contains(PATH_KM)) {
                    value = new BigDecimal(bmsPathParam.getPresetFieldMap().get(PATH_KM).toString());
                } else if (bmsDeliveryQuotaCalculateCostRange.getQuoteCalculateCostName().contains(PATH_POINT_QUANTITY)) {
                    value = new BigDecimal(bmsPathParam.getPresetFieldMap().get(PATH_POINT_QUANTITY).toString());
                }
                //如果 下线<=值<上限，匹配到对应区间，将其公式返回
                if (bmsDeliveryQuotaCalculateCostRange.getLowerLimit().compareTo(value) <= 0 && bmsDeliveryQuotaCalculateCostRange.getUpperLimit().compareTo(value) > 0) {
                    formula = bmsDeliveryQuotaCalculateCostRange.getFormula();
                    break;
                }
            }
            //未匹配到区间，计算结果为0
        }else {
            //计算线路计费公里数总费用
            formula = calculateCost.getFormula();
        }

        return formula;
    }

    /**
     * 根据费用名称将其放入线路计费公里map和线路计费点位map
     * @param minCalculateMap 线路计费map
     * @param minKmMap
     * @param minPointMap
     * @param bmsPathParam
     * @param prepareSiteMap
     */
    private void generateMinKmAndPointMap(Map<String, Set<String>> minCalculateMap, Map<String, BigDecimal> minKmMap, Map<String, Integer> minPointMap, BmsPathParam bmsPathParam,  Map<Long, List<BmsSiteDTO>> prepareSiteMap) {
        //根据线路计费的区域计算对应的公里数和点位数
        minCalculateMap.forEach((calculateName,areaList)->{
            if (!CollectionUtils.isEmpty(areaList)){
                for (String area : areaList){
                    if (calculateName.contains("公里") || calculateName.contains("超公里费")) {
                        BigDecimal areaDistance = countAreaDistance(area, bmsPathParam.getSectionDTOList(), prepareSiteMap);
                        BigDecimal spannedAreaDistance = countSpannedAreaDistance(area, bmsPathParam.getSpannedAreaMap(), bmsPathParam.getReverseMetaMap(), prepareSiteMap);
                        BigDecimal total = areaDistance.add(spannedAreaDistance);
                        logger.info("线路计费，地区:{},费用名称:{},区域内公里数:{}，跨区公里数:{}, 区域总公里数：{}", area, calculateName, areaDistance, spannedAreaDistance, total);
                        if (Objects.isNull(minKmMap.get(calculateName))) {
                            minKmMap.put(calculateName, total);
                        } else {
                            BigDecimal newDistance = total.add(minKmMap.get(calculateName));
                            minKmMap.put(calculateName, newDistance);
                        }
                    }
                    if (calculateName.contains("点位") || calculateName.contains("超点费")) {
                        Integer pointQuantity = countAreaPoint(area, bmsPathParam.getSiteAreaMap());
                        logger.info("线路计费，地区:{},费用名称:{},对应点位数:{}", area, calculateName, pointQuantity);
                        //根据报价单报价区域找到对应的点位数量，累加
                        if (Objects.isNull(minPointMap.get(calculateName))) {
                            minPointMap.put(calculateName, pointQuantity);
                        } else {
                            Integer newPoint = pointQuantity + minPointMap.get(calculateName);
                            minPointMap.put(calculateName, newPoint);
                        }
                    }
                }
            }
        });
    }

    /**
     * 构造线路计费集合，现把择优计费的所有费用项对应的区域放进去，再把组合计费中，线路计费的费用项放进去
     * @param mateQuotations 匹配的报价单
     * @return
     */
    private Map<String, Set<String>> generateMinCalculateMap(List<BmsDeliveryQuotationVO> mateQuotations) {
        //线路计费的区域<key:费用名称，value：对应的区域列表
        Map<String, Set<String>> minCalculateMap = Maps.newHashMap();
        //报价单不能跨市，取一份报价单数据,但是配送路线可以随心所欲
        Map<Integer, List<BmsDeliveryQuotationVO>> quotationMap = mateQuotations.stream().collect(Collectors.groupingBy(BmsDeliveryQuotationVO::getQuotaForm));
        List<BmsDeliveryQuotationVO> minQuotations = quotationMap.get(BmsQuotaFormEnum.MINIMUM.ordinal());


        if (!CollectionUtils.isEmpty(minQuotations)){
            for (BmsDeliveryQuotationVO minQuotationVO : minQuotations){
                List<BmsDeliveryQuoteCalculateCostVO> calculateCosts = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(minQuotationVO.getId());
                for (BmsDeliveryQuoteCalculateCostVO calculateCost : calculateCosts) {
                    Set<String> areaSet = minCalculateMap.get(calculateCost.getCalculateName());
                    if (!CollectionUtils.isEmpty(areaSet)){
                        StringBuilder stringBuilder = new StringBuilder();
                        areaSet.add(String.valueOf(stringBuilder.append(minQuotationVO.getCity()).append(CROSS_BAR).append(minQuotationVO.getDistrict())));
                        minCalculateMap.put(calculateCost.getCalculateName(), areaSet);
                    }else {
                        Set<String> init = new HashSet<>();
                        StringBuilder stringBuilder = new StringBuilder();
                        init.add(String.valueOf(stringBuilder.append(minQuotationVO.getCity()).append(CROSS_BAR).append(minQuotationVO.getDistrict())));
                        minCalculateMap.put(calculateCost.getCalculateName(), init);
                    }
                }
            }
        }
        List<BmsDeliveryQuotationVO> combinedQuotations = quotationMap.get(BmsQuotaFormEnum.COMBINED.ordinal());
        if (!CollectionUtils.isEmpty(combinedQuotations)) {
            for (BmsDeliveryQuotationVO combinedQuotation : combinedQuotations) {
                // 报价单计费模型
                List<BmsDeliveryQuoteCalculateCostVO> calculateCosts = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(combinedQuotation.getId()).stream().filter(f -> f.getCalculateType().equals(BmsCalculateTypeEnum.PATH.getCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(calculateCosts)) {
                    for (BmsDeliveryQuoteCalculateCostVO calculateCost : calculateCosts) {
                        Set<String> areaSet = minCalculateMap.get(calculateCost.getCalculateName());
                        if (!CollectionUtils.isEmpty(areaSet)) {
                            StringBuilder stringBuilder = new StringBuilder();
                            areaSet.add(String.valueOf(stringBuilder.append(combinedQuotation.getCity()).append(CROSS_BAR).append(combinedQuotation.getDistrict())));
                            minCalculateMap.put(calculateCost.getCalculateName(), areaSet);
                        } else {
                            Set<String> init = new HashSet<>();
                            StringBuilder stringBuilder = new StringBuilder();
                            init.add(String.valueOf(stringBuilder.append(combinedQuotation.getCity()).append(CROSS_BAR).append(combinedQuotation.getDistrict())));
                            minCalculateMap.put(calculateCost.getCalculateName(), init);
                        }
                    }
                }
            }
        }
        return minCalculateMap;
    }

    private Integer countAreaPoint(String cityAreaKey, Map<String, List<BmsSiteDTO>> siteAreaMap) {
        List<BmsSiteDTO> siteDTOList = siteAreaMap.get(cityAreaKey);
        return CollectionUtils.isEmpty(siteDTOList) ? 0 : siteDTOList.size();
    }

    /**
     * 区域内部距离计算，若出现拦截，例如A-B-C B点位拦截，且系统公里数小的情况下，则A-B的距离折半计算到A区域，B-C的距离折半计算到C区域
     * @param cityAreaKey 城市+区域
     * @param sectionDTOList 路段信息
     * @param prepareSiteMap 预先加工好的点位信息集合
     * @return
     */
    private BigDecimal countAreaDistance(String cityAreaKey, List<DeliverySectionDTO> sectionDTOList, Map<Long, List<BmsSiteDTO>> prepareSiteMap) {
        if (BooleanUtil.isTrue(dynamicConfig.getBmsSettleCostSwitch())) {
            BigDecimal distance = BigDecimal.ZERO;
            for (DeliverySectionDTO deliverySectionDTO : sectionDTOList) {
                SiteDTO beginSite = deliverySectionDTO.getBeginSite();
                String beginKey = beginSite.getCity() + "-" + beginSite.getArea();
                String endKey = deliverySectionDTO.getEndSite().getCity() + "-" + deliverySectionDTO.getEndSite().getArea();
                if (beginKey.equals(endKey) && beginKey.equals(cityAreaKey)){
                    BmsSiteDTO endSiteInterceptInfo = prepareSiteMap.get(deliverySectionDTO.getEndSite().getId()).get(NumberUtils.INTEGER_ZERO);
                    boolean endInterceptStatus = endSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
                    if(endInterceptStatus){
                        logger.info("未跨区:cityAreaKey{}, 后点未拦截 ，距离:{}", cityAreaKey,deliverySectionDTO.getDistance());
                        distance = distance.add(deliverySectionDTO.getDistance());
                    }
                }
            }
            //实际公里数（转为公里数）
            return distance.divide(new BigDecimal(1000)).setScale(2, RoundingMode.HALF_UP);
        }else{
            BigDecimal distance = BigDecimal.ZERO;
            for (DeliverySectionDTO deliverySectionDTO : sectionDTOList) {
                SiteDTO beginSite = deliverySectionDTO.getBeginSite();
                String beginKey = beginSite.getCity() + "-" + beginSite.getArea();
                String endKey = deliverySectionDTO.getEndSite().getCity() + "-" + deliverySectionDTO.getEndSite().getArea();
                boolean beginInterceptStatus = false;
                if (beginKey.equals(endKey) && beginKey.equals(cityAreaKey)){
                    //如果起始点位是城配仓，拦截信息默认为true
                    if (beginSite.getType().equals(TmsSiteTypeEnum.STORE.getCode())){
                        beginInterceptStatus = true;
                    }else {
                        BmsSiteDTO beginSiteInterceptInfo = prepareSiteMap.get(beginSite.getId()).get(NumberUtils.INTEGER_ZERO);
                        beginInterceptStatus = beginSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
                    }
                    BmsSiteDTO endSiteInterceptInfo = prepareSiteMap.get(deliverySectionDTO.getEndSite().getId()).get(NumberUtils.INTEGER_ZERO);
                    //起点或者终点不是配送完成的状态beginInterceptStatus = beginSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
                    boolean endInterceptStatus = endSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
                    //开始结束点位有且只有一个非配送完成，距离只算一半
                    if ((!beginInterceptStatus && endInterceptStatus) || (beginInterceptStatus && !endInterceptStatus)){
                        distance = distance.add(deliverySectionDTO.getDistance().divide(new BigDecimal(2), 2 , RoundingMode.HALF_UP));
                    }else if (!beginInterceptStatus){
                        //都拦截了，不算距离
                    } else {
                        //都配送完成，都算
                        distance = distance.add(deliverySectionDTO.getDistance());
                    }
                }
            }
            //实际公里数（转为公里数）
            return distance.divide(new BigDecimal(1000)).setScale(2, RoundingMode.HALF_UP);
        }

    }

    /**
     * 计算跨区部分距离
     * @param cityAreaKey
     * @param spannedAreaMap
     * @param reverseMetaMap
     * @param prepareSiteMap
     * @return
     */
    private BigDecimal countSpannedAreaDistance(String cityAreaKey, Map<DeliverySectionDTO, BigDecimal> spannedAreaMap, Map<String, BmsDeliveryQuotationVO> reverseMetaMap, Map<Long, List<BmsSiteDTO>> prepareSiteMap) {
        if(BooleanUtil.isTrue(dynamicConfig.getBmsSettleCostSwitch())){
            List<BigDecimal>  spannedAreaDistance = Lists.newArrayList();
            spannedAreaMap.forEach((k,distance)->{
                // 找到section涉及的两个方案以及线路计费模型
                if (StringUtils.isBlank(k.getBeginSite().getArea())){
                    k.getBeginSite().setArea("默认区");
                }
                if (StringUtils.isBlank(k.getEndSite().getArea())){
                    k.getEndSite().setArea("默认区");
                }
                String endKey = k.getEndSite().getCity()+ "-" + k.getEndSite().getArea();
                BmsDeliveryQuotationVO endQuotation = reverseMetaMap.get(endKey);
                BmsSiteDTO endSiteInterceptInfo = prepareSiteMap.get(k.getEndSite().getId()).get(NumberUtils.INTEGER_ZERO);
                boolean endInterceptStatus = endSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
                if(endQuotation!=null && endInterceptStatus){
                    if(endKey.equals(cityAreaKey)){
                        BigDecimal divide = distance.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
                        logger.info("结束点位配送完成{}，距离算到后面那个区域{}",endKey,divide );
                        spannedAreaDistance.add(divide);
                    }
                }
            });
            //实际公里数（转为公里数）
            logger.info("地区：{}，跨区部分公里数计算，距离列表：{}，总公里数:{}", cityAreaKey, spannedAreaDistance, CollectionUtils.isEmpty(spannedAreaDistance)? BigDecimal.ZERO : spannedAreaDistance.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            return CollectionUtils.isEmpty(spannedAreaDistance)? BigDecimal.ZERO : spannedAreaDistance.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        }else{
            List<BigDecimal>  spannedAreaDistance = Lists.newArrayList();
            spannedAreaMap.forEach((k,distance)->{
                // 找到section涉及的两个方案以及线路计费模型
                if (StringUtils.isBlank(k.getBeginSite().getArea())){
                    k.getBeginSite().setArea("默认区");
                }
                if (StringUtils.isBlank(k.getEndSite().getArea())){
                    k.getEndSite().setArea("默认区");
                }
                String beginKey = k.getBeginSite().getCity()+ "-" + k.getBeginSite().getArea();
                String endKey = k.getEndSite().getCity()+ "-" + k.getEndSite().getArea();
                BmsDeliveryQuotationVO beginQuotation = reverseMetaMap.get(beginKey);
                BmsDeliveryQuotationVO endQuotation = reverseMetaMap.get(endKey);
                //起始点位是城配仓
                boolean isStore = k.getBeginSite().getType().equals(TmsSiteTypeEnum.STORE.getCode());
                BmsSiteDTO endSiteInterceptInfo = prepareSiteMap.get(k.getEndSite().getId()).get(NumberUtils.INTEGER_ZERO);
                //起点是城配仓，第一个配送点位未拦截且有报价，距离不减半
                if(endQuotation != null && endKey.equals(cityAreaKey) && isStore && endSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())){
                    spannedAreaDistance.add(distance.divide(new BigDecimal(1000), 2 , RoundingMode.HALF_UP));
                }
                //第一个点位就拦截关闭，距离减半
                if (endQuotation != null && endKey.equals(cityAreaKey) && isStore && !endSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())){
                    spannedAreaDistance.add(distance.divide(new BigDecimal(2000), 2 , RoundingMode.HALF_UP));
                }
                if((beginQuotation != null || endQuotation != null) && (beginKey.equals(cityAreaKey) || endKey.equals(cityAreaKey)) && !isStore){
                    //点位拦截信息,因为根据id分组所以肯定只有一个
                    BmsSiteDTO beginSiteInterceptInfo = prepareSiteMap.get(k.getBeginSite().getId()).get(NumberUtils.INTEGER_ZERO);
                    //起点或者终点不是配送完成的状态
                    boolean beginInterceptStatus = beginSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
                    boolean endInterceptStatus = endSiteInterceptInfo.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
                    //起始点位未配送完成，结束点位配送完成，距离只算一半，只算到前面那个区域
                    if (!beginInterceptStatus && endInterceptStatus && endKey.equals(cityAreaKey)){
                        spannedAreaDistance.add(distance.divide(new BigDecimal(2000), 2 , RoundingMode.HALF_UP));
                    }
                    //起始点位配送完成，结束点位未配送完成，距离只算一半，只算到后面那个区域
                    else if (beginInterceptStatus && !endInterceptStatus && beginKey.equals(cityAreaKey)){
                        spannedAreaDistance.add(distance.divide(new BigDecimal(2000), 2 , RoundingMode.HALF_UP));
                    }
                    else if (!beginInterceptStatus){
                        //都拦截了，不算距离
                    } else {
                        //都配送完成，都算
                        spannedAreaDistance.add(distance.divide(new BigDecimal(2000), 2 , RoundingMode.HALF_UP));
                    }
                }
            });
            //实际公里数（转为公里数）
            logger.info("地区：{}，跨区部分公里数计算，距离列表：{}，总公里数:{}", cityAreaKey, spannedAreaDistance, CollectionUtils.isEmpty(spannedAreaDistance)? BigDecimal.ZERO : spannedAreaDistance.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            return CollectionUtils.isEmpty(spannedAreaDistance)? BigDecimal.ZERO : spannedAreaDistance.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    @Override
    public void manualSettleAccountSingle(Integer storeNo, LocalDate date) {

        if (Objects.isNull(storeNo)) {
            // 查询所有城配仓
            List<WarehouseLogisticsCenterVO> storeCenter = warehouseLogisticsCenterMapper.selectAllCenterVO(new WarehouseLogisticsCenter());
            for (WarehouseLogisticsCenterVO center : storeCenter) {
                Integer implementStoreNo = center.getStoreNo();
                try {
                    selfService.settleAccountSingle(implementStoreNo, date);
                } catch (Exception e) {
                    logger.error( "城配仓：{},结算明细单生成失败:{},",storeMap.get(storeNo), e.getMessage(), e);
                }
            }
        }
        else {
            selfService.settleAccountSingle(storeNo, date);
        }

    }

    @Override
    public void exportCostAdjustmentTemplate(BmsSettleAccountQuery param) throws IOException {

        // 处理参数
        handleParam(param);

        List<BmsDeliverySettleAccountsDetailVO> detailVos = bmsDeliverySettleAccountsDetailMapper.selectByCondition(param);

        if (CollectionUtils.isEmpty(detailVos)) {
            throw new ParamsException("模板导出失败,无费用详情信息");
        }

        // 获取所有费用详情字段
        Set<String> calculateNames = getCalculateNames(detailVos);

        Workbook workbook = new HSSFWorkbook();
        //设置全局默认文本格式
        CellStyle bookStyle = workbook.createCellStyle();
        bookStyle.setAlignment(HorizontalAlignment.CENTER);
        bookStyle.setDataFormat(workbook.createDataFormat().getFormat("@"));
        //sheet名称
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);

        // 封装字段名
        packageSystemField(title, calculateNames);

        int rowIndex = 1;
        for (BmsDeliverySettleAccountsDetailVO detailVo : detailVos) {
            Row row = sheet.createRow(rowIndex);
            row.createCell(0).setCellValue(detailVo.getDeliveryDate().toString());
            row.createCell(1).setCellValue(detailVo.getPath());
            rowIndex++;
        }

        BmsDeliverySettleAccountsDetailVO detailVO = detailVos.get(NumberUtils.INTEGER_ZERO);
        String carrierName = "";
        if (Objects.nonNull(detailVO.getCarrierId())) {
            Carrier carrier = carrierMapper.selectByPrimaryKey(Long.valueOf(detailVO.getCarrierId()));
            carrierName = carrier.getCarrierName();
        }

        String fileName = storeMap.get(detailVO.getStoreNo()) +
                carrierName +
                detailVO.getDeliveryDate() + "批量调整模板.xls";
        ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());

    }

    private void packageSystemField(Row title, Set<String> calculateNames) {

        int titleIndex = 0;
        title.createCell(titleIndex).setCellValue("配送日期");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("路线");
        titleIndex++;
        for (String s : calculateNames) {
            title.createCell(titleIndex).setCellValue(s);
            titleIndex++;
        }
        title.createCell(titleIndex).setCellValue("帮采费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("打车费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("过路费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("干线用车费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("线路补贴费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("加车费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("疫情补贴费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("专车费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("核酸费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("通行证费");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("扣减费用");
        titleIndex++;
        title.createCell(titleIndex).setCellValue("调整原因");

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importCostAdjustmentTemplate(Integer id, MultipartFile file) {
        logger.info("批量明细单费用调整执行开始>>>>>>>>>>");
        BmsSettleAccountVO accountVO = bmsSettleAccountMapper.selectByPrimaryKey(id);
        BmsSettleAccountQuery param = new BmsSettleAccountQuery();
        param.setId(id);
        Workbook workbook;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Iterator<Sheet> sheetIterator = workbook.sheetIterator();
            while (sheetIterator.hasNext()) {
                Sheet sheet = sheetIterator.next();
                int lastRowNum = sheet.getLastRowNum();
                List<String> paths = new ArrayList<>();

                // 校验路线信息
                AjaxResult result = getAjaxResult(sheet, paths);
                if (Objects.nonNull(result)) {
                    return result;
                }
                int rowCellIndex = 2;
                List<String> calculateFields = new ArrayList<>();
                String calculateField = sheet.getRow(0).getCell(rowCellIndex).getStringCellValue();
                while (StringUtils.isNotBlank(calculateField)) {
                    if (calculateFields.contains(calculateField)) {
                        return AjaxResult.getErrorWithMsg("费用详情字段重复,请检查");
                    }
                    calculateFields.add(calculateField);
                    rowCellIndex++;
                    try {
                        calculateField = sheet.getRow(0).getCell(rowCellIndex).getStringCellValue();
                    } catch (Exception e) {
                        return AjaxResult.getErrorWithMsg("调整原因不规范");
                    }
                    if (Objects.equals("调整原因", calculateField)) {
                        break;
                    }
                }
                param.setPaths(paths);
                List<BmsDeliverySettleAccountsDetailVO> detailVos = bmsDeliverySettleAccountsDetailMapper.selectByCondition(param);

                // 校验账单信息
                AjaxResult errorWithMsg = getAjaxResult(calculateFields, detailVos);
                if (Objects.nonNull(errorWithMsg)) {
                    return errorWithMsg;
                }
                // 封装调整单信息
                return handleData(param, accountVO, sheet, lastRowNum, rowCellIndex);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        logger.info("批量明细单费用调整执行结束>>>>>>>>>>");
        return AjaxResult.getOK("上传失败");
    }

    @Nullable
    private AjaxResult getAjaxResult(List<String> calculateFields, List<BmsDeliverySettleAccountsDetailVO> detailVos) {
        if (CollectionUtils.isEmpty(detailVos)) {
            return AjaxResult.getErrorWithMsg("批量调整失败,无费用详情信息");
        }

        // 获取所有费用详情字段
        Set<String> calculateNames = getAllCalculateNames(detailVos);
        for (String field : calculateFields) {
            if (!calculateNames.contains(field)) {
                return AjaxResult.getErrorWithMsg("不可新增账单字段:" + field);
            }
        }
        return null;
    }

    private Set<String> getAllCalculateNames(List<BmsDeliverySettleAccountsDetailVO> detailVos) {
        Set<String> calculateNames = new HashSet<>();
        detailVos.forEach(detailVo -> {
            List<BmsCalculationDetailsVO> calculationDetailsVos = bmsCalculationDetailsMapper.selectByAccountsDetailId(detailVo.getId());
            Set<String> details = calculationDetailsVos.stream().map(BmsCalculationDetailsVO::getCalculateName).collect(Collectors.toSet());
            calculateNames.addAll(details);
        });
        return calculateNames;
    }

    @Nullable
    private AjaxResult getAjaxResult(Sheet sheet, List<String> paths) {
        for (int i = 1; i < sheet.getLastRowNum() + 1; i++) {
            Cell cell;
            try {
                cell = sheet.getRow(i).getCell(1);
            } catch (Exception e) {
                break;
            }
            String path = cell.getStringCellValue();
            if (StringUtils.isEmpty(path)) {
                return AjaxResult.getErrorWithMsg("路线不完整");
            }
            if (paths.contains(path)) {
                return AjaxResult.getErrorWithMsg("路线不完整");
            }
            paths.add(path);
        }
        return null;
    }

    private AjaxResult handleData(BmsSettleAccountQuery param, BmsSettleAccountVO accountVO, Sheet sheet, int lastRowNum, int rowCellIndex) {
        for (int i = 1; i <= lastRowNum; i++) {
            Cell cell;
            try {
                cell = sheet.getRow(i).getCell(1);
            } catch (Exception e) {
                break;
            }
            String path = cell.getStringCellValue();
            param.setPath(path);
            DeliverySettleAccountsDetail detail = bmsDeliverySettleAccountsDetailMapper.selectByPath(param);
            if (Objects.isNull(detail)) {
                sheet.getRow(i).getCell(rowCellIndex).setCellValue("账单数据不存在");
                continue;
            }

            List<BmsCalculationDetailsVO> caDetailVo = bmsCalculationDetailsMapper.selectByAccountsDetailId(detail.getId());
            Map<String, BmsCalculationDetailsVO> caDetailMap = caDetailVo.stream().collect(Collectors.toMap(BmsCalculationDetailsVO::getCalculateName, Function.identity(), (o1, o2) -> o1));
            String remake = sheet.getRow(i).getCell(rowCellIndex).getStringCellValue();

            List<BmsCalculationDetailsVO> list = new ArrayList<>();
            // 单行生成调整信息
            BmsCostAdjustmentQuery query = packageParameter(accountVO, sheet, rowCellIndex, i, detail, caDetailMap, remake, list);
            // 发起调整
            if (Objects.nonNull(query)) {
                AjaxResult result = costAdjustmentService.saveCostAdjustment(query);
                if (!result.isSuccess()) {
                    return result;
                }
            }
        }
        return AjaxResult.getOK("上传成功");
    }

    private BmsCostAdjustmentQuery packageParameter(BmsSettleAccountVO accountVO, Sheet sheet, int rowCellIndex, int i, DeliverySettleAccountsDetail detail, Map<String, BmsCalculationDetailsVO> caDetailMap, String remake, List<BmsCalculationDetailsVO> list) {
        if (StringUtils.isEmpty(remake)) {
            return null;
        }
        BmsCostAdjustmentQuery query = new BmsCostAdjustmentQuery();
        query.setStoreNo(accountVO.getStoreNo());
        query.setDeliveryDate(accountVO.getDeliveryDate());
        query.setId(detail.getId());
        query.setType(1);
        for (int j = 2; j <= rowCellIndex - 1; j++) {
            if (Objects.isNull(sheet.getRow(i).getCell(j))) {
                continue;
            }
            String stringAmount = String.valueOf(sheet.getRow(i).getCell(j).getNumericCellValue());
            // 金额正则校验
            boolean res = checkPrice(stringAmount);
            if (!res) {
                sheet.getRow(i).getCell(rowCellIndex).setCellValue(stringAmount + "金额异常");
                break;
            }
            BmsCalculationDetailsVO detailsQuery = new BmsCalculationDetailsVO();
            String calculationField = sheet.getRow(0).getCell(j).getStringCellValue();
            BmsCalculationDetailsVO detailsVO = caDetailMap.get(calculationField);
            detailsQuery.setOldAmount(detailsVO.getAmount());
            detailsQuery.setNewAmount(new BigDecimal(stringAmount).setScale(2, BigDecimal.ROUND_HALF_UP));
            detailsQuery.setRemake(remake);
            detailsQuery.setId(detailsVO.getId());
            detailsQuery.setCalculateName(detailsVO.getCalculateName());
            list.add(detailsQuery);
        }
        query.setBmsCalculationDetails(list);
        return query;
    }

    public static boolean checkPrice(String price) {
        String regex = "\\d\\.\\d*|[1-9]\\d*|\\d*\\.\\d*|\\d";
        // 将给定的正则表达式编译到模式中
        Pattern pattern = Pattern.compile(regex);
        // 创建匹配给定输入与此模式的匹配器
        Matcher isNum = pattern.matcher(price);
        return isNum.matches();
    }


    @Override
    public String createImportKey(Integer id) {
        return MD5Util.string2MD5(getAdminId() + String.valueOf(id));
    }

    private Set<String> getCalculateNames(List<BmsDeliverySettleAccountsDetailVO> detailVos) {
        Set<String> calculateNames = new HashSet<>();
        detailVos.forEach(detailVo -> {
            List<BmsCalculationDetailsVO> calculationDetailsVos = bmsCalculationDetailsMapper.selectCustomByAccountsDetailId(detailVo.getId());
            Set<String> details = calculationDetailsVos.stream().map(BmsCalculationDetailsVO::getCalculateName).collect(Collectors.toSet());
            calculateNames.addAll(details);
        });
        return calculateNames;
    }

    private List<List<String>> detailExcelDynamicHead(List<String> fieldNames) {
        List<List<String>> list = Lists.newArrayList();
        for (String fieldName : fieldNames) {
            List<String> head = Lists.newArrayList();
            head.add(fieldName);
            list.add(head);
        }
        return list;
    }

    @Override
    public AjaxResult selectAccountFeeDetails(BmsSettleAccountQuery param) {

        // 封装条件
        AjaxResult result = handleParam(param);
        if (result != null) {
            return result;
        }

        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<BmsDeliverySettleAccountsDetailVO> accountsDetails = bmsDeliverySettleAccountsDetailMapper.selectByCondition(param);
        accountsDetails.forEach(detail -> {
            if (Objects.nonNull(detail.getQuotationId())) {
                BmsDeliveryQuotationArea area = bmsDeliveryQuotationAreaMapper.selectById(detail.getServiceAreaId());
                if (Objects.nonNull(area)) {
                    detail.setServiceAreaName(area.getArea());
                }
            }
            detail.setStoreName(storeMap.get(detail.getStoreNo()));
            if (Objects.nonNull(detail.getCarrierId())) {
                Carrier carrier = carrierMapper.selectByPrimaryKey(Long.valueOf(detail.getCarrierId()));
                detail.setCarrierName(carrier.getCarrierName());
            }

            if (StringUtils.isNotBlank(detail.getPassingArea())) {
                detail.setDistricts(Arrays.asList(detail.getPassingArea().split(SEPARATING_SYMBOL)));
            }
            List<BmsCalculationDetailsVO> calculationDetails = bmsCalculationDetailsMapper.selectByAccountsDetailId(detail.getId());
            // 应付费用
            BigDecimal accountPayable = calculationDetails.stream().filter(details -> !"扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setPayableAmount(accountPayable);
            // 扣减费用
            BigDecimal deductionPayable = calculationDetails.stream().filter(details -> "扣减费用".equals(details.getCalculateName())).map(BmsCalculationDetailsVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setDeductionAmount(deductionPayable);
            // 查询审核中费用调整单
            Integer count = bmsCostAdjustmentMapper.selectInReviewByAccountsDetailId(detail.getId());
            detail.setHaveExamineTask(count > NumberUtils.INTEGER_ZERO ? 1 : 0);
        });
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(accountsDetails));
    }

    @Nullable
    private AjaxResult<PageInfo<Object>> handleParam(BmsSettleAccountQuery param) {
        // 处理省市区搜索
        if (!CollectionUtils.isEmpty(param.getQuotationAreas())) {
            List<String> districts = param.getQuotationAreas().stream().map(CarrierQuotationArea::getArea).collect(Collectors.toList());
            param.setPaths(districts);
        }
        if (Objects.nonNull(param.getHaveExamineTask())) {
            List<Integer> accountDetailsIds = bmsCostAdjustmentMapper.selectPathInReview(param.getId());
            if (Objects.equals(1, param.getHaveExamineTask())) {
                if (CollectionUtils.isEmpty(accountDetailsIds)) {
                    return AjaxResult.getOK(PageInfoHelper.createPageInfo(new ArrayList<>()));
                }
                param.setSettleAccountIds(accountDetailsIds);
            } else {
                if (!CollectionUtils.isEmpty(accountDetailsIds)) {
                    param.setFilterSettleAccountIds(accountDetailsIds);
                }
            }
        }
        return null;
    }

    @Override
    public AjaxResult selectCalculationDetails(Integer id) {
        BmsDeliverySettleAccountsDetailVO detailVo = bmsDeliverySettleAccountsDetailMapper.selectById(id);
        if (Objects.isNull(detailVo)) {
            AjaxResult.getErrorWithMsg("未找到费用详情");
        }
        BmsPathCostDetailVO result = ConvertUtils.convert(detailVo, BmsPathCostDetailVO.class);
        result.setDeliveryDate(detailVo.getDeliveryDate());
        result.setServiceAreaName(detailVo.getServiceAreaName());
        result.setStoreName(storeMap.get(detailVo.getStoreNo()));
        result.setStoreNo(detailVo.getStoreNo());
        result.setPath(detailVo.getPath());
        if (Objects.nonNull(detailVo.getCarrierId())) {
            Carrier carrier = carrierMapper.selectByPrimaryKey(Long.valueOf(detailVo.getCarrierId()));
            result.setCarrierName(carrier.getCarrierName());
        }
        result.setProvince(detailVo.getProvince());
        result.setCity(detailVo.getCity());

        if (StringUtils.isNotBlank(detailVo.getPassingArea())) {
            result.setDistricts(Arrays.asList(detailVo.getPassingArea().split(SEPARATING_SYMBOL)));
        }
        result.setActualKilometers(detailVo.getActualKilometers());
        result.setStartingKilometers(detailVo.getStartingKilometers());
        result.setSkuQuantity(detailVo.getSkuQuantity());
        result.setTaxiPosition(detailVo.getTaxiPosition());
        result.setTotalPosition(detailVo.getTotalPosition());
        result.setSystemKilometers(detailVo.getSystemKilometers());
        BmsAdjustmentVO adjustmentVO = bmsCostAdjustmentMapper.selectByDeliverySettleAccountsDetailId(id);
        result.setHaveExamineTask(Objects.nonNull(adjustmentVO) ? 1 : 0);
        List<BmsCalculationDetailsVO> calculationDetailsVos = bmsCalculationDetailsMapper.selectByAccountsDetailId(id);
        result.setBmsCalculationDetailsVos(calculationDetailsVos);
        result.setStatus(detailVo.getStatus());
        result.setId(detailVo.getId());
        result.setStartingPositions(detailVo.getStartingPositions());
        BmsCostAdjustment adjustment = bmsCostAdjustmentMapper.selectByAccountDetailId(id);
        if (Objects.isNull(adjustment)) {
            result.setHaveExamineTask(0);
        } else if (Objects.equals(0, adjustment.getStatus())) {
            result.setHaveExamineTask(1);
            result.setAdjustmentStatus(adjustment.getStatus());
        } else {
            result.setHaveExamineTask(0);
            result.setAdjustmentStatus(adjustment.getStatus());
        }

        return AjaxResult.getOK(result);
    }

    @Override
    public void deliverySettleAccount() {
        // 查询所有城配仓
        List<WarehouseLogisticsCenterVO> storeCenter = warehouseLogisticsCenterMapper.selectAllCenterVO(new WarehouseLogisticsCenter());
        LocalDate yesterdayDeliveryDate = LocalDate.now().minusDays(1);

        for (WarehouseLogisticsCenterVO center : storeCenter) {
            Integer storeNo = center.getStoreNo();
            try {
                selfService.settleAccountSingle(storeNo, yesterdayDeliveryDate);
            } catch (Exception e) {
                logger.error( "城配仓：{},结算明细单生成失败:{},",storeMap.get(storeNo), e.getMessage(), e);
            }

        }
    }

    /**
     *
     * @param calculateCostAmountsMap
     * @param cost
     * @param quoteName
     * @param amount
     */
    private void putSystemField(Map<String, BmsDeliveryQuoteCalculateCostVO> calculateCostAmountsMap, BmsDeliveryQuoteCalculateCost cost, String quoteName, BigDecimal amount) {
        cost.setCalculateName(quoteName);
        cost.setFormula(quoteName);
        cost.setCalculateType(BmsCalculateTypeEnum.PATH.getCode());
        bmsDeliveryQuoteCalculateCostMapper.insert(cost);
        BmsDeliveryQuoteCalculateCostVO costVO = new BmsDeliveryQuoteCalculateCostVO();
        costVO.setAmount(amount);
        costVO.setCalculateName(quoteName);
        costVO.setType(0);
        costVO.setSourceType(0);
        costVO.setId(cost.getId());
        calculateCostAmountsMap.put(quoteName, costVO);
    }


    /**
     * 校验阐述
     *
     * @param accountVos
     * @return
     */
    private AjaxResult checkReconciliationSettleAccount(List<BmsSettleAccountVO> accountVos) {

        LocalDate beginTime = null;
        for (BmsSettleAccountVO accountVo : accountVos) {
            LocalDate deliveryDate = accountVo.getDeliveryStartDate();
            int monthValue = deliveryDate.getMonthValue();
            int year = deliveryDate.getYear();
            LocalTime localTime = LocalTime.of(10, 0, 0);
            //获取当月一号
            LocalDate localDate = LocalDate.of(year, monthValue, 1);
            //下个月一号 10点
            LocalDateTime nextMonth = LocalDateTime.of(localDate.plusMonths(1), localTime);
            if (nextMonth.isAfter(LocalDateTime.now())) {
                return AjaxResult.getErrorWithMsg("可发起结算对账时间为次月早上10点之后");
            }
            beginTime = Objects.isNull(beginTime) ? localDate : beginTime;
            if (!Objects.equals(beginTime, localDate)) {
                return AjaxResult.getErrorWithMsg("不可发起跨月的结算对账单");
            }
        }
        BmsSettleAccountVO bmsSettleAccountVO = accountVos.get(0);
        BmsSettleAccountQuery query = new BmsSettleAccountQuery();
        query.setDeliveryStartDate(beginTime);
        query.setDeliveryEndDate(beginTime.plusMonths(1).minusDays(1));
        query.setStoreNo(bmsSettleAccountVO.getStoreNo());
        query.setCarrierId(bmsSettleAccountVO.getCarrierId());
        List<BmsSettleAccountVO> bmsSettleAccountVOS = bmsSettleAccountMapper.selectSettleAccount(query);
        if (!Objects.equals(bmsSettleAccountVOS.size(), accountVos.size())) {
            return AjaxResult.getErrorWithMsg("月度数据不完整，请返回重新选择");
        }
        return AjaxResult.getOK();
    }
}
