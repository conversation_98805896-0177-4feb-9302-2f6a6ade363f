package net.summerfarm.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/11 10:42
 */
@Component
@NacosPropertySource(dataId = "purchase_common",type = ConfigType.PROPERTIES,autoRefreshed = true)
public class CommonDynamicConfig {


    /**
     * wms回调迁移
     */
    @NacosValue(value = "${wms.stock.callback.switch:true}", autoRefreshed = true)
    public Boolean wmsCallbackStockSwitch;


    public Boolean getWmsCallbackStockSwitch(){
        return wmsCallbackStockSwitch;
    }
}
