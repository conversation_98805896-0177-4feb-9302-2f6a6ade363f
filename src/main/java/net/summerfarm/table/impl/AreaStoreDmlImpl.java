package net.summerfarm.table.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.AreaStoreService;
import net.summerfarm.service.PurchasesService;
import net.summerfarm.table.DbTableDml;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 14:22
 */
@Slf4j
@Service
public class AreaStoreDmlImpl implements DbTableDml {

    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private PurchasesService purchasesService;


    @Override
    public void tableDml(DtsModel dtsModel) {
        if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), dtsModel.getType())) {
            areaStoreService.handleBinLogToOpenSale(dtsModel);
            purchasesService.sendWarningMsg(dtsModel);
            areaStoreService.sendReverseOrSelloutMsg(dtsModel);
            // 虚拟库存变更触发自动拆包
            areaStoreService.handleAutoUnpacking(dtsModel);
        }
    }
}
