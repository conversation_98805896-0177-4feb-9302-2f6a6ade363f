package net.summerfarm.table.item.impl;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.ItemProductInfoUtil;
import net.summerfarm.facade.item.MarketProviderFacade;
import net.summerfarm.manage.client.inventory.enums.SkuOutdatedEnum;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.service.item.ProductSyncToItemService;
import net.summerfarm.table.enums.DBTableName;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-04-28
 * @description 监听Inventory
 */
@Slf4j
@Service
public class InventoryIDbDmlImpl extends AbstractDbDml {
    @Resource
    private ProductSyncToItemService productSyncToItemService;
    @Override
    public String getTableDmlName() {
        return DBTableName.INVENTORY;
    }

    @Override
    protected void syncInsertItemHandle(Map<String, String> data) {
        log.info("开始同步商品数据,data :{}", JSON.toJSONString(data));
        if (check(data)) {
            return;
        }
        Long id = Long.parseLong(data.get("inv_id"));
        productSyncToItemService.skuInsertSyncToItem(id);
    }

    private static boolean check(Map<String, String> data) {
        //上新中商品不同步
        Integer outdatedNew = Integer.valueOf(data.get("outdated"));
        if (SkuOutdatedEnum.CREATING.getCode().equals(Integer.valueOf(outdatedNew))){
            return true;
        }
        Integer createType = Integer.valueOf(data.get("create_type"));
        if (!ItemProductInfoUtil.SYNC_SKU_CREATE_TYPE_LIST.contains(createType)) {
            return true;
        }
        return false;
    }


    @Override
    protected void syncUpdateItemHandle(Map<String, String> oldData, Map<String, String> data) {
        //上新中商品不同步
        if (check(data)) {
            log.info ("上新中商品不同步，跳过更新，sku={}",data.get("sku"));
            return;
        }
        String weight = oldData.get("weight");
        //过滤重复修改消息
        if (Objects.nonNull(weight) && "".equals(weight)){
            log.info ("空=weight，跳过更新，sku={}",data.get("sku"));
            return;
        }
        upsertSkuToItem(data,oldData);
    }
    private void upsertSkuToItem(Map<String, String> data,Map<String, String> oldData) {
        String sku = data.get("sku");
        MarketItemInfoResp marketItemInfoResp = marketProviderFacade.getMarketItemInfoByItemCode(ItemProductInfoUtil.TENANT_ID, sku);
        Long id = Long.parseLong(data.get("inv_id"));
        if (Objects.isNull(marketItemInfoResp)) {
            productSyncToItemService.skuInsertSyncToItem(id);
        } else {
            Boolean isSyncToItem = ItemProductInfoUtil.isSyncToItem(getTableDmlName(), data, oldData);
            //同步商品
            if (isSyncToItem) {
                log.info("开始同步鲜沐商品数据,sku:{}", sku);
                productSyncToItemService.skuUpdateSyncToItem(id);
            }
            Boolean isSyncToProduct = ItemProductInfoUtil.isSyncToProduct(getTableDmlName(), data, oldData);
            //同步货品
            if (isSyncToProduct) {
                log.info("同步鲜沐货品数据,sku:{}", sku);
                productSyncToItemService.skuUpdateSyncToSku(id);
            }
        }
    }
    @Override
    protected void syncInsertItemRetryHandle(Map<String, String> data) {
        syncInsertItemHandle(data);
    }

    @Override
    protected void syncUpdateItemRetryHandle(Map<String, String> oldData, Map<String, String> data) {
        syncUpdateItemHandle(oldData,data);
    }

}
