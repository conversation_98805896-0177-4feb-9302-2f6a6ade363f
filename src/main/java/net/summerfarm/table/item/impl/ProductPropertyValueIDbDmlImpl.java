package net.summerfarm.table.item.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.ItemProductInfoUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.service.item.ProductSyncToItemService;
import net.summerfarm.table.enums.DBTableName;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-04-28
 * @description 属性变化同步到货品
 */
@Slf4j
@Service
public class ProductPropertyValueIDbDmlImpl extends AbstractDbDml {


    @Resource
    private ProductSyncToItemService productSyncToItemService;
    @Override
    public String getTableDmlName() {
        return DBTableName.PRODUCTS_PROPERTY_VALUE;
    }

    @Override
    protected void syncInsertItemHandle(Map<String, String> data) {

    }
    @Override
    protected void syncUpdateItemHandle(Map<String, String> oldData, Map<String, String> data) {
        Long propertyId = Long.valueOf(data.get("products_property_id"));
        if (!ItemProductInfoUtil.PROPERTIES_ID_LIST.contains(propertyId)) {
           return;
        }
        String pdId = data.get("pd_id");
        if (StringUtils.isNotBlank(pdId)) {
            productSyncToItemService.propertyUpdateSyncToSpu(Long.valueOf(pdId));
        }
    }

    @Override
    protected void syncInsertItemRetryHandle(Map<String, String> data) {

    }
    @Override
    protected void syncUpdateItemRetryHandle(Map<String, String> oldData, Map<String, String> data) {
        this.syncUpdateItemHandle(oldData,data);
    }
}
