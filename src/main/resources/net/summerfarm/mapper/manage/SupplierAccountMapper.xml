<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SupplierAccountMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SupplierAccount" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="supplier_id" property="supplierId" jdbcType="INTEGER" />
    <result column="pay_type" property="payType" jdbcType="INTEGER" />
    <result column="account_name" property="accountName" jdbcType="VARCHAR" />
    <result column="account_bank" property="accountBank" jdbcType="VARCHAR" />
    <result column="account_ascription" property="accountAscription" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="net.summerfarm.model.domain.SupplierAccount" >
    insert into supplier_account (id, supplier_id, pay_type,
      account_name, account_bank, account_ascription,
      account, creator)
    values (#{id,jdbcType=INTEGER}, #{supplierId,jdbcType=INTEGER}, #{payType,jdbcType=INTEGER},
      #{accountName,jdbcType=VARCHAR}, #{accountBank,jdbcType=VARCHAR}, #{accountAscription,jdbcType=VARCHAR},
      #{account,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.SupplierAccount" >
    insert into supplier_account
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="supplierId != null" >
        supplier_id,
      </if>
      <if test="payType != null" >
        pay_type,
      </if>
      <if test="accountName != null" >
        account_name,
      </if>
      <if test="accountBank != null" >
        account_bank,
      </if>
      <if test="accountAscription != null" >
        account_ascription,
      </if>
      <if test="account != null" >
        account,
      </if>
      <if test="creator != null" >
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null" >
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="accountName != null" >
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountBank != null" >
        #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="accountAscription != null" >
        #{accountAscription,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="updateSelective" parameterType="net.summerfarm.model.domain.SupplierAccount" >
    update supplier_account set
    <trim suffixOverrides="," >
      <if test="supplierId != null" >
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="payType != null" >
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="accountName != null" >
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountBank != null" >
        account_bank= #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="accountAscription != null" >
        account_ascription = #{accountAscription,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
    where id = #{id}
  </insert>

  <select id="selectSupplierAccount" parameterType="int" resultMap="BaseResultMap">
    select id,
       supplier_id,
       pay_type,
       account_name,
       account_bank,
       account_ascription,
       account,
       creator
    from supplier_account where supplier_id = (select supplier_id from supplier_account where id = #{accountId})
  </select>

  <select id="selectAccountName" parameterType="string" resultType="integer">
    select count(1)
    from supplier_account
    where account_name = #{supplierName}
  </select>

  <select id="selectName" parameterType="string" resultMap="BaseResultMap">
    select id,
           supplier_id,
           pay_type,
           account_name,
           account_bank,
           account_ascription,
           account,
           creator
    from supplier_account
    where account_name = #{supplierName}
    limit 1
  </select>

  <select id="selectSupplierBySupplierId" parameterType="int" resultMap="BaseResultMap">
    select id,
       supplier_id,
       pay_type,
       account_name,
       account_bank,
       account_ascription,
       account,
       creator
    from supplier_account where supplier_id = #{supplierId}
  </select>

  <delete id="delete" parameterType="int">
    delete from supplier_account where id = #{accountId}
  </delete>

  <select id="selectById" resultMap="BaseResultMap">
    select id,
       supplier_id,
       pay_type,
       account_name,
       account_bank,
       account_ascription,
       account,
       creator
    from supplier_account where id = #{id}
  </select>

  <delete id="deleteBySupplierId">
    delete from supplier_account where supplier_id = #{supplierId}
  </delete>
</mapper>
