<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AdjustmentDetailMapper">

    <resultMap id="VOMap" type="net.summerfarm.model.vo.AdjustmentDetailVO">
        <result column="adjust_price" property="adjustPrice" jdbcType="DECIMAL"/>
        <result column="adjust_delivery" property="adjustDelivery" jdbcType="DECIMAL"/>
        <result column="adjust_no" property="adjustNo" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="total_price" property="actualPrice" jdbcType="DECIMAL"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="order_item_id" property="orderItemId" jdbcType="BIGINT"/>
        <result column="invoice_status" property="invoiceStatus" jdbcType="TINYINT"/>
        <result column="finish_time " property="finishTime" jdbcType="TIMESTAMP"/>
        <result column="approve_time " property="approveTime" jdbcType="TIMESTAMP"/>
        <result column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectDetail" resultMap="VOMap">
        select fad.adjust_price,fad.adjust_delivery,fad.adjust_no,fad.m_id,fad.order_item_id,IFNULL(o.order_no,fad.order_no) order_no,oi.sku,oi.pd_name,oi.weight,oi.amount,oi.price,oi.amount * oi.price as total_price,o.delivery_fee,o.order_time
        ,fa.approve_time
        from finance_adjustment_detail fad
        left join order_item oi on fad.order_item_id = oi.id
        left join orders o on o.order_no = oi.order_no
        LEFT JOIN finance_adjustment fa on fad.adjust_no=fa.adjust_no
        where  fad.adjust_no = #{adjustNo}
        ORDER by oi.id DESC
    </select>

    <select id="selectDetailByMId" resultMap="VOMap">
        select fad.adjust_price,fad.adjust_delivery,fad.adjust_no,fad.m_id,fad.order_item_id,IFNULL(o.order_no,fad.order_no) order_no,oi.sku,oi.pd_name,oi.weight,oi.amount,oi.price,oi.amount * oi.price as total_price,o.delivery_fee,o.order_time
        from finance_adjustment_detail fad
        left join order_item oi on fad.order_item_id = oi.id
        left join orders o on o.order_no = oi.order_no
        where  fad.adjust_no = #{adjustNo} and fad.m_id = #{mId}
        ORDER by fad.id DESC
    </select>

    <select id="selectItem" resultMap="VOMap" parameterType="net.summerfarm.model.input.AdjustmentInput">
        select oi.id order_item_id,oi.order_no,oi.sku,oi.pd_name,oi.weight,oi.amount,oi.price,oi.amount * oi.price as total_price,
        o.order_time,o.invoice_status, c.contact_id, dp.delivery_time
        from finance_accounting_store_detail fasd
        left join orders o on o.order_no = fasd.order_no
        <if test="mname != null">
            left join merchant m on o.m_id = m.m_id
        </if>
        left join order_item oi on o.order_no = oi.order_no
        left join delivery_plan dp on dp.order_no = o.order_no
        left join contact c on dp.contact_id = c.contact_id
        <where>
            fasd.total_price > 0
            <if test="mname != null">
                and m.mname like concat('%',#{mname},'%')
            </if>
            <if test="orderNo != null">
                and o.order_no = #{orderNo}
            </if>
            <if test="pdName != null">
                and oi.pd_name like concat('%',#{pdName},'%')
            </if>
            <if test="sku != null">
                and oi.sku = #{sku}
            </if>
            <if test="phone != null">
                and c.phone = #{phone}
            </if>
            <if test="billNumber != null">
                and fasd.bill_number = #{billNumber}
            </if>
        </where>
    </select>

    <select id="selectSuccessNumByOrderItem" resultType="java.math.BigDecimal">
        select IFNULL(SUM(fad.adjust_price), 0)
        from finance_adjustment_detail fad
        left join finance_adjustment fa on fad.adjust_no = fa.adjust_no
        where fad.order_item_id = #{orderItemId} and  fa.status = 1
    </select>

    <select id="selectSuccessNumByOrderNo" resultType="java.math.BigDecimal">
        select IFNULL(SUM(fad.adjust_delivery), 0)
        from finance_adjustment_detail fad
                 left join finance_adjustment fa on fad.adjust_no = fa.adjust_no
        where fad.order_no = #{orderNo} and  fa.status = 1
    </select>

    <select id="selectByOrderItem" resultType="net.summerfarm.model.vo.AdjustmentDetailVO">
        select fa.adjust_no adjustNo,fa.approve_time approveTime,fad.sku,fad.order_item_id
        orderItemId,fad.adjust_price adjustPrice,fad.adjust_delivery adjustDelivery,fad.m_id mId
        from finance_adjustment_detail fad
        left join finance_adjustment fa on fad.adjust_no = fa.adjust_no
        where order_item_id in
        <foreach collection="orderItemIdList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and fa.status = 1
    </select>

    <insert id="insertBatch" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into finance_adjustment_detail (m_id,adjust_no,sku,order_item_id,adjust_price,adjust_delivery,order_no) VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.mId},#{item.adjustNo},#{item.sku}, #{item.orderItemId}, #{item.adjustPrice}, #{item.adjustDelivery}, #{item.orderNo})
        </foreach>
    </insert>

    <select id="selectDelivery" resultType="java.math.BigDecimal">
        select fad.adjust_delivery
        from finance_adjustment_detail fad
        where fad.adjust_no = #{adjustNo} and fad.order_no = #{orderNo}
    </select>
    <select id="selectAdjustAmountByItemId" resultType="java.math.BigDecimal">
        select IFNULL(sum(adjust_price+adjust_delivery), 0)
        from finance_adjustment_detail fad
        LEFT JOIN  finance_adjustment fa on fa.`adjust_no` =fad.`adjust_no`
        where status=1 and  order_no in
        <foreach collection="orderNos" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectAdjustByOrderNos" resultType="net.summerfarm.model.vo.finance.AdminAdjustExportVo">
        SELECT fad.m_id                                                                             mId,
               fad.adjust_no                                                                        adjustNo,
               oi.`order_no`                                                                        orderNo,
               oi.`add_time`                                                                        orderTime,
               fa.`approve_time`                                                                    adjustmentTime,
               oi.`sku`                                                                             sku,
               oi.`pd_name`                                                                         pdName,
               oi.`weight`                                                                          weight,
               if(os.order_pay_type in (1, 3), '账期', if(os.order_pay_type in (2, 4), '现结', '')) settlementMethod,
               oi.`amount`                                                                          quantity,
               fad.`adjust_price`                                                                   adjustmentActualAmount,
               fad.`adjust_delivery`                                                                adjustmentDeliveryAmount,
               fad.`adjust_price` + `adjust_delivery`                                               adjustmentTotalAmount
        from finance_adjustment fa
                 LEFT JOIN finance_adjustment_detail fad on fad.`adjust_no` = fa.`adjust_no`
                 LEFT JOIN `order_item` oi on oi.id = fad.`order_item_id`
                 LEFT JOIN orders os on os.order_no = oi.order_no
        where fa.`status` = 1 and average_flag = 1 and fad.order_no in
        <foreach collection="orderNos" separator="," open="(" close=")" item="orderNo">
            #{orderNo}
        </foreach>
    </select>
    <select id="selectAdjustByStoreOrder" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(fad.adjust_price+adjust_delivery), 0)
        FROM finance_adjustment_detail fad
                 LEFT JOIN finance_accounting_store_detail fasd ON fad.order_no = fasd.order_no
                 LEFT JOIN finance_accounting_store fas on fasd.finance_accounting_store_id = fas.id
        WHERE fasd.bill_number = #{billNo}
          and fas.m_id = #{mId}
    </select>
</mapper>
