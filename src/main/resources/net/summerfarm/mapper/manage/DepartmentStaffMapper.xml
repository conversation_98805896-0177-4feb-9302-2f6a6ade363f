<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DepartmentStaffMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DepartmentStaff">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="union_id" jdbcType="VARCHAR" property="unionId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="leader" jdbcType="BIT" property="leader" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,dept_id,user_id,union_id,`name`,mobile,leader,`status`,create_time,update_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DepartmentStaff" useGeneratedKeys="true">
        insert into department_staff (dept_id,user_id,union_id,name,mobile,leader,status,create_time) value (#{deptId,jdbcType=BIGINT}, #{userId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR},#{name,jdbcType=VARCHAR},#{mobile,jdbcType=VARCHAR}, #{leader,jdbcType=BIT},#{status,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP})
    </insert>
    <select id="selectByStaff" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.DepartmentStaff">
        select *
        from department_staff where dept_id=#{deptId,jdbcType=BIGINT} and union_id = #{unionId,jdbcType=VARCHAR}
    </select>

    <update id="updateByStaff" parameterType="net.summerfarm.model.domain.DepartmentStaff">
        update department_staff
        set name= #{name},mobile=#{mobile},leader=#{leader},status =#{status},update_time=#{updateTime}
        where union_id = #{unionId} and dept_id = #{deptId}
    </update>

    <update id="updateByStaffStatus" parameterType="net.summerfarm.model.domain.DepartmentStaff">
    update department_staff
    set status= 0
    </update>

    <select id="findOneByUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from department_staff
        where user_id = #{userId} and status = '1'
        order by id desc
        limit 1
    </select>
</mapper>