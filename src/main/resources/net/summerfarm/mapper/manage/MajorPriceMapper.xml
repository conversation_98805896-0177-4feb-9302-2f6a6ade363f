<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MajorPriceMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MajorPrice">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="area_name" property="areaName" jdbcType="VARCHAR"/>
        <result column="direct" property="direct" jdbcType="INTEGER"/>
        <result column="pay_method" property="payMethod"/>
        <result column="valid_time" property="validTime"/>
        <result column="invalid_time" property="invalidTime"/>
        <result column="mall_show" property="mallShow"/>
        <result column="price_type" property="priceType" javaType="INTEGER"></result>
        <result column="interest_rate" property="interestRate" jdbcType="DECIMAL"></result>
        <result column="fixed_price" property="fixedPrice" jdbcType="DECIMAL"></result>
        <result column="cost" property="cost" jdbcType="DECIMAL"></result>
        <result column="original_price" property="originalPrice" jdbcType="DECIMAL"></result>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,sku,pd_name,weight,area_no,admin_id,price,area_name,direct,pay_method,valid_time, invalid_time,mall_show,price_type,interest_rate,fixed_price,cost,original_price
    </sql>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.MajorPrice" useGeneratedKeys="true"
            keyProperty="id">
        insert into major_price (sku, pd_name,weight,
        price,direct,area_no,admin_id,area_name,pay_method,valid_time,invalid_time,mall_show,price_type,interest_rate,fixed_price,cost,original_price,large_area_no)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.sku},#{item.pdName},
            #{item.weight},#{item.price},#{item.direct},#{item.areaNo},#{item.adminId},#{item.areaName},
            #{item.payMethod},
            #{item.validTime},
            #{item.invalidTime},#{item.mallShow},#{item.priceType},#{item.interestRate},#{item.fixedPrice},#{item.cost},#{item.originalPrice},#{item.largeAreaNo})
        </foreach>

    </insert>

    <update id="updateBatch" parameterType="net.summerfarm.model.input.MajorPriceInput">
        <foreach collection="list" item="item" index="index">
            update major_price
            <set>
                <if test="item.priceType!=null">price_type=#{item.priceType,jdbcType=INTEGER},</if>
                <if test="item.price!=null">price=#{item.price,jdbcType=DECIMAL},</if>
                <if test="item.interestRate!=null">interest_rate=#{item.interestRate,jdbcType=DECIMAL},</if>
                <if test="item.fixedPrice!=null">fixed_price=#{item.fixedPrice,jdbcType=DECIMAL},</if>
                <if test="item.cost!=null">cost=#{item.cost,jdbcType=DECIMAL}</if>
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="batchExpireMajorPrice" >
        update major_price set invalid_time = now()
        where id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <update id="update" parameterType="net.summerfarm.model.input.MajorPriceInput">
        update major_price
        <set>
            <if test="priceType!=null">price_type=#{priceType,jdbcType=INTEGER},</if>
            price=#{price,jdbcType=DECIMAL},
            interest_rate=#{interestRate,jdbcType=DECIMAL},
            fixed_price=#{fixedPrice,jdbcType=DECIMAL},
            cost=#{cost,jdbcType=DECIMAL},
            original_price=#{originalPrice,jdbcType=DECIMAL},
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>


    <delete id="delete">
        DELETE
        FROM major_price
        WHERE admin_id = #{adminId}
          AND direct = #{direct}
    </delete>

    <delete id="deleteBatch">
        DELETE
        FROM major_price
        WHERE id IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectOne" parameterType="net.summerfarm.model.domain.MajorPrice" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM major_price mp
        <where>
            <if test="sku != null">
                AND mp.sku = #{sku}
            </if>
            <if test="pdName != null">
                AND mp.pd_name = #{pdName}
            </if>
            <if test="areaNo != null">
                AND mp.area_no = #{areaNo}
            </if>
            <if test="adminId != null">
                AND mp.admin_id = #{adminId}
            </if>
            <if test="direct != null">
                AND mp.direct = #{direct}
            </if>
        </where>
    </select>

    <select id="selectAreaNoAndSkuById" resultMap="BaseResultMap">
        select mp.area_no,mp.sku,mp.mall_show,mp.price,mp.cost,mp.sku,mp.pd_name,mp.weight,mp.area_name
        from major_price mp
        where id=#{id}
    </select>

    <select id="selectDataCount" parameterType="java.util.HashMap"
            resultType="net.summerfarm.model.vo.MajorDataCountVO">
        SELECT COUNT(c.num) storeCount, SUM(c.num) orderCount, SUM(c.price) totalAmount, c.direct
        FROM (SELECT COUNT(*) num, o.m_id, SUM(o.total_price) price, o.direct direct
              FROM merchant m
                       INNER JOIN orders o ON m.m_id = o.m_id
              WHERE m.admin_id = #{adminId}
                AND o.direct = #{direct}
                AND o.status IN (2, 3, 6)
                AND DATE_FORMAT(o.order_time, '%Y%m%d')<![CDATA[ >=]]> DATE_FORMAT(#{startTime}, '%Y%m%d')
                AND DATE_FORMAT(o.order_time, '%Y%m%d')<![CDATA[ <]]> DATE_FORMAT(#{endTime}, '%Y%m%d')
              GROUP BY o.m_id) c
    </select>

    <select id="selectDataTrend" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT DATE_FORMAT(o.order_time, '%d') 'day',COUNT(*) 'orderNum',SUM(o.total_price) 'totalPrice'
        FROM merchant m
                 LEFT JOIN orders o ON m.m_id = o.m_id
        WHERE m.admin_id = #{adminId}
          AND o.status IN (2, 3, 6)
          AND DATE_FORMAT(o.order_time, '%Y%m%d')<![CDATA[ >=]]> DATE_FORMAT(#{startTime}, '%Y%m%d')
          AND DATE_FORMAT(o.order_time, '%Y%m%d') <![CDATA[<]]> DATE_FORMAT(#{endTime}, '%Y%m%d')
        GROUP BY DATE_FORMAT(o.order_time, '%Y%m%d')
    </select>

    <select id="selectMajorSkuDataVO" parameterType="java.util.HashMap"
            resultType="net.summerfarm.model.vo.MajorSkuDataVO">
        SELECT oi.pd_name 'pdName',oi.weight 'weight',COUNT(*) 'orderCount',SUM(oi.amount) 'amount',SUM(oi.price * oi.amount) 'totalPrice',sku
        FROM order_item oi
                 LEFT JOIN orders o ON oi.order_no = o.order_no
                 LEFT JOIN merchant m ON m.m_id = o.m_id
        WHERE m.admin_id = #{adminId}
          AND o.status IN (2, 3, 6)
          AND DATE_FORMAT(o.order_time, '%Y%m%d')<![CDATA[ >=]]> DATE_FORMAT(#{startTime}, '%Y%m%d')
          AND DATE_FORMAT(o.order_time, '%Y%m%d') <![CDATA[<]]> DATE_FORMAT(#{endTime}, '%Y%m%d')
        GROUP BY oi.sku;
    </select>

    <select id="selectBill" parameterType="java.util.HashMap" resultType="net.summerfarm.model.vo.MajorSkuDataVO">
        SELECT m.mname 'mname',COUNT(*) 'orderCount',SUM(o.total_price) 'totalPrice', m.m_id 'mId'
        FROM merchant m
                 LEFT JOIN orders o ON m.m_id = o.m_id
        WHERE m.admin_id = #{adminId}
          AND o.status IN (2, 3, 6)
          AND o.m_size = '大客户'
          AND o.`type` != 10
          AND DATE_FORMAT(o.order_time, '%Y%m%d')<![CDATA[ >= ]]> DATE_FORMAT(#{startTime}, '%Y%m%d')
          AND DATE_FORMAT(o.order_time, '%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{endTime}, '%Y%m%d')
        GROUP BY o.m_id
    </select>

    <select id="selectBillOrderNo" parameterType="java.util.HashMap" resultType="string">
        SELECT o.order_no
        FROM merchant m
                 LEFT JOIN orders o ON m.m_id = o.m_id
        WHERE m.admin_id = #{adminId}
          AND m.m_id = #{mId}
          AND o.status IN (2, 3, 6)
          AND DATE_FORMAT(o.order_time, '%Y%m%d')<![CDATA[ >= ]]> DATE_FORMAT(#{startTime}, '%Y%m%d')
          AND DATE_FORMAT(o.order_time, '%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{endTime}, '%Y%m%d')
    </select>

    <select id="selectMajorPrice" parameterType="net.summerfarm.model.input.MajorPriceInput"
            resultType="net.summerfarm.model.input.MajorPriceInput">
        SELECT m.id,m.sku,p.pd_name pdName,m.area_name areaName,m.price price,m.direct,i.weight,i.ext_type extType,m.admin_id
        adminId,m.area_no areaNo,
        m.pay_method payMethod, m.valid_time validTime, m.invalid_time invalidTime,m.price_type priceType,
        m.interest_rate
        interestRate,m.fixed_price fixedPrice,m.original_price originalPrice,
        (case when m.valid_time <![CDATA[>]]> now() and m.status = 1 then 0
        when ( m.valid_time <![CDATA[<=]]> now() AND m.invalid_time <![CDATA[>]]> now() and m.status = 1) then 1
        when m.invalid_time <![CDATA[<=]]> now() then 2
        when m.invalid_time <![CDATA[>]]> now() and m.status = 0 then 3
        end ) validStatus,
        ak.price salePrice,
        i.type as characters
        ,ad.name_remakes nameRemakes,
        ak.on_sale onSale,
        ak.show,
        m.mall_show mallShow,i.sub_type subType,
        m.large_area_no AS largeAreaNo,
        m.price_adjustment_value AS priceAdjustmentValue,
        m.remark AS remark,
        m.status AS status
        FROM major_price m
        LEFT JOIN inventory i ON m.sku = i.sku
        LEFT JOIN products p on i.pd_id = p.pd_id
        LEFT JOIN area_sku ak ON m.sku=ak.sku AND m.area_no=ak.area_no
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        LEFT JOIN (
        select area_no ,store_no from fence
        where status = 0
        ) f on f.area_no = ak.area_no
        inner join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        WHERE m.admin_id=#{adminId}
        <if test="direct != null">
            AND m.direct=#{direct}
        </if>
        <if test="largeAreaNo != null">
            AND m.large_area_no=#{largeAreaNo}
        </if>
        <if test="areaNo != null">
            AND m.area_no=#{areaNo}
        </if>
        <if test="priceType != null">
            AND m.price_type=#{priceType}
        </if>
        <if test="pdName != null">
            AND p.pd_name LIKE concat('%',#{pdName},'%')
        </if>
        <if test="sku != null">
            AND m.sku LIKE concat('%',#{sku},'%')
            and wim.sku LIKE concat('%',#{sku},'%')
        </if>
        <if test="pdName != null">
            AND p.pd_name LIKE concat('%',#{pdName},'%')
        </if>
        <if test="areaName != null">
            AND m.area_name LIKE concat('%',#{areaName},'%')
        </if>
        <if test="areaNo != null">
            AND m.area_no = #{areaNo}
        </if>
        <if test="priceType!=null">
            AND m.price_type =#{priceType}
        </if>
        <if test="characters != null">
            AND i.type = #{characters}
        </if>
        <if test="validStatus != null">
            <if test="validStatus == 0">
                AND m.valid_time <![CDATA[>]]> now() and m.status = 1
            </if>
            <if test="validStatus == 1">
                AND m.valid_time <![CDATA[<=]]> now()
                AND m.invalid_time <![CDATA[>]]> now()
                and m.status = 1
            </if>
            <if test="validStatus == 2">
                AND m.invalid_time <![CDATA[<=]]> now()
            </if>
            <if test="validStatus == 3">
                AND m.invalid_time <![CDATA[>]]> now() and m.status = 0
            </if>
        </if>
        <if test="validTime != null">
            AND m.valid_time >= #{validTime}
        </if>
        <if test="invalidTime != null">
            AND m.invalid_time <![CDATA[=]]>  #{invalidTime}
        </if>
        <if test="subType != null">
            AND  i.sub_type=#{subType}
        </if>
        <if test="warehouseNo != null">
            and wim.warehouse_no = #{warehouseNo}
        </if>
        GROUP BY m.id
        order by id desc
        <if test="limitSize != null">
            limit  #{limitSize}
        </if>
    </select>

    <select id="selectList" parameterType="net.summerfarm.model.domain.MajorPrice"
            resultType="net.summerfarm.model.vo.MajorPriceVO">
        SELECT id,sku,pd_name pdName,weight,area_no areaNo,admin_id adminId,price,area_name areaName,direct, pay_method
        payMethod,
        valid_time validTime, invalid_time invalidTime,mp.mall_show mallShow,mp.price_type priceType,mp.interest_rate
        interestRate,mp.fixed_price fixedPrice,mp.cost,mp.original_price originalPrice,
        (case when mp.valid_time <![CDATA[>]]> now() then 0
        when (mp.valid_time <![CDATA[<=]]> now() and mp.invalid_time <![CDATA[>]]> now()) then 1
        when mp.invalid_time <![CDATA[<=]]> now() then 2
        end ) validStatus
        FROM major_price mp
        where mp.admin_id=#{adminId}
        <if test="sku != null">
            AND mp.sku = #{sku}
        </if>
        <if test="areaNo != null">
            AND mp.area_no = #{areaNo}
        </if>
        <if test="direct != null">
            AND mp.direct=#{direct}
        </if>
        <if test="invalidTime != null">
            AND invalid_time > now()
        </if>

    </select>

    <select id="selectMerchantsByMajorPrice" parameterType="net.summerfarm.model.domain.MajorPrice"
            resultType="net.summerfarm.model.domain.Merchant">
        SELECT m.m_id mId,m.admin_id adminId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo,
        m.area,m.province,m.city,m.address,
        m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode, m.remark,m.member_integral
        memberIntegral,m.size,m.direct
        FROM major_price mp
        INNER JOIN merchant m ON mp.admin_id=m.admin_id AND mp.direct=m.direct AND mp.area_no=m.area_no
        <where>
            <if test="sku != null">
                AND mp.sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND mp.area_no = #{areaNo}
            </if>
        </where>
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MajorPrice">
        update major_price
        <set>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="pdName != null">
                pd_name = #{pdName,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null">
                area_no = #{areaNo,jdbcType=INTEGER},
            </if>
            <if test="adminId != null">
                admin_id = #{adminId,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="areaName != null">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="direct != null">
                direct = #{direct,jdbcType=INTEGER},
            </if>
            <if test="payMethod != null">
                pay_method = #{payMethod,jdbcType=TINYINT},
            </if>
            <if test="validTime != null">
                valid_time = #{validTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invalidTime != null">
                invalid_time = #{invalidTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mallShow != null">
                mall_show = #{mallShow}
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectLowPriceRemainder" resultType="net.summerfarm.model.vo.MajorPriceLowRemainder">
        select mp.id,
               mp.sku,
               mp.pd_name        pdName,
               mp.weight,
               mp.area_no        areaNo,
               mp.admin_id       adminId,
               mp.price,
               mp.area_name      areaName,
               mp.valid_time     validTime,
               mp.invalid_time   invalidTime,
               mp.mall_show      mallShow,
               ak.original_price originalPrice,
               ak.price          mallPrice
        from major_price mp
                 left join area_sku ak on mp.area_no = ak.area_no and mp.sku = ak.sku
        where mp.admin_id = #{adminId}
          and mp.area_no = #{areaNo}
          and ak.on_sale = 1
          and mp.mall_show = 0
          and mp.invalid_time > now()
          and ak.m_type = 0
    </select>

    <select id="selectLowPriceRemainderSku" resultType="net.summerfarm.model.vo.MajorPriceLowRemainder">
        select mp.id,
               mp.sku,
               mp.pd_name        pdName,
               mp.weight,
               mp.area_no        areaNo,
               mp.admin_id       adminId,
               mp.price,
               mp.area_name      areaName,
               mp.valid_time     validTime,
               mp.invalid_time   invalidTime,
               mp.mall_show      mallShow,
               mp.price_type     priceType,
               mp.interest_rate     interestRate,
               mp.fixed_price     fixedPrice,
               ak.original_price originalPrice,
               ak.price          mallPrice
        from major_price mp
                 left join area_sku ak on mp.area_no = ak.area_no and mp.sku = ak.sku
        <where>
          ak.on_sale = 1

          and mp.invalid_time > now()
          and ak.m_type = 0
          <if test="adminId != null">
            and mp.admin_id = #{adminId}
          </if>
          <if test="areaNo != null">
            and mp.area_no = #{areaNo}
          </if>
          <if test="sku != null">
            and mp.sku = #{sku}
          </if>
        </where>
    </select>

    <update id="updatePriceById">
        update major_price
        set price = #{price}
        where id = #{id}
    </update>



    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM major_price mp
        where mp.id = #{id}

    </select>
    <select id="lowerCostWhenCostChange" resultType="net.summerfarm.model.vo.CostChangeVo">
        select mp.area_no   as        areaNo,
               ROUND(
                       CASE
                           WHEN mp.price_type IS NULL THEN 0
                           WHEN mp.price_type = 0 THEN COALESCE(ask.price, 0)
                           WHEN mp.price_type = 1 THEN COALESCE(mp.price, 0)
                           WHEN mp.price_type = 2 THEN COALESCE(mp.price, 0)
                           WHEN mp.price_type = 3 THEN COALESCE(ask.price, 0) + COALESCE(ask.price, 0) * COALESCE(mp.price_adjustment_value, 0) / 100
                           WHEN mp.price_type = 4 THEN COALESCE(ask.price, 0) - COALESCE(ask.price, 0) * COALESCE(mp.price_adjustment_value, 0) / 100
                           WHEN mp.price_type = 5 THEN COALESCE(ask.price, 0) + COALESCE(mp.price_adjustment_value, 0)
                           WHEN mp.price_type = 6 THEN COALESCE(ask.price, 0) - COALESCE(mp.price_adjustment_value, 0)
                           ELSE COALESCE(mp.price, 0) -- 默认情况，返回原始价格或 0
                           END,
                       2 -- 保留两位小数
                   ) AS price,
           mp.sku,
           mp.admin_id as        adminId,
           mp.price_type         priceType,
           interest_rate         interestRate,
           fixed_price           fixedPrice
        from major_price mp
             left join area_sku ask on mp.sku = ask.sku and mp.area_no = ask.area_no
             left join admin m on mp.admin_id = m.admin_id
        where mp.sku = #{sku} and mp.mall_show = 0 and m.is_disabled != 1
             and mp.area_no in (select f.area_no
                                from warehouse_inventory_mapping wim
                                left join fence f on f.store_no = wim.store_no
                                where wim.sku = #{sku} and f.status = 0 and wim.warehouse_no = #{warehouseNo})
             and now() between valid_time and invalid_time and mp.status = 1

    </select>

    <select id="countAutoUpdateMajorPrice" resultType="java.lang.Integer">
        select count(1) from major_price mp
        left join area_sku ak on mp.area_no = ak.area_no and mp.sku = ak.sku
        left join admin a on a.admin_id=mp.admin_id
        <where>
            <include refid="autoUpdateMajorPriceCondition"/>
        </where>
    </select>

    <select id="selectAutoUpdateMajorPrice" resultType="net.summerfarm.model.vo.MajorPriceAutoUpdateVO">
        select
            mp.id, mp.sku, mp.pd_name pdName,mp.weight,mp.area_no areaNo,mp.admin_id adminId, mp.price,mp. area_name areaName,mp.interest_rate interestRate,
               mp.fixed_price fixedPrice,mp.cost,a.major_cycle majorCycle,mp.price_type priceType
        from major_price mp
        left join area_sku ak on mp.area_no = ak.area_no and mp.sku = ak.sku
        left join admin a on a.admin_id=mp.admin_id
        <where>
            <include refid="autoUpdateMajorPriceCondition"/>
        </where>
    </select>
    <select id="selectPriceById" resultType="net.summerfarm.model.domain.MajorPrice">
        select  ROUND(
                        CASE
                            WHEN mp.price_type IS NULL THEN 0
                            WHEN mp.price_type = 0 THEN COALESCE(ask.price, 0)
                            WHEN mp.price_type = 1 THEN COALESCE(mp.price, 0)
                            WHEN mp.price_type = 2 THEN COALESCE(mp.price, 0)
                            WHEN mp.price_type = 3 THEN COALESCE(ask.price, 0) + COALESCE(ask.price, 0) * COALESCE(mp.price_adjustment_value, 0) / 100
                            WHEN mp.price_type = 4 THEN COALESCE(ask.price, 0) - COALESCE(ask.price, 0) * COALESCE(mp.price_adjustment_value, 0) / 100
                            WHEN mp.price_type = 5 THEN COALESCE(ask.price, 0) + COALESCE(mp.price_adjustment_value, 0)
                            WHEN mp.price_type = 6 THEN COALESCE(ask.price, 0) - COALESCE(mp.price_adjustment_value, 0)
                            ELSE COALESCE(mp.price, 0) -- 默认情况，返回原始价格或 0
                            END,
                        2 -- 保留两位小数
                    ) AS price,mp.area_no areaNo,mp.sku,
                            mp.price_type priceType,mp.interest_rate interestRate,mp.fixed_price fixedPrice,mp.admin_id adminId
        from major_price mp left join area_sku ask on mp.sku = ask.sku and mp.area_no = ask.area_no
        where mp.id = #{id}

    </select>

    <sql id="autoUpdateMajorPriceCondition">
        mp.mall_show = 0
        and price_type = 2
        and mp.valid_time <![CDATA[<=]]> now()
        and mp.invalid_time <![CDATA[>]]> now() and mp.status = 1
        <if test="adminId !=null">
            and mp.admin_id = #{adminId}
        </if>
        <if test="areaNo !=null">
            and mp.area_no = #{areaNo}
        </if>
        <if test="sku !=null">
            and mp.sku = #{sku}
        </if>
        <if test="majorCycle !=null">
            and a.major_cycle = #{majorCycle}
        </if>
        <if test="pageIndex !=null and pageSize !=null">
            limit #{pageIndex},#{pageSize}
        </if>
    </sql>

    <update id="updatePriceAndCostById">
        update major_price set price = #{price},cost = #{cost} where id = #{id}
    </update>

    <select id="selectByInvalidStatus" parameterType="net.summerfarm.model.domain.MajorPrice"
            resultType="net.summerfarm.model.vo.MajorPriceVO">
        SELECT
            id,sku,pd_name pdName,weight,area_no areaNo,admin_id adminId,price,area_name areaName,direct, pay_method payMethod,
            valid_time validTime, invalid_time invalidTime,mp.mall_show mallShow,mp.price_type priceType,mp.interest_rate
            interestRate,mp.fixed_price fixedPrice,mp.cost,mp.original_price originalPrice
        FROM major_price mp
        WHERE mp.admin_id=#{adminId}
        AND mp.sku = #{sku}
        AND mp.area_no = #{areaNo}
        AND mp.direct=#{direct}
        <if test="validStatus != null">
            <if test="validStatus == 0">
                AND mp.valid_time <![CDATA[>]]> now()
            </if>
            <if test="validStatus == 1">
                AND mp.valid_time <![CDATA[<=]]> now()
                AND mp.invalid_time <![CDATA[>]]> now()
            </if>
            <if test="validStatus == 2">
                AND mp.invalid_time <![CDATA[<=]]> now()
                order by mp.invalid_time desc limit 1
            </if>
        </if>
    </select>

  <select id="selectValidMajorPrice" resultType="net.summerfarm.model.domain.MajorPrice">
    select m.id, m.sku, m.area_no areaNo, m.admin_id adminId, m.price, m.price_type priceType
    from major_price m
           left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
    where m.sku = #{sku}
      and m.area_no = #{areaNo}
      and m.admin_id = #{adminId}
      and m.direct = #{direct}
      and ak.on_sale = 1
      and m.valid_time <![CDATA[<=]]> now()
      and m.invalid_time <![CDATA[>]]> now()
  </select>


    <select id="selectSalePrice" resultType="net.summerfarm.model.input.MajorPriceInput">
        SELECT IFNULL(ak.price,i.sale_price) salePrice,a.area_no areaNo, ak.on_sale onSale, ak.show
        FROM inventory i
                 LEFT JOIN area_sku ak ON i.sku=ak.sku
                 LEFT JOIN area a ON ak.area_no=a.area_no
        WHERE a.area_no=#{areaNo}
          AND i.sku=#{sku}
    </select>

    <select id="queryListMajorPrice"
            resultType="net.summerfarm.model.domain.MajorPriceDO">
        select m.id, m.sku, m.area_no areaNo, m.admin_id adminId,  min(m.price) minPrice, max(m.price) maxPrice, m.price_type priceType
        from major_price m
        left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
        where m.sku = #{sku}
        and m.admin_id = #{adminId}
        and ak.on_sale = 1
        and m.price_type != 0
        and m.valid_time <![CDATA[<=]]> now()
        and m.invalid_time <![CDATA[>]]> now()
        and m.area_no in
        <foreach collection="areaNos" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by m.area_no
    </select>

  <select id="getMajorPrice" resultType="net.summerfarm.model.domain.MajorPrice">
    select m.id, m.sku, m.area_no areaNo, m.admin_id adminId, m.price, m.price_type priceType,
    m.pay_method payMethod, m.valid_time validTime, m.invalid_time invalidTime, m.mall_show mallShow
    from major_price m
    left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
    where m.sku = #{sku}
    and m.area_no = #{areaNo}
    and m.admin_id = #{adminId}
    and m.direct = #{direct}
    and ak.on_sale = 1
    and m.valid_time <![CDATA[<=]]> now()
    and m.invalid_time <![CDATA[>]]> now()
    and m.status = 1
  </select>

  <select id="getMajorPriceByAreaNoList" resultType="net.summerfarm.model.domain.MajorPrice">
    select m.id, m.sku, m.area_no areaNo, m.admin_id adminId, m.price, m.price_type priceType,m.direct,
    m.pay_method payMethod, m.valid_time validTime, m.invalid_time invalidTime, m.mall_show mallShow
    from major_price m
    left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
    where m.admin_id = #{adminId}
    and ak.on_sale = 1
    <if test="direct != null">
      and m.direct = #{direct}
    </if>
    <if test="skuList != null and skuList.size() > 0">
      and m.sku in
      <foreach collection="skuList" open="(" close=")" separator="," item="sku">
        #{sku}
      </foreach>
    </if>
    <if test="areaNoList != null and areaNoList.size() > 0">
      and m.area_no in
      <foreach collection="areaNoList" open="(" close=")" separator="," item="areaNo">
        #{areaNo}
      </foreach>
    </if>
    and m.valid_time <![CDATA[<=]]> now()
    and m.invalid_time <![CDATA[>]]> now()
    and m.status = 1
</select>

  <select id="listAllGrossProfitMajorPrice" resultType="net.summerfarm.model.vo.MajorPriceAutoUpdateVO">
    select
      mp.id, mp.sku, mp.pd_name pdName,mp.weight,mp.area_no areaNo,mp.admin_id adminId, mp.price,mp. area_name areaName,mp.interest_rate interestRate,
      mp.fixed_price fixedPrice,mp.cost,a.major_cycle majorCycle,mp.price_type priceType
    from major_price mp
    left join area_sku ak on mp.area_no = ak.area_no and mp.sku = ak.sku
    left join admin a on a.admin_id=mp.admin_id
    where
    mp.mall_show = 0
    and price_type = 2 and mp.status = 1
    and mp.valid_time <![CDATA[<=]]> now()
    and mp.invalid_time <![CDATA[>]]> now()
    and a.major_cycle = #{majorCycle}
    limit #{offset}, #{limit}
  </select>


    <select id="selectValidMajorPriceByAreaNo" resultType="long">
        select distinct mp.id
        from major_price mp
        <where>
            mp.invalid_time > now()
            and mp.valid_time <![CDATA[<=]]> now()
            and mp.area_no = #{areaNo}
        </where>
    </select>

</mapper>
