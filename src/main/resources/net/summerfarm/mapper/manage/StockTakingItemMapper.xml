<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTakingItemMapper">
    <resultMap id="withDetail" type="net.summerfarm.model.vo.StockTakingItemVO">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="sku" property="sku" jdbcType="VARCHAR" />
        <result column="pdName" property="pdName" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="storeQuantity" property="storeQuantity" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="state" property="state" jdbcType="INTEGER" />
        <result column="storage_location" property="storageLocation" jdbcType="INTEGER" />
        <result column="name_remakes" property="nameRemakes"/>
        <result column="type" property="skuType"/>
        <result column="extType" property="extType"/>
        <collection property="stockTakingListDetails" ofType="net.summerfarm.model.domain.StockTakingListDetail">
            <result column="batch" property="batch" jdbcType="VARCHAR"/>
            <result column="qualityDate" property="qualityDate" jdbcType="DATE" />
            <result column="productionDate" property="productionDate" jdbcType="DATE" />
            <result column="quantity" property="quantity" jdbcType="INTEGER"/>
            <result column="realQuantity" property="realQuantity" jdbcType="INTEGER" />
            <result column="reason" property="reason" jdbcType="VARCHAR" />
            <result column="glNo" property="glNo" jdbcType="VARCHAR" />
        </collection>
    </resultMap>

    <select id="selectUnTakingItem" resultMap="withDetail">
        SELECT sti.id,sti.sku,sti.pd_name pdName,sti.weight,sti.state
        FROM stocktaking st
        INNER JOIN stock_taking_item sti ON st.id=sti.taking_id AND sti.state=0
        <where>
            <if test="takingId != null">
                AND st.id=#{takingId}
            </if>
            <if test="pdName != null">
                AND sti.pd_name LIKE concat('%',#{pdName},'%')
            </if>
            <if test="sku != null">
                AND sti.sku = #{sku}
            </if>
        </where>
    </select>

    <select id="selectTakingItem"  resultMap="withDetail">
        SELECT sti.id,sti.sku,sti.pd_name pdName,sti.weight,sti.store_quantity storeQuantity,sti.state,ar.`status`,stld.batch,
	      stld.quality_date qualityDate,stld.quantity,stld.real_quantity realQuantity,stld.reason,stld.production_date productionDate,
        ad.name_remakes,stld.gl_no glNo,i.type,i.ext_type extType
        FROM stocktaking st
        INNER JOIN stock_taking_item sti ON st.id=sti.taking_id AND sti.state IN (1,2,3)
        INNER JOIN warehouse_stock_ext ar ON st.area_no=ar.warehouse_no AND sti.sku=ar.sku
        LEFT JOIN stock_taking_list stl ON st.id=stl.taking_id
        LEFT JOIN stock_taking_list_detail stld ON stl.id=stld.stock_taking_list_id AND sti.sku=stld.sku
        LEFT JOIN inventory i on i.sku = sti.sku
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        <where>
            <if test="takingId != null">
                AND st.id = #{takingId}
            </if>
            <if test="pdName != null">
                AND sti.pd_name LIKE concat('%',#{pdName},'%')
            </if>
            <if test="sku != null">
                AND sti.sku = #{sku}
            </if>
            <if test="diff != null">
                <choose>
                    <when test="diff">
                        AND ( stld.quantity != stld.real_quantity OR stld.quantity IS NULL )
                    </when>
                    <otherwise>
                        AND stld.quantity = stld.real_quantity
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.StockTakingItem">
        INSERT INTO stock_taking_item(taking_id,sku,pd_name,weight,store_quantity,state,gl_no)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.takingId},#{item.sku},#{item.pdName},#{item.weight},#{item.storeQuantity},#{item.state},#{item.glNo})
        </foreach>
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.StockTakingItem">
        SELECT id,taking_id takingId,sku,pd_name pdName,weight,store_quantity storeQuantity,state,gl_no glNo
        FROM stock_taking_item
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <update id="updateByPrimary" parameterType="net.summerfarm.model.domain.StockTakingItem">
        UPDATE stock_taking_item
        <set>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="storeQuantity != null">
                store_quantity = #{storeQuantity},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByTakingId" parameterType="net.summerfarm.model.domain.StockTakingItem">
        UPDATE stock_taking_item
        <set>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="storeQuantity != null">
                store_quantity = #{storeQuantity},
            </if>
        </set>
        WHERE taking_id = #{takingId}
    </update>

    <update id="updateState">
        UPDATE stock_taking_item
        SET store_quantity = NULL , state = 0
        WHERE taking_id = #{takingId}
        AND sku = #{sku}
    </update>

    <select id="selectByStockTakingListId" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.StockTakingItem">
      SELECT DISTINCT sti.id,sti.taking_id takingId,sti.sku,sti.pd_name pdName,sti.weight,sti.store_quantity storeQuantity,sti.state
        FROM stock_taking_item sti
        INNER JOIN stock_taking_list stl ON sti.taking_id=stl.taking_id
        INNER JOIN stock_taking_list_detail stld ON stl.id=stld.stock_taking_list_id AND sti.sku=stld.sku
        WHERE stl.id=#{stockTakingListId,jdbcType=INTEGER}
    </select>

    <select id="selectUnFinishSku" parameterType="net.summerfarm.model.domain.StockTakingItem" resultType="net.summerfarm.model.domain.StockTakingItem">
        SELECT id,taking_id takingId,sku,pd_name pdName,weight,store_quantity storeQuantity,state
        FROM stock_taking_item
        WHERE state IN (0,1,2)
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="takingId != null">
            AND taking_id = #{takingId}
        </if>
    </select>
    

    <select id="selectByTakingId" resultType="net.summerfarm.model.domain.StockTakingItem">
        select sti.id,sti.taking_id takingId,sti.sku,sti.pd_name pdName,sti.weight,sti.store_quantity storeQuantity,sti.state,sti.gl_no glNo
         from stock_taking_item  sti
         where sti.taking_id  = #{id}
    </select>

</mapper>