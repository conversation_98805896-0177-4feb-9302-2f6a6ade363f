<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ScheduleTaskLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ScheduleTaskLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="result" jdbcType="INTEGER" property="result" />
    <result column="executor" jdbcType="VARCHAR" property="executor" />
    <result column="exception_info" jdbcType="VARCHAR" property="exceptionInfo" />
  </resultMap>
  <resultMap id="VOMap" type="net.summerfarm.model.vo.ScheduleTaskLogVO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="result" jdbcType="INTEGER" property="result" />
    <result column="executor" jdbcType="VARCHAR" property="executor" />
    <result column="exception_info" jdbcType="VARCHAR" property="exceptionInfo" />
    <result column="name" jdbcType="VARCHAR" property="name"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, task_id, start_time, end_time, `result`, executor, exception_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from schedule_task_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from schedule_task_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ScheduleTaskLog" useGeneratedKeys="true">
    insert into schedule_task_log (task_id, start_time, end_time, 
      `result`, executor, exception_info
      )
    values (#{taskId,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{result,jdbcType=INTEGER}, #{executor,jdbcType=VARCHAR}, #{exceptionInfo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ScheduleTaskLog" useGeneratedKeys="true">
    insert into schedule_task_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="result != null">
        `result`,
      </if>
      <if test="executor != null">
        executor,
      </if>
      <if test="exceptionInfo != null">
        exception_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="result != null">
        #{result,jdbcType=INTEGER},
      </if>
      <if test="executor != null">
        #{executor,jdbcType=VARCHAR},
      </if>
      <if test="exceptionInfo != null">
        #{exceptionInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ScheduleTaskLog">
    update schedule_task_log
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="result != null">
        `result` = #{result,jdbcType=INTEGER},
      </if>
      <if test="executor != null">
        executor = #{executor,jdbcType=VARCHAR},
      </if>
      <if test="exceptionInfo != null">
        exception_info = #{exceptionInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ScheduleTaskLog">
    update schedule_task_log
    set task_id = #{taskId,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `result` = #{result,jdbcType=INTEGER},
      executor = #{executor,jdbcType=VARCHAR},
      exception_info = #{exceptionInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="list" resultMap="VOMap">
    select
        st.name, stl.*
    from schedule_task_log stl left join schedule_task st on stl.task_id = st.id
    <where>
        <if test="taskId != null">
            stl.task_id = #{taskId}
        </if>
        <if test="date != null">
            date(stl.start_time) = #{date}
        </if>
    </where>
    order by stl.id desc
  </select>
</mapper>