<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SupplierConnectMapper">

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.SupplierConnect" useGeneratedKeys="true"
            keyProperty="id">
        insert into supplier_connect (supplier_id, name,position,
        phone,admin_name)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.supplierId},#{item.name},
            #{item.position},#{item.phone},#{item.adminName})
        </foreach>

    </insert>

    <insert id="insert" parameterType="net.summerfarm.model.domain.SupplierConnect" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_connect
        <trim prefix="(" suffix=")"  suffixOverrides=",">
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="position != null">
                position,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="adminName != null">
                admin_name,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="supplierId != null">
                #{supplierId},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="position != null">
                #{position},
            </if>
            <if test="phone != null">
                #{phone},
            </if>
            <if test="adminName != null">
                #{adminName},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="net.summerfarm.model.domain.SupplierConnect">
        update supplier_connect
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="position != null">
                position = #{position},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="adminName != null">
                admin_name = #{adminName},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="delete" parameterType="java.lang.Integer">
        delete FROM supplier_connect
        WHERE supplier_id=#{supplierId}
    </update>

    <select id="selectBySupplierId" parameterType="java.lang.Integer"
            resultType="net.summerfarm.model.domain.SupplierConnect">
        SELECT id,supplier_id supplierId,name,position,phone,admin_name adminName
        FROM supplier_connect
        WHERE supplier_id=#{supplierId}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete FROM supplier_connect
        WHERE id=#{id}
    </delete>
    <delete id="removeBySupplierId">
        delete FROM supplier_connect
        WHERE supplier_id = #{supplierId}
    </delete>
</mapper>