<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTakingListMapper">

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockTakingList" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO stock_taking_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="takingId != null">
                taking_id,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="handler != null">
                handler,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="updatetime != null">
                updatetime,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
        </trim>
        <trim prefix="VALUES ( " suffix=")"  suffixOverrides=",">
            <if test="takingId != null">
                #{takingId},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="handler != null">
                #{handler},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="updatetime != null">
                #{updatetime},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
        </trim>
    </insert>

    <delete id="delete" parameterType="java.lang.Integer">
        DELETE
        FROM  stock_taking_list
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <select id="select" parameterType="net.summerfarm.model.vo.StockTakingListVO"
            resultType="net.summerfarm.model.vo.StockTakingListVO">
        SELECT DISTINCT st2.id stockTaskId,stl.id,st.area_no areaNo,st.type,stl.`status`,stl.addtime,stl.updatetime,stl.`handler`,a.realname adminName,st2.dimension,st.id stockTakingNo
        FROM stock_taking_list stl
        INNER JOIN stocktaking st ON stl.taking_id=st.id
        INNER JOIN stock_task st2 ON st.stock_taking_no=st2.task_no
        LEFT JOIN stock_taking_list_detail stld ON stl.id=stld.stock_taking_list_id
        LEFT JOIN admin a ON stl.admin_id = a.admin_id
        <where>
            <if test="areaNo != null">
                AND st.area_no = #{areaNo}
            </if>
            <if test="stockTaskId != null">
                AND st2.id = #{stockTaskId}
            </if>
            <if test="dimension != null">
                AND st2.dimension = #{dimension}
            </if>
            <if test="type != null">
                AND st.type = #{type}
            </if>
            <if test="status != null">
                AND stl.status = #{status}
            </if>
            <if test="addtime != null">
                AND stl.addtime <![CDATA[>=]]> #{addtime}
            </if>
            <if test="pdName != null and pdName != ''">
                AND stld.pd_name LIKE concat('%',#{pdName},'%')
            </if>
        </where>
        ORDER BY stl.id DESC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.StockTakingListVO">
        SELECT stl.id,stl.taking_id takingId,stl.admin_id adminId,stl.`handler`,stl.`status`,stl.updatetime,stl.addtime,stl.remark,
	      a.realname adminName,a2.realname handlerName,st2.id stockTaskId,st.area_no areaNo,st.type,st.stock_taking_no stockTakingNo,st2.dimension
        FROM stock_taking_list stl
        LEFT JOIN admin a ON stl.admin_id=a.admin_id
        LEFT JOIN admin a2 ON stl.`handler`=a2.admin_id
        LEFT JOIN stocktaking st ON stl.taking_id=st.id
        LEFT JOIN stock_task st2 ON st.stock_taking_no=st2.task_no
        WHERE stl.id = #{id,jdbcType=INTEGER}
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StockTakingList" keyProperty="taking_id" useGeneratedKeys="true">
        UPDATE stock_taking_list
        <set>
            <if test="handler != null">
                handler = #{handler},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updatetime != null">
                updatetime = #{updatetime},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>


    <update id="updateByTakingId" parameterType="net.summerfarm.model.domain.StockTakingList">
        UPDATE stock_taking_list
        <set>
            <if test="handler != null">
                handler = #{handler},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updatetime != null">
                updatetime = #{updatetime},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
        </set>
       where id = #{id}
    </update>

    <select id="selectBySku"  resultType="net.summerfarm.model.domain.StockTakingList">
        SELECT  st2.id stockTaskId,stl.id,st.type,stl.`status`,stl.addtime,stl.updatetime,stl.`handler`
        FROM stock_taking_list stl
        INNER JOIN stocktaking st ON stl.taking_id=st.id
        INNER JOIN stock_task st2 ON st.stock_taking_no=st2.task_no
        LEFT JOIN stock_taking_list_detail stld ON stl.id=stld.stock_taking_list_id
        where
        stld.sku =#{sku}
        and stl.status in (0,1)
        and st2.area_no=#{areaNo}
        limit 1
    </select>

</mapper>