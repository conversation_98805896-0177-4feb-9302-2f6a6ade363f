<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.StrictSelectionMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StrictSelection">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="background_pic" jdbcType="VARCHAR" property="backgroundPic" />
    <result column="head_pic" jdbcType="VARCHAR" property="headPic" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="auto_sort" jdbcType="BOOLEAN" property="autoSort" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, background_pic, head_pic, start_time, end_time, auto_sort, updater, update_time, 
    creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from strict_selection
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from strict_selection
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.StrictSelection" useGeneratedKeys="true">
    insert into strict_selection (background_pic, head_pic, start_time, 
      end_time, auto_sort, updater, 
      update_time, creator, create_time
      )
    values (#{backgroundPic,jdbcType=VARCHAR}, #{headPic,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{autoSort,jdbcType=BOOLEAN}, #{updater,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.StrictSelection" useGeneratedKeys="true">
    insert into strict_selection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="backgroundPic != null">
        background_pic,
      </if>
      <if test="headPic != null">
        head_pic,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="autoSort != null">
        auto_sort,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="backgroundPic != null">
        #{backgroundPic,jdbcType=VARCHAR},
      </if>
      <if test="headPic != null">
        #{headPic,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoSort != null">
        #{autoSort,jdbcType=BOOLEAN},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.StrictSelection">
    update strict_selection
    <set>
      <if test="backgroundPic != null">
        background_pic = #{backgroundPic,jdbcType=VARCHAR},
      </if>
      <if test="headPic != null">
        head_pic = #{headPic,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoSort != null">
        auto_sort = #{autoSort,jdbcType=BOOLEAN},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StrictSelection">
    update strict_selection
    set background_pic = #{backgroundPic,jdbcType=VARCHAR},
      head_pic = #{headPic,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      auto_sort = #{autoSort,jdbcType=BOOLEAN},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByModel" resultMap="BaseResultMap">
    select id,
           background_pic,
           head_pic,
           start_time,
           end_time,
           auto_sort,
           updater,
           update_time,
           creator,
           create_time
    from strict_selection ss
    <where>
      <if test="areaName != null">
        and ss.id in (
            select soa.series_id from series_of_area soa
            inner join area a on soa.area_no = a.area_no
            where soa.series_type = 0 and a.area_name like concat('%', #{areaName} ,'%')
        )
      </if>
      <if test="status != null">
          <if test="status == 0">
            and ss.start_time &gt; now()
          </if>
          <if test="status == 1">
            and ss.start_time &lt;= now()
            and ss.end_time &gt;= now()
          </if>
          <if test="status == 2">
            and ss.end_time &lt; now()
          </if>
      </if>
      <if test="autoSort != null">
        and ss.auto_sort = #{autoSort}
      </if>
    </where>
    order by id desc
  </select>
  <select id="selectFirstTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strict_selection
    where id in
          <foreach collection="ids" item="id" close=")" open="(" separator=",">
          #{id}
        </foreach>
    order by create_time asc limit 1
  </select>
    <select id="selectCurrentStrict" resultType="java.lang.Integer">
      select distinct ss.id from strict_selection ss left join series_of_area soa on ss.id = soa.series_id and series_type = 0
      where
       soa.area_no in
       <foreach collection="areaNos" item="areaNo" close=")" open="(" separator=",">
         #{areaNo}
       </foreach>
        and ss.start_time = #{startTime} and ss.end_time = #{endTime}
        <if test="id!=null">
          and ss.id &lt;&gt; #{id}
        </if>
    </select>
</mapper>