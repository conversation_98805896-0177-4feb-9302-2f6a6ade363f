<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.OrdersMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.vo.OrderVO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
        <result column="mcontact" property="mcontact" jdbcType="VARCHAR"/>
        <result column="mphone" property="mphone" jdbcType="VARCHAR"/>
        <result column="mname" jdbcType="VARCHAR" property="mname"/>
        <result column="money" jdbcType="DECIMAL" property="money"/>
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="contact_id" jdbcType="INTEGER" property="contactId"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="end_time" property="endTime"/>
        <result column="admin_id" property="adminId"/>
        <result column="invoice_status" property="invoiceStatus"/>
        <result column="financial_invoice_id" property="financialInvoiceId"/>
        <result column="after_sale_order_no" jdbcType="VARCHAR" property="afterSaleOrderNo"/>
    </resultMap>

    <select id="selectSalePrice" resultType="net.summerfarm.model.vo.SalePricesVO">
        select o.order_time orderTime , oi.price, oi.amount
        from order_item oi,orders o,merchant m
        where oi.order_no = o.order_no
        AND m.m_id = o.m_id
        AND o.status in (2,3,6)
        and oi.sku = #{sku}
        and m.area_no = #{areaNo}
        AND o.order_time >= #{startTime}
        AND o.order_time <![CDATA[<]]> #{endTime}
    </select>

    <!--截单报表-->
    <resultMap id="ClosingOrderMap" type="net.summerfarm.model.vo.ClosingOrder">
        <id column="orderId" property="orderId" jdbcType="BIGINT"/>
        <id column="contact_id" property="contactId" jdbcType="INTEGER"/>
        <result column="orderNo" property="orderNo" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="userName" property="userName" jdbcType="VARCHAR"/>
        <result column="contact" property="contact" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="deliveryAddress" property="deliveryAddress" jdbcType="VARCHAR"/>
        <result column="timeFrame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="couponMoney" property="couponMoney" jdbcType="DECIMAL"/>
        <result column="totalPay" property="totalPay" jdbcType="DECIMAL"/>
        <result column="deliveryFee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="out_times_fee" property="outTimesFee" jdbcType="DECIMAL"/>
        <result column="deliveryTime" property="deliveryTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="deliveryCar" property="deliveryCar" jdbcType="VARCHAR"/>
        <result column="BD" property="BD" jdbcType="VARCHAR"/>
        <result column="adminId" property="adminId" jdbcType="INTEGER"/>
        <result column="orderTime" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="areaNo" property="areaNo" jdbcType="INTEGER"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="prePath" property="prePath" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="orderType" property="orderType" jdbcType="INTEGER"/>
        <result column="showPrice" property="showPrice" jdbcType="BIT"/>
        <result column="totalVolume" property="totalVolume" jdbcType="DECIMAL"/>
        <result column="distance" property="distance" jdbcType="DECIMAL"/>
        <result column="deliveryType" property="deliveryType" jdbcType="DECIMAL"/>
        <result column="deliverytype" property="isSelf" jdbcType="INTEGER"/>
        <result column="sku_sorting" property="skuSorting"/>
        <result column="ht_order_code" property="htOrderCode"/>
        <result column="brandType" property="brandType"/>
        <result column="outerBrandName" property="outerBrandName"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="store_id" property="storeId"/>
        <result column="saasFlag" property="saasFlag"/>
        <result column="sendRemark" property="sendRemark"/>
        <result column="endSiteId" property="endSiteId"/>
        <result column="storeNo" property="storeNo"/>
        <result column="orderRemark" property="orderRemark"/>
        <collection property="orderItemVOs" ofType="net.summerfarm.model.vo.OrderItemVO">
            <id column="id" property="id" jdbcType="BIGINT"/>
            <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
            <result column="amount" property="amount" jdbcType="VARCHAR"/>
            <result column="unit" property="unit" jdbcType="VARCHAR"/>
            <result column="weight" property="weight" jdbcType="VARCHAR"/>
            <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
            <result column="salePrice" property="salePrice" jdbcType="DECIMAL"/>
            <result column="sku" property="sku" jdbcType="VARCHAR"/>
            <result column="storage_location" property="storageLocation" jdbcType="TINYINT"/>
            <result column="categoryId" property="categoryId" jdbcType="INTEGER"/>
            <result column="volume" property="volume" jdbcType="VARCHAR"/>
            <result column="name_remakes" property="nameRemakes"  jdbcType="VARCHAR"/>
            <result column="category" property="category" jdbcType="VARCHAR"/>
            <result column="parentCategory" property="parentCategory" jdbcType="VARCHAR"/>
            <result column="price" property="price" jdbcType="DECIMAL"/>
            <result column="skuType" property="skuType" jdbcType="INTEGER"/>
            <result column="originalPrice" property="originalPrice" jdbcType="DECIMAL"/>
            <result column="categoryType" property="categoryType" jdbcType="INTEGER"/>
            <result column="oi_order_no" property="orderNo" jdbcType="VARCHAR"/>
            <result column="orderItemDeliveryType" property="orderItemDeliveryType"/>
            <result column="productType" property="productType"/>
            <result column="orderType" property="orderType" jdbcType="INTEGER"/>
            <result column="extType" property="extType" jdbcType="INTEGER"/>
            <result column="weight_num" property="weightNum" jdbcType="DECIMAL"/>
            <result column="warehouseNo" property="warehouseNo" jdbcType="INTEGER"/>
            <result column="pdId" property = "pdId" jdbcType="VARCHAR"></result>
            <result column="pack_type" property = "packType" jdbcType="INTEGER"></result>
        </collection>
    </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.model.vo.OrderVO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="itemSum" property="itemSum" jdbcType="DECIMAL"/>
        <result column="itemCount" property="itemCount" jdbcType="INTEGER"/>
        <result column="m_id" property="mId" jdbcType="DECIMAL"/>
        <collection property="orderItemVOs" ofType="net.summerfarm.model.vo.OrderItemVO" column="order_no" select="selectItem">
            <result column="id" property="id" jdbcType="BIGINT" />
            <result column="sku" property="sku" jdbcType="VARCHAR" />
            <result column="itemAmount" property="itemAmount" jdbcType="DECIMAL" />
            <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        </collection>
    </resultMap>

    <select id="selectClosingOrder" resultMap="ClosingOrderMap">
        SELECT o.order_id orderId, o.order_no orderNo, m.mname userName, m.m_id, con.contact contact, dp.delivery_time
        deliveryTime,m.show_price showPrice,dp.deliverytype ,
        con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,o.total_price totalPay ,
        o.delivery_fee deliveryFee,o.out_times_fee, o.remark,o.remark as orderRemark,o.type orderType,oi.category_id categoryId,
        oi.id id,oi.pd_name , if(o.type=1,dp.quantity,oi.amount) amount, oi.original_price salePrice, i.unit,oi.price,c1.type categoryType,i.pd_id pdId,
        oi.weight,oi.maturity,oi.sku,con.delivery_car deliveryCar,oi.volume,m.admin_id adminId,
        oi.storage_location,con.contact_id,dp.time_frame timeFrame, m.area_no areaNo,dep.path,dep.sort,con.path prePath,ad.name_remakes,ad2.sku_sorting,
        dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,o.order_time orderTime,oi.order_no oi_order_no,oi.product_type productType,
        i.type skuType,ad.name_remakes nameRemakes,i.ext_type extType,oht.ht_order_code,i.weight_num,wip.warehouse_no warehouseNo,ad2.name_remakes outerBrandName,0 brandType,
        dp.order_store_no as storeNo
        FROM ( SELECT * FROM  delivery_plan
               WHERE  delivery_time  <![CDATA[ >= ]]>  #{startDate}
                        AND delivery_time  <![CDATA[<]]>  #{endDate}
                        AND status in (3,6)
                        AND show_flag = 0
                        <if test="orderNo != null">
                            AND order_no=#{orderNo}
                        </if>
            ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        LEFT JOIN order_hey_tea oht on oht.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12,30)
        <if test="orderNo != null">
            AND o.order_no=#{orderNo}
        </if>
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        LEFT JOIN contact con on dp.contact_id=con.contact_id
        LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time AND dep.brand_type=0
        LEFT JOIN inventory i ON i.sku = oi.sku
        left join admin ad on ad.admin_id = i.admin_id
        left join admin ad2 on ad2.admin_id = m.admin_id
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        LEFT JOIN warehouse_inventory_mapping wip ON wip.sku=i.sku and wip.store_no=dp.order_store_no
        <where>
            <if test="deliverytype != null">
                AND dp.deliverytype=#{deliverytype}
            </if>
            <if test="areaNo != null">
                AND m.area_no=#{areaNo}
            </if>
            <if test="parentNo != null">
                AND dp.order_store_no = #{parentNo}
            </if>
        </where>
        order by dp.time_frame desc
    </select>

    <select id="selectTmsClosingOrder" resultMap="ClosingOrderMap">
        SELECT 	o.order_id orderId,
        o.order_no orderNo,
        m.mname userName,
        m.m_id,
        tdse.NAME contact,
        dp.outer_contact_id contact_id,
        dp.expect_begin_time deliveryTime,
        m.show_price showPrice,
        dp.type AS deliverytype,
        tdse.phone phone,
        ifnull(tdse.area,'') as area,
        CONCAT( tdse.provice, tdse.city, ifnull(tdse.area,''), tdse.address ) deliveryAddress,
        tdse.id endSiteId,
        o.total_price totalPay,
        o.delivery_fee deliveryFee,
        o.out_times_fee,
        o.remark,
        o.remark as orderRemark,
        o.type orderType,
        oi.category_id categoryId,
        oi.id id,
        oi.pd_name pd_name,
        IF
        ( o.type = 1, tdi.quantity, oi.amount ) amount,
        oi.original_price salePrice,
        i.unit,
        oi.price,
        c1.type categoryType,
        i.pd_id pdId,
        oi.weight,
        oi.maturity,
        oi.sku,
        i.volume,
        m.admin_id adminId,
        oi.storage_location,
        tdse.out_business_no,
        dp.time_frame timeFrame,
        m.area_no areaNo,
        tdb.path_code path,
        IF(tdb.path_id > 0 ,tdsite.sequence,null) sort,
        ad.name_remakes,
        ad2.sku_sorting,
        c1.category,
        c2.category parentCategory,
        o.order_time orderTime,
        oi.order_no oi_order_no,
        oi.product_type productType,
        i.type skuType,
        ad.name_remakes nameRemakes,
        i.ext_type extType,
        oht.ht_order_code,
        i.weight_num,
        wip.warehouse_no warehouseNo,
        0 brandType,
        tdsite.outer_brand_name outerBrandName,
        tdsite.send_remark sendRemark
        FROM ( 	SELECT
        *
        FROM
        tms_dist_order
        WHERE  expect_begin_time  <![CDATA[ = ]]>  #{deliveryTime}
        AND state NOT IN ( 18, 19 )
        AND source IN ( 200, 203 )
        ) dp
        LEFT JOIN tms_dist_site tdse ON tdse.id = dp.end_site_id
        LEFT JOIN tms_dist_site tdsb ON tdsb.id = dp.begin_site_id
        LEFT JOIN tms_delivery_order tdo ON tdo.dist_order_id = dp.id
        LEFT JOIN tms_delivery_batch tdb ON tdb.id = tdo.batch_id
        LEFT JOIN tms_delivery_site tdsite ON tdsite.delivery_batch_id = tdo.batch_id AND dp.end_site_id = tdsite.site_id
        INNER JOIN orders o ON dp.outer_order_id = o.order_no
        LEFT JOIN tms_dist_item tdi ON tdi.dist_order_id = dp.id AND dp.source = 203
        LEFT JOIN order_hey_tea oht on oht.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12,30)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        LEFT JOIN inventory i ON i.sku = oi.sku
        left join admin ad on ad.admin_id = i.admin_id
        left join admin ad2 on ad2.admin_id = m.admin_id
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        LEFT JOIN warehouse_inventory_mapping wip ON wip.sku=i.sku and wip.store_no=tdsb.out_business_no
        where
            tdsb.out_business_no = #{storeNo}
        order by dp.time_frame desc
    </select>

    <select id="selectClosingOrderByMid" resultMap="ClosingOrderMap">
        SELECT o.order_id orderId, o.order_no orderNo, m.mname userName, m.m_id, con.contact contact, dp.delivery_time
        deliveryTime,m.show_price showPrice,
        con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,o.total_price totalPay ,
        o.delivery_fee deliveryFee,o.out_times_fee, o.remark,o.type orderType,oi.category_id categoryId,
        oi.id id,oi.pd_name , if(o.type=1,dp.quantity,oi.amount) amount, oi.original_price salePrice, i.unit,oi.price,c1.type categoryType,i.pd_id pdId,
        oi.weight,oi.maturity,oi.sku,con.delivery_car deliveryCar,oi.volume,m.admin_id adminId,
        oi.storage_location,con.contact_id,dp.time_frame timeFrame, m.area_no areaNo,dep.path,dep.sort,con.path prePath,ad.name_remakes,ad2.sku_sorting,
        dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,o.order_time orderTime,oi.order_no oi_order_no,oi.product_type productType,
        i.type skuType,ad.name_remakes nameRemakes,i.ext_type extType,oht.ht_order_code,i.weight_num,0 brandType
        FROM ( SELECT * FROM  delivery_plan
        WHERE  delivery_time  <![CDATA[ >= ]]>  #{startDate}
        AND delivery_time  <![CDATA[<]]>  #{endDate}
        AND status in (3,6)
        AND show_flag = 0
        ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        LEFT JOIN order_hey_tea oht on oht.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12,30)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        LEFT JOIN contact con on dp.contact_id=con.contact_id
        LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time  AND dep.brand_type=0
        LEFT JOIN inventory i ON i.sku = oi.sku
        left join admin ad on ad.admin_id = i.admin_id
        left join admin ad2 on ad2.admin_id = m.admin_id
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        <where>
            <if test="deliverytype != null">
                AND dp.deliverytype=#{deliverytype}
            </if>
            <if test="mId != null">
                AND con.m_id=#{mId}
            </if>
        </where>
        order by dp.time_frame desc
    </select>

    <select id="selectClosingOrderNew" resultMap="ClosingOrderMap">
        SELECT o.order_id orderId, o.order_no orderNo, m.mname userName, m.m_id, con.contact contact, dp.delivery_time
        deliveryTime,m.show_price showPrice,
        con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,o.total_price totalPay ,
        o.delivery_fee deliveryFee,o.out_times_fee, o.remark,o.type orderType,oi.category_id categoryId,
        oi.id id,oi.pd_name , if(o.type=1,dp.quantity,oi.amount) amount, oi.original_price salePrice, i.unit,oi.price,c1.type categoryType,i.pd_id pdId,
        oi.weight,oi.maturity,oi.sku,con.delivery_car deliveryCar,oi.volume,m.admin_id adminId,
        oi.storage_location,con.contact_id,dp.time_frame timeFrame, m.area_no areaNo,dep.path,dep.sort,con.path prePath,ad.name_remakes,ad2.sku_sorting,
        dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,o.order_time orderTime,oi.order_no oi_order_no,oi.product_type productType,
        i.type skuType,ad.name_remakes nameRemakes,i.ext_type extType,oht.ht_order_code,0 as brandType
        FROM ( SELECT * FROM  delivery_plan
               WHERE  delivery_time  <![CDATA[ >= ]]>  #{startDate}
                        AND delivery_time  <![CDATA[<]]>  #{endDate}
                        AND status in (3,6)
                        <if test="interceptFlag != null">
                            AND intercept_flag = #{interceptFlag}
                        </if>
                        <if test="orderNo != null">
                            AND order_no=#{orderNo}
                        </if>
            ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        LEFT JOIN order_hey_tea oht on oht.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        <if test="orderNo != null">
            AND o.order_no=#{orderNo}
        </if>
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        LEFT JOIN contact con on dp.contact_id=con.contact_id
        LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time AND dep.brand_type= 0
        LEFT JOIN inventory i ON i.sku = oi.sku
        left join admin ad on ad.admin_id = i.admin_id
        left join admin ad2 on ad2.admin_id = m.admin_id
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        <where>
            <if test="deliverytype != null">
                AND dp.deliverytype=#{deliverytype}
            </if>
            <if test="areaNo != null">
                AND m.area_no=#{areaNo}
            </if>
            <if test="parentNo != null">
                AND dp.order_store_no = #{parentNo}
            </if>
        </where>
        order by dp.time_frame desc
    </select>

    <select id="selectSassClosingOrder" resultMap="ClosingOrderMap">
        SELECT
        'saas' saasFlag,
        dp.id orderId,
        f.area_no areaNo,
        dp.delivery_type deliveryType,
        true showPrice,
        dp.order_no orderNo,
        con.NAME contact,
        con.mname userName,
        p.pd_name as pd_name,
        con.store_id as m_id,
        dp.delivery_time deliveryTime,
        con.phone phone,
        ifnull(con.area,'') area,
        CONCAT( con.province, con.city, ifnull( con.area, ''), con.address ) deliveryAddress,
        c1.id categoryId,
        tdpd.amount amount,
        i.unit,
        c1.type categoryType,
        i.weight,
        i.maturity,
        i.pd_id pdId,
        tdpd.sku,
        i.volume,
        p.storage_location,
        con.id AS contact_id,
        dep.path,
        dep.sort,
        ad.name_remakes,
        dep.total_volume totalVolume,
        con.distance,
        c1.category,
        c2.category parentCategory,
        i.type skuType,
        ad.name_remakes nameRemakes,
        i.ext_type extType,
        wip.warehouse_no areaNo,
        1 as brandType,
        dp.tenant_id,
        dp.store_id,
        i.weight_num,
        tdpd.delivery_type orderItemDeliveryType
        FROM
        ( SELECT * FROM tms_delivery_plan WHERE delivery_time = #{deliveryTime} AND STATUS = 1 ) dp
        INNER JOIN tms_delivery_plan_detail tdpd ON tdpd.tms_delivery_plan_id = dp.id and tdpd.status = 0
        LEFT JOIN outside_contact con ON dp.contact_id = con.id
        LEFT JOIN delivery_path dep ON dp.contact_id = dep.contact_id
        AND dp.delivery_time = dep.delivery_time
        AND dep.brand_type=1
        LEFT JOIN inventory i ON i.sku = tdpd.sku
        LEFT JOIN admin ad ON ad.admin_id = i.admin_id
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c1 ON p.category_id = c1.id
        LEFT JOIN category c2 ON c1.parent_id = c2.id
        LEFT JOIN warehouse_inventory_mapping wip ON wip.sku=tdpd.sku and wip.store_no=dp.store_no
        LEFT JOIN ad_code_msg acm ON acm.city=con.city AND acm.area=con.area AND acm.`status`=0
        LEFT JOIN fence f ON f.id = acm.fence_id
        <where>
            <if test="parentNo != null">
                AND dp.store_no = #{parentNo}
            </if>
            <if test="areaNo != null">
                AND f.area_no=#{areaNo}
            </if>
            <if test="areaNo != null">
                AND f.area_no=#{areaNo}
            </if>
        </where>
        order by dp.create_time desc
    </select>

    <select id="selectTmsSassClosingOrder" resultMap="ClosingOrderMap">
        SELECT
            'saas' saasFlag,
            tdo.id orderId,
            f.area_no areaNo,
            tdo.type deliveryType,
            TRUE showPrice,
            tdo.outer_order_id orderNo,
            tdse.`name` contact,
            tdo.outer_client_name userName,
            p.pd_name AS pd_name,
            tdo.outer_client_id AS m_id,
            tdo.expect_begin_time deliveryTime,
            tdse.phone phone,
            ifnull( tdse.area, '' ) area,
            CONCAT( tdse.provice, tdse.city, ifnull( tdse.area, '' ), tdse.address ) deliveryAddress,
            tdse.id endSiteId,
            c1.id categoryId,
            tdi.quantity amount,
            tdi.unit,
            c1.type categoryType,
            i.weight,
            i.weight_num,
            i.maturity,
            i.pd_id pdId,
            tdi.outer_item_id sku,
            i.volume,
            p.storage_location,
            tdo.outer_contact_id AS contact_id,
            tdb.path_code path,
            IF(tdb.path_id > 0 ,tdsite.sequence,null) sort,
            ad.name_remakes,
            c1.category,
            c2.category parentCategory,
            i.type skuType,
            ad.name_remakes nameRemakes,
            i.ext_type extType,
            wip.warehouse_no areaNo,
            1 AS brandType,
            tdo.outer_tenant_id tenant_id,
            tdo.outer_client_id store_id,
            tdi.delivery_type orderItemDeliveryType,
            tdi.id,
            tdsite.outer_brand_name outerBrandName,
            tdsite.send_remark sendRemark
        FROM
            tms_dist_order tdo
                LEFT JOIN tms_dist_item tdi ON tdo.id = tdi.dist_order_id
                LEFT JOIN tms_dist_site tdse ON tdse.id = tdo.end_site_id
                LEFT JOIN tms_dist_site tdsb ON tdsb.id = tdo.begin_site_id
                LEFT JOIN tms_delivery_order tdeo ON tdeo.dist_order_id = tdo.id
                LEFT JOIN tms_delivery_batch tdb ON tdb.id = tdeo.batch_id
                LEFT JOIN tms_delivery_site tdsite ON tdsite.delivery_batch_id = tdeo.batch_id
                AND tdo.end_site_id = tdsite.site_id
                LEFT JOIN inventory i ON i.sku = tdi.outer_item_id
                LEFT JOIN admin ad ON ad.admin_id = i.admin_id
                LEFT JOIN products p ON i.pd_id = p.pd_id
                LEFT JOIN category c1 ON p.category_id = c1.id
                LEFT JOIN category c2 ON c1.parent_id = c2.id
                LEFT JOIN warehouse_inventory_mapping wip ON wip.sku = tdi.outer_item_id
                AND wip.store_no = tdsb.out_business_no
                LEFT JOIN ad_code_msg acm ON acm.city = tdse.city
                AND acm.area = tdse.area
                AND acm.`status` = 0
                LEFT JOIN fence f ON f.id = acm.fence_id
        WHERE
            tdsb.out_business_no = #{storeNo}
          AND tdo.expect_begin_time = #{deliveryTime}
          AND tdo.state NOT IN ( 18, 19 )
          AND tdo.source IN ( 210,211 )
    </select>
    <select id="selectSassClosingOrderByOrder" resultMap="ClosingOrderMap">
        SELECT
        'saas' saasFlag,
        true showPrice,
        dp.order_no orderNo,
        con.NAME contact,
        con.mname userName,
        p.pd_name as pd_name,
        con.store_id as m_id,
        dp.delivery_time deliveryTime,
        con.phone phone,
        con.area,
        CONCAT( con.province, con.city, con.area, con.address ) deliveryAddress,
        c1.id categoryId,
        tdpd.amount amount,
        i.unit,
        c1.type categoryType,
        i.weight,
        i.maturity,
        tdpd.sku,
        i.volume,
        i.pd_id,
        p.storage_location,
        con.id AS contact_id,
        dep.path,
        dep.sort,
        ad.name_remakes,
        dep.total_volume totalVolume,
        con.distance,
        c1.category,
        c2.category parentCategory,
        i.type skuType,
        ad.name_remakes nameRemakes,
        i.ext_type extType,
        wip.warehouse_no warehouseNo,
        1 as brandType,
        dp.tenant_id,
        dp.store_id
        FROM
         tms_delivery_plan dp
        INNER JOIN tms_delivery_plan_detail tdpd ON tdpd.tms_delivery_plan_id = dp.id and tdpd.status = 0
        LEFT JOIN outside_contact con ON dp.contact_id = con.id
        LEFT JOIN delivery_path dep ON dp.contact_id = dep.contact_id
        AND dp.delivery_time = dep.delivery_time
        AND dep.brand_type=1
        LEFT JOIN inventory i ON i.sku = tdpd.sku
        LEFT JOIN admin ad ON ad.admin_id = i.admin_id
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c1 ON p.category_id = c1.id
        LEFT JOIN category c2 ON c1.parent_id = c2.id
        LEFT JOIN warehouse_inventory_mapping wip ON wip.sku=tdpd.sku and wip.store_no=dp.store_no
        <where>
            dp.status = 2
            <if test="orderNo != null">
                AND dp.order_no = #{orderNo}
            </if>
        </where>
    </select>

    <select id="selectClosingOrderWithOrderNo" resultMap="ClosingOrderMap">
        SELECT o.order_id orderId, o.order_no orderNo, m.mname userName, m.m_id, con.contact contact, dp.delivery_time
        deliveryTime,m.show_price showPrice,
        con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,o.total_price totalPay ,
        o.delivery_fee deliveryFee,o.out_times_fee, o.remark,o.type orderType,
        oi.id id,oi.pd_name , if(o.type=1,dp.quantity,oi.amount) amount, oi.original_price salePrice, i.unit,oi.price,i.pd_id,
        oi.weight,oi.maturity,oi.sku,con.delivery_car deliveryCar,oi.volume,
        oi.storage_location,con.contact_id,dp.time_frame timeFrame, m.area_no areaNo,dep.path,dep.sort,con.path prePath,ad.name_remakes,
        dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,o.order_time orderTime
        FROM ( SELECT * FROM  delivery_plan
                WHERE  delivery_time  <![CDATA[ >= ]]>  #{startDate}
                    AND delivery_time  <![CDATA[<]]>  #{endDate}
                    AND status in (3,6)
                    <if test="orderNo != null">
                        AND order_no=#{orderNo}
                    </if>
            ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        <if test="orderNo != null">
            AND o.order_no=#{orderNo}
        </if>
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        LEFT JOIN contact con on dp.contact_id=con.contact_id
        LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time AND dep.brand_type=0
        <if test="type != null">
            and dep.type = #{type}
        </if>
        LEFT JOIN inventory i ON i.sku = oi.sku
        left join admin ad on ad.admin_id = i.admin_id
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        <where>
            <if test="deliverytype != null">
                AND dp.deliverytype=#{deliverytype}
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="parentNo != null">
                AND dp.order_store_no = #{parentNo}
            </if>
            <if test="startTime != null or endTime != null">
                and (o.type = 1 or (o.type in (0,3,12)
                <if test="startTime != null">
                    and o.order_time &gt;= #{startTime}
                </if>
                <if test="endTime != null">
                    and o.order_time &lt; #{endTime}
                </if>
                ))
            </if>
            <if test="orderList != null and orderList.size != 0">
                and o.order_no
                <if test="inOrOut == false">
                    not
                </if>
                in
                <foreach collection="orderList" separator="," open="(" close=")" item="orderNo">
                    #{orderNo}
                </foreach>
            </if>
        </where>
        order by dp.time_frame desc
    </select>


    <select id="selectClosingOrderWithAreaNo" resultMap="ClosingOrderMap">
        SELECT o.order_id orderId, o.order_no orderNo, m.mname userName, m.m_id, con.contact contact, dp.delivery_time
        deliveryTime,m.show_price showPrice,
        con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,o.total_price totalPay ,
        o.delivery_fee deliveryFee,o.out_times_fee, o.remark,o.type orderType,
        oi.id id,oi.pd_name , if(o.type=1,dp.quantity,oi.amount) amount, oi.original_price salePrice, i.unit,oi.price,i.pd_id,
        oi.weight,oi.maturity,oi.sku,con.delivery_car deliveryCar,oi.volume,
        oi.storage_location,con.contact_id,dp.time_frame timeFrame, m.area_no areaNo,dep.path,dep.sort,con.path prePath,ad.name_remakes,
        dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,o.order_time orderTime
        FROM ( SELECT * FROM  delivery_plan
                WHERE  delivery_time  <![CDATA[ >= ]]>  #{startDate}
                  AND delivery_time  <![CDATA[<]]>  #{endDate}
                  AND status in (3,6)
                  <if test="orderNo != null">
                    AND order_no=#{orderNo}
                  </if>
            ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        <if test="orderNo != null">
            AND o.order_no=#{orderNo}
        </if>
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        LEFT JOIN contact con on dp.contact_id=con.contact_id
        LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time AND dep.brand_type=0
        LEFT JOIN inventory i ON i.sku = oi.sku
        left join admin ad on ad.admin_id = i.admin_id
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        <where>
            <if test="deliverytype != null">
                AND dp.deliverytype=#{deliverytype}
            </if>
            <if test="areaNoList != null">
                AND m.area_no not in
                <foreach collection="areaNoList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="parentNo != null">
                AND dp.order_store_no=#{parentNo}
            </if>
        </where>
        order by dp.time_frame desc
    </select>

    <select id="selectSaleOutOrder" resultType="net.summerfarm.model.domain.OrderItem">
        SELECT oi.sku,if(o.type=1,dp.quantity,oi.amount) amount, oi.`order_no` orderNo
        FROM ( SELECT * FROM  delivery_plan WHERE order_store_no = #{areaNo} and delivery_time = #{deliveryDate} AND deliverytype = #{deliverytype}  AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku and  wim.warehouse_no = #{trustStoreNo}
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        INNER JOIN inventory i ON i.sku = oi.sku AND (i.sub_type != 1 OR i.`sub_type` IS NULL)
        where dp.order_store_no = #{areaNo} and oi.sku != 'DF001TD0001'
    </select>

    <select id="selectSaleOutOrderForCross" resultType="net.summerfarm.model.domain.OrderItem">
        SELECT oi.sku,if(o.type=1,dp.quantity,oi.amount) amount, oi.`order_no` orderNo
        FROM ( SELECT * FROM  delivery_plan WHERE order_store_no = #{areaNo} and delivery_time = #{deliveryDate} AND deliverytype = #{deliverytype}  AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku and  wim.warehouse_no = #{trustStoreNo}
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        INNER JOIN inventory i ON i.sku = oi.sku AND i.sub_type = 1
        where dp.order_store_no = #{areaNo} and oi.sku != 'DF001TD0001'
    </select>

    <!--下单用户地址坐标 -->
    <select id="selectClosingOrderPoiNotes" resultType="java.util.Map">
    SELECT o.order_no orderNo, m.mname userName, m.poi_note poiNote
      FROM orders o
      INNER JOIN payment p ON p.order_no = o.order_no
      AND p.end_time  <![CDATA[ >= ]]>  #{startDate,jdbcType=TIMESTAMP}
      AND p.end_time  <![CDATA[<]]>  #{endDate,jdbcType=TIMESTAMP}
      AND o.status IN (3,6) AND o.type =0
      LEFT JOIN merchant m ON o.m_id = m.m_id
      ORDER BY m.area
  </select>

    <select id="selectTimingClosingOrderPoiNotes" resultType="java.util.Map">
    SELECT o.order_no orderNo, m.mname userName, m.poi_note poiNote, m.area_no areaNo
    FROM orders o
    INNER JOIN delivery_plan dp on dp.order_no = o.order_no
    AND dp.delivery_time  <![CDATA[ >= ]]>  #{startDate}
    AND dp.delivery_time  <![CDATA[<]]>  #{endDate}
    AND o.status IN (3,6) AND o.type =1
    LEFT JOIN merchant m ON o.m_id = m.m_id
    ORDER BY m.area
  </select>


    <select id="count" resultType="java.lang.Integer">
        select count(1) from orders o
        WHERE o.status in (2,3,6)
        AND o.order_time <![CDATA[<]]> #{endTime}
        AND o.order_time <![CDATA[>=]]> #{startTime}

        <if test="mId !=null">
            and o.m_id=#{mId}
        </if>
    </select>

    <select id="countNum" resultType="java.lang.Integer">
        SELECT count(*)
        FROM orders o
                 LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
                 LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time
        WHERE o.m_id = #{mId} and o.status in (2,3,6,8)
            and ((dep.finish_time <![CDATA[<]]> #{endTime} and dep.finish_time <![CDATA[>=]]> #{startTime})
           or (dep.finish_time is null and  dp.delivery_time <![CDATA[<]]> #{endTime} and dp.delivery_time <![CDATA[>=]]> #{startTime} ))
          and ((o.direct = 1) or (o.direct = 2 and o.type = 3))
    </select>

    <select id="countTimingNotDelivery" resultType="java.lang.Integer">
        select count(1) from orders o
        WHERE o.status in (2,3)
        AND o.type=1
        <if test="mId !=null">
            and o.m_id=#{mId}
        </if>
    </select>

    <select id="orderData" resultType="java.util.HashMap">
        select CONCAT(p.pd_name,'-',i.weight,'-',a.sku) name,a.num number from (select sku,count(*) num FROM order_item
        oi WHEre
        oi.order_no in
        (select order_no from orders o
        left join merchant m on o.m_id=m.m_id
        WHERE o.status in (3,6)
        AND o.order_time <![CDATA[<=]]> #{endTime}
        AND o.order_time <![CDATA[>]]> #{startTime}
        <if test="phone !=null">
            and m.phone=#{phone}
        </if>
        )
        GROUP BY oi.sku)a left join inventory i on a.sku =i.sku
        left join products p on p.pd_id=i.pd_id
    </select>

    <select id="orderMoneyData" resultType="java.util.HashMap">
        select date(o.order_time) time,sum(total_price) total from orders o
        left join merchant m on o.m_id=m.m_id
        WHERE o.status in (3,6)
        AND o.order_time <![CDATA[<=]]> #{endTime}
        AND o.order_time <![CDATA[>]]> #{startTime}
        <if test="phone !=null">
            and m.phone=#{phone}
        </if>
        group by date(o.order_time)
    </select>

    <select id="notDeliveryOrders"
            parameterType="net.summerfarm.model.vo.DeliveryOrdersVO"
            resultType="net.summerfarm.model.vo.DeliveryOrdersVO">
        select d.sku,d.pd_name pdName,d.delivery_time deliveryTime,sum(d.amount) total, d.areaNo, d.name_remakes nameRemakes from (
        select oi.sku,oi.pd_name,dp.delivery_time,if(o.type=1,dp.quantity ,oi.amount) amount, m.area_no areaNo,ad.name_remakes from
        orders o
        left join order_item oi on o.order_no=oi.order_no and oi.status=3
        left join merchant m on o.m_id=m.m_id
        left join inventory inv on oi.sku = inv.sku
        left join admin ad on ad.admin_id = inv.admin_id
        left join delivery_plan dp on o.order_no = dp.order_no
        where o.status=3 and o.type in (0,1,3,12) AND dp.id is not null and dp.status in (2,3)
        <if test="areaNo !=null">
            and dp.order_store_no=#{areaNo}
        </if>
        <if test="sku !=null">
            and oi.sku=#{sku}
        </if>
        <if test="pdName !=null">
            and oi.pd_name LIKE CONCAT('%',#{pdName,jdbcType=VARCHAR},'%')
        </if>
        <if test="mname !=null">
            and m.mname LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime !=null">
            and date(dp.delivery_time) <![CDATA[>]]> #{startTime}
        </if>
        <if test="endTime !=null">
            and date(dp.delivery_time) <![CDATA[<=]]> #{endTime}
        </if>
        UNION ALL
        /**
        省心送没有设置配送计划的
        */
        select oi.sku,oi.pd_name,null delivery_time,(oi.amount - ifnull(dp.tot,0)) amount,m.area_no areaNo, ad.name_remakes from (select * from
        orders where type=1 and status in (2,3))t
        left join order_item oi on t.order_no=oi.order_no and oi.sku !='DF001TD0001' and oi.status=3
        left join inventory inv on oi.sku = inv.sku
        left join admin ad on ad.admin_id = inv.admin_id
        left join (
        select order_no,null delivery_time,sum(quantity) tot,order_store_no from delivery_plan
        where status in (2,3,6) and order_no in (select order_no from
        orders where type=1 and status in (2,3)) group by order_no
         )dp on
        oi.order_no=dp.order_no
        left join merchant m on t.m_id=m.m_id
        where oi.amount>dp.tot
        <if test="areaNo !=null">
            and dp.order_store_no=#{areaNo}
        </if>
        <if test="sku !=null">
            and oi.sku=#{sku}
        </if>
        <if test="pdName !=null">
            and oi.pd_name LIKE CONCAT('%',#{pdName,jdbcType=VARCHAR},'%')
        </if>
        <if test="mname !=null">
            and m.mname LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime !=null">
            and date(dp.delivery_time) <![CDATA[>]]> #{startTime}
        </if>
        <if test="endTime !=null">
            and date(dp.delivery_time) <![CDATA[<=]]> #{endTime}
        </if>

        )d group by d.sku,date(d.delivery_time)
        order by d.delivery_time
    </select>

    <select id="notDeliveryOrdersDetail"
            parameterType="net.summerfarm.model.vo.DeliveryOrdersVO"
            resultType="net.summerfarm.model.vo.DeliveryOrdersVO">
        select o.order_no orderNo,m.mname,o.order_time orderTime,oi.price,sum(if(o.type=1,dp.quantity,oi.amount))
        total,dp.delivery_time deliveryTime
        from orders o
        left join order_item oi on o.order_no=oi.order_no
        left join delivery_plan dp on o.order_no = dp.order_no
        left join merchant m on o.m_id=m.m_id
        where
        o.status=3 and o.type in (0,1,3,12)
        and dp.id is not null and dp.status in (2,3)
        and oi.sku=#{sku}
        <if test="areaNo !=null">
            and dp.order_store_no=#{areaNo}
        </if>
        <if test="deliveryTime !=null">
            and date(dp.delivery_time)=#{deliveryTime}
        </if>
        <if test="deliveryTime ==null">
            and date(dp.delivery_time)=null
        </if>
        GROUP by o.order_no

        UNION ALL
        select t.order_no orderNo,m.mname,t.order_time orderTime,oi.price,(oi.amount-dp.tot) total,null from (select *
        from orders where type=1
        and status in (2,3)) t
        left join order_item oi on t.order_no=oi.order_no and oi.sku !='DF001TD0001'
        left join (select order_no,null delivery_time,sum(quantity) tot, order_store_no from delivery_plan group by order_no)dp on
        oi.order_no=dp.order_no
        left join merchant m on t.m_id=m.m_id
        where oi.amount>dp.tot
        and oi.sku=#{sku}
        <if test="areaNo !=null">
            and dp.order_store_no=#{areaNo}
        </if>
        <if test="deliveryTime !=null">
            and date(dp.delivery_time)=#{deliveryTime}
        </if>
        <if test="deliveryTime ==null">
            and date(dp.delivery_time) is NULL
        </if>


    </select>


    <select id="countOrderMerchant" resultType="net.summerfarm.model.vo.DataVO">
        select o.area_no areaNo, count(distinct o.m_id) total
        from orders o force index(orders_time_index)
        where o.status in (2,3,6)
          AND o.order_time <![CDATA[<=]]> #{endTime}
          AND o.order_time <![CDATA[>]]> #{startTime}
        GROUP  by o.area_no;
    </select>

    <select id="countOrderMoney" resultType="net.summerfarm.model.vo.DataVO">
        select o.area_no areaNo,sum(o.total_price) total
        from orders o force index(orders_time_index)
        where o.status in (2,3,6)
        AND o.order_time <![CDATA[<=]]> #{endTime}
        AND o.order_time <![CDATA[>]]> #{startTime}
        GROUP  by o.area_no
    </select>

    <select id="selectByOrderyNo" parameterType="string" resultType="net.summerfarm.model.vo.OrderVO">
    SELECT
    o.m_id mId,o.account_id accountId, msa.openid subAccountOpenid,o.admin_id adminId,
    o.order_no orderNo, m.mname, o.order_time orderTime, p.money, o.status,o.order_pay_type orderPayType,
    o.remark, o.type,o.total_price totalPrice, p.end_time endTime, m.admin_id majorAdminId, m.direct,m.size,o.out_times outTimes,o.out_times_fee outTimesFee,o.delivery_fee deliveryFee,o.area_no areaNo,
    msa.contact     subAccountContact,
    msa.phone       subAccountPhone,
    o.m_size mSize, o.total_price totalPrice ,
    o.out_stock outStock,
    o.order_sale_type orderSaleType,
    o.receivable_status receivableStatus,
    o.invoice_status invoiceStatus,
    o.financial_invoice_id financialInvoiceId,o.origin_price, dpi.delivery_date oneClickDeliveryDate,
    o.selling_entity_name sellingEntityName
    FROM  (SELECT * FROM orders t WHERE t.order_no= #{orderNo,jdbcType=VARCHAR}) o
    LEFT JOIN merchant m ON m.m_id = o.m_id
    LEFT JOIN payment p ON p.order_no = o.order_no and p.status = 1
    LEFT JOIN merchant_sub_account msa ON o.account_id = msa.account_id
    LEFT JOIN direct_purchase_info dpi on o.order_no = dpi.order_no
  </select>
    <!--查询-->
    <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.model.vo.OrderVO">
        SELECT
        o.order_no, m.mname, o.order_time, p.money, o.status,o.total_price,o.remark,o.type,o.m_size mSize,m.direct, m.area_no
        areaNo,fr.admin_name bdName
        FROM orders o
        LEFT JOIN merchant m ON m.m_id = o.m_id
        LEFT JOIN payment p ON p.order_no = o.order_no
        LEFT JOIN follow_up_relation fr ON o.m_id=fr.m_id AND fr.reassign = 0
        <where>
            <if test="mname != null ">
                AND m.mname LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
            </if>
            <if test="orderNo != null">
                AND o.order_no LIKE CONCAT('%',#{orderNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="status != null">
                AND o.status = #{status,jdbcType=SMALLINT}
            </if>
            <if test="mId != null">
                AND o.m_id = #{mId}
            </if>
            <if test="type != null">
                AND o.type = #{type}
            </if>
            <if test="orderLike != null">
                AND o.order_no LIKE CONCAT(#{orderLike},'%')
            </if>
            <if test="areaNo != null">
                AND m.area_no =#{areaNo}
            </if>
            <if test="majorAdminId != null">
                AND m.admin_id = #{majorAdminId}
                AND o.status IN (2,3,6)
            </if>
            <if test="adminId != null">
                AND fr.admin_id=#{adminId}
            </if>
            <if test="bdName != null">
                AND fr.admin_name LIKE CONCAT('%',#{bdName},'%')
            </if>
            <if test="startTime != null">
                AND o.order_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND o.order_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="mSize != null">
                AND o.m_size  LIKE CONCAT('%',#{mSize,jdbcType=VARCHAR},'%')
            </if>
        </where>
        ORDER BY o.order_time DESC
    </select>

    <update id="update">
        update orders o
        <set>
            <if test="remark !=null">
                o.remark = #{remark},
            </if>
            <if test="status !=null">
                o.status = #{status},
            </if>
            <if test="outTimes !=null">
                o.out_times = #{outTimes},
            </if>
            <if test="receivableStatus != null">
                o.receivable_status = #{receivableStatus},
            </if>
            <if test="invoiceStatus != null ">
                o.invoice_status = #{invoiceStatus},
            </if>
        </set>
        WHERE o.order_no = #{orderNo}
    </update>


    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.Orders">
        insert into orders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="mId != null">
                m_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="orderTime != null">
                order_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="deliveryFee != null">
                delivery_fee,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="totalPrice != null">
                total_price,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="confirmTime != null">
                confirm_time,
            </if>
            <if test="outTimes != null">
                out_times,
            </if>
            <if test="mSize != null">
                m_size,
            </if>
            <if test="direct != null">
                direct,
            </if>
            <if test="outStock != null">
                out_stock,
            </if>
            <if test="originPrice != null">
                origin_price,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="orderSaleType != null">
                order_sale_type,
            </if>
            <if test="afterSaleAmount != null">
                after_sale_amount,
            </if>
            <if test="receivableStatus != null">
                receivable_status,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="orderTime != null">
                #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="totalPrice != null">
                #{totalPrice},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="confirmTime != null">
                #{confirmTime},
            </if>
            <if test="outTimes != null">
                #{outTimes},
            </if>
            <if test="mSize != null">
                #{mSize},
            </if>
            <if test="direct != null">
                #{direct},
            </if>
            <if test="outStock != null">
                #{outStock},
            </if>
            <if test="originPrice != null">
                #{originPrice},
            </if>
            <if test="areaNo != null">
                #{areaNo},
            </if>
            <if test="orderSaleType != null">
                #{orderSaleType},
            </if>
            <if test="afterSaleAmount != null">
                #{afterSaleAmount},
            </if>
            <if test="receivableStatus != null">
                #{receivableStatus},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
        </trim>
    </insert>

    <select id="getOrdersByBD" resultMap="ClosingOrderMap">
        SELECT o.order_id orderId, o.order_no orderNo,o.total_price totalPay,o.order_time orderTime,f.admin_id
        adminId,f.admin_name BD,
        oi.id id,oi.pd_name pdName, oi.amount amount, oi.price salePrice, oi.weight,oi.maturity,oi.sku,oi.category_id
        categoryId, m.area_no areaNo,m.m_id
        from orders o
        LEFT JOIN follow_up_relation f on o.m_id=f.m_id and reassign=0
        LEFT JOIN merchant m on m.m_id = o.m_id
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3
        where o.status in (3,6) AND o.type in (0,1,3,12)
        <if test="startTime !=null">
            AND o.order_time <![CDATA[>]]>  #{startTime}
        </if>
        <if test="endTime !=null">
            AND o.order_time <![CDATA[<=]]>  #{endTime}
        </if>
        <if test="adminIds != null">
            AND f.admin_id in
            <foreach collection="adminIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="areaNo != null">
            AND m.area_no=#{areaNo}
        </if>
        and (m.admin_id not in
        <foreach collection="bandAdminIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        or m.admin_id is null)
    </select>

    <select id="getGMVByBD" resultType="java.lang.Double">
        SELECT ifnull(sum(o.total_price), 0)
        from orders o
        LEFT JOIN follow_up_relation f on o.m_id=f.m_id and reassign=0
        LEFT JOIN merchant m on m.m_id = o.m_id
        where o.status in (3,6) AND o.type in (0,1,3,12)
        <if test="startTime !=null">
            AND o.order_time <![CDATA[>]]>  #{startTime}
        </if>
        <if test="endTime !=null">
            AND o.order_time <![CDATA[<=]]>  #{endTime}
        </if>
        <if test="adminId != null">
            AND f.admin_id=#{adminId}
        </if>
        <if test="areaNo != null">
            AND m.area_no=#{areaNo}
        </if>
    </select>

    <select id="selectUnPadiTotal" parameterType="java.util.HashMap" resultType="java.math.BigDecimal">
        SELECT SUM(o.total_price)
        FROM orders o
        LEFT JOIN merchant m ON m.m_id=o.m_id
        WHERE o.status IN (2,3,6)
        AND m.admin_id=#{adminId}
        AND o.direct=1
        AND DATE_FORMAT(o.order_time,'%Y%m%d')<![CDATA[ >= ]]> DATE_FORMAT(#{startTime},'%Y%m%d')
        AND DATE_FORMAT(o.order_time,'%Y%m%d')<![CDATA[ <= ]]>  DATE_FORMAT(#{endTime},'%Y%m%d')
    </select>

    <select id="selectMajorOrderItem" parameterType="net.summerfarm.model.vo.OrderVO"
            resultType="net.summerfarm.model.vo.OrderItemVO">
        SELECT m.mname,o.order_no orderNo,o.order_time orderTime,oi.id,oi.pd_name pdName, oi.sku, sm.mapping,oi.price,oi.amount,oi.weight,o.direct,oi.rebate_number rebateNumber,oi.rebate_type rebateType,oi.m_price mPrice,
        o.remark,dp.delivery_time deliveryTime,sm.mapping_name mappingName ,o.delivery_fee deliveryFee,i.type,oi.original_price originalPrice ,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,
        o.status,m.m_id mId
        FROM orders o
        INNER JOIN merchant m ON o.m_id=m.m_id
        LEFT JOIN order_item oi ON o.order_no=oi.order_no
        left join sku_mapping sm on oi.sku = sm.sku and m.admin_id = sm.admin_id and sm.status = 1 and ka_bill_switch = 1
        LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
        LEFT JOIN inventory i on oi.sku = i.sku
        left join contact c on c.contact_id = dp.contact_id
        WHERE m.admin_id=#{majorAdminId}
        AND o.`status` in (2,3,6)
        AND o.m_size in('大客户','批发客户','普通客户')
        AND o.type !=10
        <if test="startTime != null">
            AND o.order_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND o.order_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="timeDimension !=null and timeDimension == 1">
            <if test="billStartTime != null">
                AND o.order_time >= #{billStartTime}
            </if>
            <if test="billEndTime != null">
                AND o.order_time <![CDATA[<=]]> #{billEndTime}
            </if>
        </if>
        <if test="timeDimension !=null and timeDimension == 2">
            <if test="billStartTime != null">
                AND dp.delivery_time >= #{billStartTime}
            </if>
            <if test="billEndTime != null">
                AND dp.delivery_time <![CDATA[<=]]> #{billEndTime}
            </if>
        </if>
        <if test="status != null ">
            AND o.status = #{status}
        </if>

        <if test="mId != null">
            AND o.m_id = #{mId}
        </if>
        <if test= "settlementMethod != null">
            AND o.direct = #{settlementMethod}
        </if>
        ORDER BY o.order_id DESC
    </select>

    <select id="selectTotalPriceByMonth" resultType="java.math.BigDecimal">
        SELECT SUM(total_price) totalPrice
        FROM orders
        WHERE confirm_time <![CDATA[>=]]> #{beginTime}
        AND confirm_time <![CDATA[<]]> #{endTime}
        AND `status`=6
        AND m_id=#{mId}
    </select>

    <select id="selectTotalGmv" resultType="net.summerfarm.model.vo.GmvVO">
        SELECT sum(t.gmv) gmv,COUNT(1) orderMerchants,sum(t.orderNum) orders
        FROM
            (SELECT SUM(o.origin_price) gmv,COUNT(1) orderNum,o.m_id
            FROM orders o
            INNER JOIN merchant m ON o.m_id=m.m_id
            WHERE o.`status` IN (2,3,6)
            and o.order_sale_type = 0
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="startTime != null">
                AND o.order_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND o.order_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="dataPermission != null and dataPermission.size!=0">
                AND m.area_no IN
                <foreach collection="dataPermission" item="item" open="(" close=")"  separator=",">
                    #{item}
                </foreach>
            </if>
            GROUP BY o.m_id) t
    </select>

    <select id="selectAdvance" resultType="net.summerfarm.model.vo.GmvVO">
        SELECT ifnull(sum(t.gmv),0) gmv,COUNT(1) orderMerchants, ifnull(sum(t.orderNum),0) orders
        FROM
        (SELECT SUM(o.origin_price) gmv,COUNT(1) orderNum,o.m_id
        FROM orders o
        left join order_advance ad on ad.order_no = o.order_no
        INNER JOIN merchant m ON o.m_id=m.m_id
        WHERE o.`status` IN (2,3,6)
        and o.order_sale_type = 1
        <if test="areaNo != null">
            AND m.area_no = #{areaNo}
        </if>
        <if test="startTime != null">
            AND ad.pay_remaining_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND ad.pay_remaining_time <![CDATA[<]]> #{endTime}
        </if>
        <if test="dataPermission != null and dataPermission.size!=0">
            AND m.area_no IN
            <foreach collection="dataPermission" item="item" open="(" close=")"  separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY o.m_id) t
    </select>

    <select id="selectLast" resultType="net.summerfarm.model.domain.Orders" >
        select order_time orderTime,order_no orderNo from orders where status in (1,2,3)  and  type != 10 and m_id =#{mId} order by order_id desc limit 1
    </select>

    <select id="unLockQuantity" resultType="java.lang.Integer">
        SELECT SUM(t.amount - IFNULL(t.finshQuantity,0)) quantity
        FROM (
        SELECT oi.amount,SUM(dp.quantity) finshQuantity
        FROM orders o
        LEFT JOIN order_item oi ON o.order_no=oi.order_no
        LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no AND dp.`status` IN (3,6)
        WHERE o.type=1
        AND o.`status` IN (2,3,6)
        AND oi.sku = #{sku}
        <if test="areaNos != null and areaNos.size!=0">
            AND dp.order_store_no IN
            <foreach collection="areaNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY o.order_no ) t
    </select>
    <update id="changeOrderMerchant">
        update orders set m_id = #{newMid},account_id = #{accountId} where m_id = #{oldMid}
    </update>


    <select id="selectTotalPaymentGmv" resultType="java.math.BigDecimal">
        select sum(ord.pri + ord.out_times_fee + ord.delivery_fee)
            from (
                select sum(oi.price * oi.amount) pri ,o.order_no,o.out_times_fee,o.delivery_fee from orders o
                inner join  order_item oi on o.order_no = oi.order_no and oi.status != 8
                INNER JOIN merchant m ON o.m_id=m.m_id
                WHERE o.`status` IN (2,3,6)
              and o.order_sale_type = 0
                <if test="areaNo != null">
                    AND m.area_no = #{areaNo}
                </if>
                <if test="startTime != null">
                    AND o.order_time <![CDATA[>=]]> #{startTime}
                </if>
                <if test="endTime != null">
                    AND o.order_time <![CDATA[<]]> #{endTime}
                </if>
                <if test="dataPermission != null and dataPermission.size!=0">
                    AND m.area_no IN
                    <foreach collection="dataPermission" item="item" open="(" close=")"  separator=",">
                        #{item}
                    </foreach>
                </if>
                group by o.order_no
                ) ord
    </select>

    <select id="selectAdvanceTotalPaymentGmv" resultType="java.math.BigDecimal">
        select sum(o.total_price) from orders o
        left join order_advance ad on ad.order_no = o.order_no
        INNER JOIN merchant m ON o.m_id=m.m_id
        WHERE o.`status` IN (2,3,6)
        and o.order_sale_type = 1
        <if test="areaNo != null">
            AND m.area_no = #{areaNo}
        </if>
        <if test="startTime != null">
            AND ad.pay_remaining_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND ad.pay_remaining_time <![CDATA[<]]> #{endTime}
        </if>
        <if test="dataPermission != null and dataPermission.size!=0">
            AND m.area_no IN
            <foreach collection="dataPermission" item="item" open="(" close=")"  separator=",">
                #{item}
            </foreach>
        </if>

    </select>



    <select id="selectStoreSaleAmount" resultType="net.summerfarm.model.domain.OrderItem">
        select sku,sum(amount) amount from orders o right join order_item oi  on oi.order_no = o.order_no
        left join area a on o.area_no = a.area_no
        where  o.order_time between #{startTime} and #{endTime}
            and o.status in (3,6) and o.type in (0,1,3,12)
            and a.large_area_no = #{storeNo}
        group by oi.sku
    </select>

    <select id="selectStoreSaleAmountNew" resultType="net.summerfarm.model.domain.OrderItem">
        select sku, sum(amount) amount from
            (
                select oi.sku, oi.amount, o.`area_no`
                from orders o
                right join order_item oi on oi.order_no = o.order_no
                where o.order_time between #{startTime} and #{endTime}
                  and o.status in (3,6)
                  and o.type in (0,1,3,12)
            ) tmp
        where tmp.`area_no` in
              (
                  select `area_no` from area where `large_area_no` = #{storeNo}
              )
        group by tmp.sku
    </select>


    <update id="updateStockTask" >
        update orders o
        <set>
            <if test="outStock != null">
                o.out_stock = #{outStock},
            </if>
        </set>
        WHERE o.order_no = #{orderNo}
    </update>


    <select id="selectSaleOutOrderNo" resultType="java.lang.String">
        SELECT o.order_no
        FROM ( SELECT * FROM  delivery_plan WHERE order_store_no = #{areaNo} and delivery_time = #{deliveryDate} AND deliverytype = #{deliverytype}  AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no And oi.sku = wim.sku and wim.warehouse_no = #{trustStoreNo}
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        INNER JOIN inventory i ON i.sku = oi.sku AND (i.sub_type != 1 OR i.`sub_type` IS NULL)
        where dp.order_store_no = #{areaNo}
    </select>

    <select id="selectSaleOutOrderNoForCross" resultType="java.lang.String">
        SELECT o.order_no
        FROM ( SELECT * FROM  delivery_plan WHERE order_store_no = #{areaNo} and delivery_time = #{deliveryDate} AND deliverytype = #{deliverytype}  AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no And oi.sku = wim.sku and wim.warehouse_no = #{trustStoreNo}
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        INNER JOIN inventory i ON i.sku = oi.sku AND i.sub_type = 1
        where dp.order_store_no = #{areaNo}
    </select>

    <select id="selectTimingOrder" resultType="net.summerfarm.model.domain.Orders">
        select o.area_no areaNo ,o.order_no orderNo from orders o
        inner join delivery_plan dp ON o.order_no = dp.order_no and dp.status = 2
        where o.type = 1 and o.status in (2,3) and dp.order_store_no = #{storeNo}
        group by o.order_no
    </select>

    <select id="selectTimingOrders" resultType="java.lang.String">
        select o.order_no orderNo from orders o
          inner join order_item oi on o.order_no = oi.order_no
            <if test="sku != null">
                and oi.sku= #{sku}
            </if>
          left join warehouse_inventory_mapping  wim on wim.sku = oi.sku and wim.warehouse_no = #{storeNo}
          where o.type = 1 and o.status in(2,3)
    </select>


    <select id="selectAllSituation" resultMap="BaseResultMap" parameterType="net.summerfarm.model.input.OrderReq">
        SELECT
            o.order_no, m.mname, o.order_time, p.money, o.status,o.total_price,o.remark,o.type,o.m_size mSize,o.direct,
        o.operate_id operateId, m.area_no area_no, dp.delivery_time
        FROM orders o
        LEFT JOIN merchant m ON m.m_id = o.m_id
        LEFT JOIN payment p ON p.order_no = o.order_no
        LEFT JOIN delivery_plan dp ON o.order_no = dp.order_no
        <if test="adminId != null or bdName != null">
            LEFT JOIN follow_up_relation fr ON o.m_id=fr.m_id
        </if>
        <if test= "sku != null">
            INNER JOIN order_item oi on oi.order_no = o.order_no
        </if>
        <if test= "fenceAddress != null and fenceAddress.size != 0">
            LEFT JOIN contact c on c.contact_id = dp.contact_id
        </if>
        <if test="status != null and (status == 20 or status == 3)">
            LEFT JOIN market_partnership_buy_order mpbo on o.order_no = mpbo.order_no
            LEFT JOIN market_partnership_buy mpb on mpbo.partnership_buy_id = mpb.id
        </if>
        <if test= "operateName != null">
            LEFT JOIN admin a on o.operate_id = a.admin_id
        </if>
        <where>
            AND o.type != 10
            <if test="mname != null ">
                AND m.mname LIKE CONCAT(#{mname,jdbcType=VARCHAR}, '%')
            </if>
            <if test="orderNo != null">
                AND o.order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <choose>
                <when test="status != null and status == 1 ">
                    AND o.status in(1,12)
                </when>
                <when test="status != null and status == 3">
                    AND o.status = #{status} and (mpb.status is null or mpb.status = 2)
                </when>
                <when test="status != null and status == 20">
                    AND mpb.status = 1
                </when>
                <when test="status != null ">
                    AND o.status = #{status,jdbcType=SMALLINT}
                </when>
            </choose>
            <choose>
                <when test="orderSource != null and orderSource == 0 ">
                    AND o.type != 30
                </when>
                <when test="orderSource != null and orderSource == 1">
                    AND o.type = 30
                </when>
            </choose>
            <if test="mId != null">
                AND o.m_id = #{mId}
            </if>
            <if test="type != null and type != 4 and type != 20">
                AND o.type = #{type} AND o.order_sale_type != 20
            </if>
            <if test="type != null and type == 20">
                AND o.order_sale_type = 20
            </if>
            <if test="orderSaleType != null">
                And o.order_sale_type = #{orderSaleType}
            </if>
            <if test="orderLike != null">
                AND o.order_no LIKE CONCAT(#{orderLike},'%')
            </if>
            <!--<if test="areaNo != null">
                AND m.area_no =#{areaNo}
            </if>-->
            <if test="areaList != null and areaList.size() > 0">
                and m.area_no in
                <foreach collection="areaList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="majorAdminId != null">
                AND m.admin_id = #{majorAdminId}
                AND o.status IN (2,3,6)
                AND o.m_size in ('大客户','普通客户','批发客户')
            </if>
            <if test="adminId != null">
                AND fr.admin_id = #{adminId}
            </if>
            <if test="bdName != null and bdName != '无归属BD'">
                AND fr.admin_name = #{bdName} AND fr.reassign = 0
            </if>
            <if test="bdName != null and bdName == '无归属BD'">
                AND fr.reassign = 1
            </if>
            <if test="startTime != null">
                AND o.order_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND o.order_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="mSize != null">
                AND o.m_size = #{mSize}
            </if>
            <if test= "sku != null">
                AND oi.sku = #{sku}
            </if>
            <if test="timeDimension !=null and timeDimension == 1">
                <if test="billStartTime != null">
                    AND o.order_time >= #{billStartTime}
                </if>
                <if test="billEndTime != null">
                    AND o.order_time <![CDATA[<=]]> #{billEndTime}
                </if>
            </if>
            <if test="timeDimension !=null and timeDimension == 2">
                <if test="billStartTime != null">
                    AND dp.delivery_time >= #{billStartTime}
                </if>
                <if test="billEndTime != null">
                    AND dp.delivery_time <![CDATA[<=]]> #{billEndTime}
                </if>
            </if>
            <if test= "settlementMethod != null">
                AND o.direct = #{settlementMethod}
            </if>
            <if test="mIdList !=null and mIdList.size() > 0">
                and m.m_id in
                <foreach collection="mIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
<!--            <if test= "mphone != null">
                AND m.phone = #{mphone}
            </if>-->
            <if test="direct != null">
                AND o.direct = #{direct}
            </if>
            <if test="deliveryTime != null">
                AND dp.delivery_time = #{deliveryTime}
            </if>
            <if test="orderStoreNo != null">
                AND dp.order_store_no = #{orderStoreNo}
            </if>
            <if test="fenceAddress != null and fenceAddress.size != 0">
                <foreach collection="fenceAddress" item="address" open="and (" close=")" separator="or">
                    CONCAT( c.province, c.city, c.area ) LIKE CONCAT(#{address},'%')
                </foreach>
            </if>
            <if test="operateName != null ">
                AND a.realname LIKE CONCAT(#{operateName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="operateId != null ">
                AND o.operate_id = #{operateId}
            </if>
        </where>
        group by o.order_id
        ORDER BY o.order_id DESC
    </select>
    <select id="selectForCrmOrderList" resultType="net.summerfarm.model.vo.OrderVO">
        SELECT o.order_no orderNo, m.mname, o.order_time orderTime, o.status,o.total_price totalPrice,m.area_no areaNo
            ,dp.delivery_time deliveryTime,f.admin_name bdName,o.m_id mId
        FROM orders o
        LEFT JOIN merchant m ON m.m_id = o.m_id
        LEFT JOIN delivery_plan dp ON o.order_no = dp.order_no
        LEFT JOIN follow_up_relation f ON f.m_id=m.m_id AND f.reassign = 0
        WHERE o.order_time BETWEEN #{startTime} AND #{endTime}
        <if test="adminId != null">
            AND f.admin_id = #{adminId}
        </if>
        <if test="merchantId != null">
            AND m.m_id = #{merchantId}
        </if>
        <if test="areaNo != null">
            AND o.area_no = #{areaNo}
        </if>
        <if test="province != null">
            AND m.province = #{province}
        </if>
        <if test="city != null">
            AND m.city = #{city}
        </if>
        <if test="district != null">
            AND m.area = #{district}
        </if>
        <if test="type != null and type != 4">
            AND o.type = #{type}
        </if>
        <if test="orderSaleType != null">
            And o.order_sale_type = #{orderSaleType}
        </if>
        <choose>
            <when test="status == 1 ">
                AND o.status in (1,12)
            </when>
            <when test="status != null ">
                AND o.status = #{status,jdbcType=SMALLINT}
            </when>
        </choose>
        <if test="keyword != null ">
            AND m.mname LIKE CONCAT(#{keyword,jdbcType=VARCHAR},'%')
        </if>
        <if test="orderNo != null">
            AND o.order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="mSize != null">
            AND o.m_size = #{mSize,jdbcType=VARCHAR}
        </if>
        <if test="deliveryTime != null">
            AND dp.delivery_time = #{deliveryTime}
        </if>
        ORDER BY o.order_id DESC
    </select>
    <select id="selectRecentOrderSku" resultType="net.summerfarm.model.vo.RecentOrderSkuVO">
        select p.category_id categoryId,p.pd_id pdId,p.pd_name pdName, i.sku, i.weight, if(p.pd_id = 3,3,c.type) categoryType,count(distinct o.order_no) orderTimes,
            sum(oi.amount) orderAmount,sum(oi.amount*oi.price) totalPrice
        from order_item oi
         left join orders o on oi.order_no = o.order_no and o.status in (2, 3, 6)
         left join inventory i on oi.sku = i.sku
         left join products p on i.pd_id = p.pd_id
         LEFT JOIN category c ON p.category_id = c.id
        where oi.status in (2, 3, 6)
            <if test="startTime != null">
                AND o.order_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND o.order_time <![CDATA[<]]> #{endTime}
            </if>
        <if test="categoryType != null and categoryType != 3">
            AND c.type = #{categoryType}
        </if>
        and o.m_id = #{mId}
        group by i.sku
        order by orderAmount desc
    </select>
    <select id="countOrderByMId" resultType="int">
      select ifnull(count(order_no), 0) from orders where type in (0,1,2,3,11,12) and m_id = #{mId}
    </select>

    <select id="selectFruitClosingOrder" resultType="net.summerfarm.model.domain.StockTaskPick">

        SELECT oi.sku,if(o.type=1,dp.quantity,oi.amount) amount,m.admin_id adminId,oi.weight,a.name_remakes adminName,oi.pd_name pdName
        FROM ( SELECT * FROM  delivery_plan WHERE  order_store_no = #{areaNo} and delivery_time = #{deliveryDate} AND deliverytype = #{deliveryType}  AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        left join admin a on a.admin_id = m.admin_id
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku
        INNER JOIN area_store ar ON oi.sku= ar.sku AND wim.warehouse_no = ar.area_no
        where dp.order_store_no = #{areaNo} and wim.warehouse_no = #{trustStoreNo}
        <choose>
            <when test="closeTime != null">
                AND a.close_order_time = #{closeTime}
                AND m.size = '大客户'
                AND a.close_order_type = 1
            </when>
            <otherwise>
                AND (( a.close_order_time IS NULL
                and m.size = '大客户'
                AND a.close_order_type = 0) or ( m.admin_id is null))
            </otherwise>
        </choose>
    </select>

    <select id="selectNotFruitClosingOrder" resultType="net.summerfarm.model.domain.StockTaskPick">

        SELECT oi.sku,if(o.type=1,dp.quantity,oi.amount) amount,oi.weight,oi.pd_name pdName
        FROM ( SELECT * FROM  delivery_plan WHERE  delivery_time = #{deliveryDate} AND deliverytype = #{deliveryType}  AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        left join category ca on ca.id = oi.category_id
        left join admin a on a.admin_id = m.admin_id
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku and wim.warehouse_no = #{trustStoreNo}
        INNER JOIN area_store ar ON oi.sku=ar.sku AND wim.warehouse_no = ar.area_no
        where m.size = '大客户' and a.close_order_type = 1 and ca.type != 4

        union all

        SELECT oi.sku,if(o.type=1,dp.quantity,oi.amount) amount,oi.weight,oi.pd_name pdName
        FROM ( SELECT * FROM  delivery_plan WHERE  delivery_time = #{deliveryDate} AND deliverytype = #{deliveryType} AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        left join category ca on ca.id = oi.category_id
        left join admin a on a.admin_id = m.admin_id
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and wim.warehouse_no = #{trustStoreNo}
        INNER JOIN area_store ar ON oi.sku=ar.sku AND  wim.warehouse_no = ar.area_no
        where m.size = '大客户' and a.close_order_type = 0

        union all

        SELECT oi.sku,if(o.type=1,dp.quantity,oi.amount) amounnt,oi.weight,oi.pd_name pdName
        FROM ( SELECT * FROM  delivery_plan WHERE  delivery_time = #{deliveryDate} AND deliverytype = #{deliveryType}  AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        inner join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and wim.warehouse_no = #{trustStoreNo}
        INNER JOIN area_store ar ON oi.sku=ar.sku AND  wim.warehouse_no = ar.area_no
        where m.size = '单店'
    </select>

    <select id="selectByOrderyNoNoPermission" resultType="net.summerfarm.model.vo.OrderVO">
    SELECT
    o.m_id mId,o.account_id accountId, msa.openid subAccountOpenid,
    o.order_no orderNo, m.mname, o.order_time orderTime, p.money, o.status,
    o.remark, o.type,o.total_price totalPrice, p.end_time endTime, m.admin_id majorAdminId, m.direct,m.size,o.out_times outTimes,o.out_times_fee outTimesFee,o.delivery_fee deliveryFee,o.area_no areaNo,
    msa.contact     subAccountContact,
    msa.phone       subAccountPhone,
    o.m_size mSize,
    o.out_stock outStock,
    o.order_sale_type orderSaleType,
    o.receivable_status receivableStatus,
    o.operate_id operateId,
    o.update_time updateTime,
    o.invoice_status invoiceStatus
    FROM  (SELECT * FROM orders t WHERE t.order_no= #{orderNo,jdbcType=VARCHAR}) o
    LEFT JOIN merchant m ON m.m_id = o.m_id
    LEFT JOIN payment p ON p.order_no = o.order_no
    LEFT JOIN merchant_sub_account msa ON o.account_id = msa.account_id
    </select>
    <select id="selectDirectPurchaseOrder" resultType="net.summerfarm.model.vo.OrderVO">
        SELECT o.order_id orderId, o.order_no orderNo, o.m_id mId, m.mname mname , o.total_price totalPrice,
        o.order_time orderTime, o.receivable_status receivableStatus,
        (o.total_price - IFNULL(dprr.payedAmount,0.00)) unPayAmount, o.total_price, dprr.payedAmount
        FROM  (SELECT * FROM orders t WHERE t.type = 11 AND t.status != 11) o
        LEFT JOIN  (SELECT sum(pre_amount -left_amount) payedAmount , order_no from direct_purchase_recharge_record where type = 0 group by order_no) dprr ON o.order_no = dprr.order_no
        INNER JOIN merchant m ON m.m_id = o.m_id
     <where>
         <if test="mname != null ">
             AND m.mname LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
         </if>
         <if test="orderNo != null">
             AND o.order_no LIKE CONCAT('%',#{orderNo,jdbcType=VARCHAR},'%')
         </if>
         <if test="status != null">
             AND o.status = #{status,jdbcType=SMALLINT}
         </if>
         <if test="startTime != null">
             AND o.order_time >= #{startTime}
         </if>
         <if test="endTime != null">
             AND o.order_time <![CDATA[<=]]> #{endTime}
         </if>
         <if test="receivableStatus != null">
             AND o.o.receivable_status = #{receivableStatus}
         </if>
     </where>
     GROUP BY o.order_id
     ORDER BY o.order_id DESC
    </select>
    <select id="queryByOrderNo" resultType="net.summerfarm.model.domain.Orders">
        SELECT order_id orderId, m_id mId, account_id accountId, order_time orderTime, type, status, delivery_fee deliveryFee,
        total_price totalPrice, remark, confirm_time confirmTime, out_times outTimes, m_size mSize, direct, out_stock outStock, origin_price originPrice,
        area_no areaNo, order_sale_type orderSaleType, receivable_status receivableStatus,invoice_status invoiceStatus
        FROM orders
        where order_no = #{orderNo}
    </select>

    <select id="selectDirectPurchaseOrderBeforeDate" resultType="net.summerfarm.model.domain.Orders">
      SELECT order_no orderNo FROM direct_purchase_info
      WHERE one_click_ship_time is not null and  CONVERT ( one_click_ship_time, date )  <![CDATA[<=]]> #{placeOrderDate}
    </select>
    <resultMap id="AdminOrderMap" type="net.summerfarm.model.vo.OrderVO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="order_time" property="orderTime"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="delivery_fee" property="deliveryFee"/>
        <result column="total_price" property="totalPrice"/>
        <result column="remark" property="remark"/>
        <result column="confirm_time" property="confirmTime"/>
        <result column="out_times" property="outTimes"/>
        <result column="out_times_fee" property="outTimesFee"/>
        <result column="area_no" property="areaNo"/>
        <result column="m_size" property="mSize"/>
        <result column="direct" property="direct"/>
        <result column="red_pack_amount" property="redPackAmount"/>
        <result column="account_id" property="accountId"/>
        <result column="origin_price" property="originPrice"/>
        <result column= "out_stock" property="outStock"/>
        <result column="order_sale_type" property="orderSaleType"/>
        <result column="admin_id" property="adminId"/>
        <collection property="orderItemVOs" ofType="net.summerfarm.model.vo.OrderItemVO">
            <id column="id" property="id" jdbcType="BIGINT"/>
            <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
            <result column="sku" property="sku" jdbcType="VARCHAR"/>
            <result column="weight" property="weight" jdbcType="VARCHAR"/>
            <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
            <result column="item_order_no" property="orderNo" jdbcType="VARCHAR"/>
            <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
            <result column="amount" property="amount" jdbcType="VARCHAR"/>
            <result column="price" property="price" jdbcType="DECIMAL"/>
            <result column="original_price" property="originalPrice" jdbcType="DECIMAL"/>
            <result column="picture_path" property="picturePath"/>
            <result column="add_time" property="addTime"/>
            <result column="storage_location" property="storageLocation" jdbcType="TINYINT"/>
            <result column="item_status" property="status"/>
            <result column="m_price" property="mPrice"/>
            <result column="volume" property="volume" jdbcType="VARCHAR"/>
            <result column="weight_num" property="weightNum"/>
            <result column="max_threshold" property="maxThreshold"/>
        </collection>
    </resultMap>
    <select id="selectOrdersByAdminId" resultMap="AdminOrderMap">
    SELECT
	o.order_id,
	o.order_no,
	o.m_id,
	o.order_time,
	o.type,
	o.`status`,
	o.delivery_fee,
	o.total_price,
	o.remark,
	o.confirm_time,
	o.out_times,
	o.out_times_fee,
	o.area_no,
	o.m_size,
	o.direct,
	o.red_pack_amount,
	o.account_id,
	o.origin_price,
	o.out_stock,
	o.admin_id,
	oi.id id,
	oi.pd_name,
	oi.sku,
	oi.order_no item_order_no,
	oi.category_id,
	oi.amount,
	oi.price,
	oi.original_price,
	oi.picture_path,
	oi.add_time,
	oi.storage_location,
	oi.`status` item_status,
	oi.m_price,
	oi.weight_num
FROM
	orders o
	LEFT JOIN order_item oi ON o.order_no = oi.order_no
	<where>
	      o.status in (2,3,6)
	      AND o.type != 10
        <if test="adminId != null">
            AND o.m_id in (SELECT m_id FROM merchant where admin_id = #{adminId})
        </if>
        <if test="startTime != null">
            AND o.order_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND o.order_time <![CDATA[<=]]> #{endTime}
        </if>
    </where>
    </select>

    <resultMap id="FinancialOrderMap" type="net.summerfarm.model.vo.FinancialOrderVO">
        <result column="orderNo" property="orderNo" />
        <result column="mId" property="merchantId"/>
        <result column="adminId" property="adminId"/>
        <result column="orderStatus" property="orderStatus"/>
        <result column="orderRemark" property="orderRemark"/>
        <result column="orderTime" property="payEndTime"/>
        <result column="deliveryTime" property="deliveryTime"/>
        <result column="orderType" property="orderType"/>
        <result column="totalPrice" property="totalPrice"/>
        <result column="deliveryFee" property="deliveryFee"/>
        <result column="outTimesFee" property="outTimesFee"/>
        <result column="direct" property="direct"/>
        <result column="billNumber" property="billNumber"/>
        <result column="sellingEntityName" property="sellingEntityName"/>
        <collection property="financialOrderItemList" ofType="net.summerfarm.model.vo.FinancialOrderItemVO">
            <result column="sku" property="sku"/>
            <result column="pdName" property="pdName"/>
            <result column="weight" property="weight"/>
            <result column="amount" property="amount"/>
            <result column="orderItemStatus" property="orderItemStatus"/>
            <result column="skuTotalPrice" property="totalPrice"/>
            <result column="orderItemId" property="orderItemId"/>
            <result column="categoryId" property="categoryId"/>
            <result column="picturePath" property="picturePath"/>
            <result column="order_no" property="orderNo"/>
        </collection>
    </resultMap>

    <select id="selectFinancialOrders" parameterType="net.summerfarm.model.input.FinancialOrderQuery" resultMap = "FinancialOrderMap">
        SELECT
        o.order_no orderNo,o.m_id mId,o.admin_id adminId,o.`status` orderStatus,o.remark orderRemark,o.type orderType,
        o.total_price totalPrice,o.delivery_fee deliveryFee,o.out_times_fee outTimesFee,o.direct direct,o.order_time orderTime,
        t1.delivery_time  deliveryTime,
        oi.id orderItemId,oi.sku,oi.pd_name pdName,oi.weight,oi.amount,oi.`status` orderItemStatus,oi.category_id categoryId,
        IFNULL(oi.actual_total_price,IFNULL( oi.amount, 0 ) * IFNULL( oi.price, 0.00 )) skuTotalPrice,
        fasd.bill_number billNumber,oi.picture_path picturePath, oi.order_no, o.selling_entity_name sellingEntityName
        FROM orders o
        LEFT JOIN order_item oi ON o.order_no = oi.order_no
        LEFT JOIN delivery_plan t1 ON o.order_no = t1.order_no AND o.type in (0,3,12)
        LEFT JOIN finance_accounting_store_detail fasd ON fasd.order_no = o.order_no
        <where>
        <if test="invoiceStatus != null">
            AND o.invoice_status = #{invoiceStatus}
        </if>
        <choose>
            <when test="orderNoList != null and orderNoList.size > 0">
                AND o.order_no IN
                <foreach collection="orderNoList" item="orderNo" index="index" open="("  separator="," close=")">
                    #{orderNo}
                </foreach>
            </when>
            <when test="isReplaceOrder!=null and isReplaceOrder">
                AND o.`status` = 6 AND o.type = 0
            </when>
            <otherwise>
                AND o.`status` IN ( 2, 3, 6 ) AND o.type IN (0,1,3,12)
            </otherwise>
        </choose>
        <if test="mIdList != null and mIdList.size > 0 ">
            AND o.m_id in
            <foreach collection="mIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderItemIdList != null and orderItemIdList.size > 0 ">
            AND oi.id in
            <foreach collection="orderItemIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="remarkType == 0">
            AND o.remark is null
        </if>
        <if test="remarkType == 1">
            AND o.remark is not null
        </if>
        <if test="startTime != null and endTime != null">
            AND o.order_time <![CDATA[ >= ]]> #{startTime}
            AND o.order_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="deliveryStartTime != null and deliveryEndTime != null">
            AND t1.delivery_time <![CDATA[ >= ]]> #{deliveryStartTime}
            AND t1.delivery_time <![CDATA[ <= ]]> #{deliveryEndTime}
            AND o.type in (0,3,12)
        </if>
        <if test="billNumber != null">
            AND fasd.bill_number = #{billNumber}
        </if>
        <if test="isBillOrder != null and isBillOrder">
            AND fasd.bill_number is not null
        </if>
        <if test="isBillOrder != null and !isBillOrder">
            AND fasd.bill_number is null
        </if>
        <if test="isCashOrder != null and isCashOrder">
            AND (o.direct is null or o.direct =2)
        </if>
        <if test="contactId !=null">
            AND t1.contact_id=#{contactId}
        </if>
    </where>
        order by o.order_id DESC
    </select>

    <select id="selectFinancialGroupByOrders" parameterType="net.summerfarm.model.input.FinancialOrderQuery" resultMap = "FinancialOrderMap">
        SELECT
        o.order_no orderNo
        FROM orders o
        LEFT JOIN order_item oi ON o.order_no = oi.order_no
        LEFT JOIN delivery_plan t1 ON o.order_no = t1.order_no AND o.type in (0,3,12)
        LEFT JOIN finance_accounting_store_detail fasd ON fasd.order_no = o.order_no
        <where>
            <if test="invoiceStatus != null">
                AND o.invoice_status = #{invoiceStatus}
            </if>
            <choose>
                <when test="orderNoList != null and orderNoList.size > 0">
                    AND o.order_no IN
                    <foreach collection="orderNoList" item="orderNo" index="index" open="("  separator="," close=")">
                        #{orderNo}
                    </foreach>
                </when>
                <when test="isReplaceOrder!=null and isReplaceOrder">
                    AND o.`status` = 6 AND o.type = 0
                </when>
                <otherwise>
                    AND o.`status` IN ( 2, 3, 6 ) AND o.type IN (0,1,3,12)
                </otherwise>
            </choose>
            <if test="mIdList != null and mIdList.size > 0 ">
                AND o.m_id in
                <foreach collection="mIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orderItemIdList != null and orderItemIdList.size > 0 ">
                AND oi.id in
                <foreach collection="orderItemIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="remarkType == 0">
                AND o.remark is null
            </if>
            <if test="remarkType == 1">
                AND o.remark is not null
            </if>
            <if test="startTime != null and endTime != null">
                AND o.order_time <![CDATA[ >= ]]> #{startTime}
                AND o.order_time <![CDATA[ <= ]]> #{endTime}
            </if>
            <if test="deliveryStartTime != null and deliveryEndTime != null">
                AND t1.delivery_time <![CDATA[ >= ]]> #{deliveryStartTime}
                AND t1.delivery_time <![CDATA[ <= ]]> #{deliveryEndTime}
                AND o.type in (0,3,12)
            </if>
            <if test="billNumber != null">
                AND fasd.bill_number = #{billNumber}
            </if>
            <if test="isBillOrder != null and isBillOrder">
                AND fasd.bill_number is not null
            </if>
            <if test="isBillOrder != null and !isBillOrder">
                AND fasd.bill_number is null
            </if>
            <if test="isCashOrder != null and isCashOrder">
                AND (o.direct is null or o.direct =2)
            </if>
            <if test="contactId !=null">
                AND t1.contact_id=#{contactId}
            </if>
        </where>
        group by o.order_id
        order by o.order_id DESC
    </select>

    <resultMap id="OrderAndItemMap" type="net.summerfarm.model.DTO.OrderAndItemDTO">
        <result column="order_no" property="orderNo" />
        <result column="m_id" property="mId"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="total_price" property="totalPrice"/>
        <result column="origin_price" property="originPrice"/>
        <result column="delivery_fee" property="deliveryFee"/>
        <result column="out_times_fee" property="outTimesFee"/>
        <result column="invoice_status" property="invoiceStatus"/>
        <collection property="orderItemList" ofType="net.summerfarm.model.domain.OrderItem">
            <result column="id" property="id"/>
            <result column="sku" property="sku"/>
            <result column="pd_name" property="pdName"/>
            <result column="weight" property="weight"/>
            <result column="amount" property="amount"/>
            <result column="status" property="status"/>
            <result column="price" property="price"/>
            <result column="category_id" property="categoryId"/>
            <result column="skuTotalPrice" property="totalPrice"/>
            <result column="actualTotalPrice" property="actualTotalPrice"/>

        </collection>
    </resultMap>

    <select id="selectOrderAndItemList" resultMap="OrderAndItemMap">
        SELECT
        o.order_no,o.m_id,o.`status`,o.type,o.total_price,IFNULL(o.origin_price,0.00) origin_price,o.delivery_fee,
        o.out_times_fee,o.invoice_status,
        oi.id,oi.sku,oi.pd_name,oi.weight,oi.amount,oi.`status`,oi.price,oi.category_id,
        IFNULL( oi.amount, 0 ) * IFNULL( oi.price, 0.00 ) skuTotalPrice, oi.actual_total_price  actualTotalPrice
        FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        where o.order_no IN
        <foreach collection="orderNoList" item="orderNo" index="index" open="("  separator="," close=")">
            #{orderNo}
        </foreach>
    </select>

    <select id="selectByOrders" resultType="net.summerfarm.model.vo.FinancialInvoiceOrderItemsVO">
    SELECT
        o.order_no orderNo,
        o.`status` orderStatus,
        o.remark orderRemark,
        o.delivery_fee deliveryFee,
        o.out_times_fee outTimesFee,
        p.end_time payEndTime,
        dp.delivery_time  deliveryTime,
        oi.sku,
        oi.pd_name pdName,
        i.unit,
        oi.weight,
        i.ext_type extType,
        oi.amount skuItemAmount,
        oi.`status` orderItemStatus,
        IFNULL( oi.amount, 0 ) * IFNULL( oi.price, 0.00 ) skuItemTotal,
        IFNULL(IFNULL(t1.tax_rate_value, t2.tax_rate_value), 0) taxRateValue,
        IFNULL(IFNULL(t1.tax_rate_code, t2.tax_rate_code), "") taxRateCode
    FROM
        orders o
        LEFT JOIN order_item oi ON o.order_no = oi.order_no
        left join inventory i on i.sku = oi.sku
        LEFT JOIN payment p ON o.order_no = p.order_no
        LEFT JOIN tax_rate_config t1 ON t1.pd_id = i.pd_id
        LEFT JOIN tax_rate_config t2 ON t2.category_id = oi.category_id
        LEFT JOIN delivery_plan dp ON o.order_no = dp.order_no AND o.type in (0,3,12)
        <where>
            <if test="orderNoList != null and orderNoList.size > 0">
                o.order_no IN
                <foreach collection="orderNoList" item="orderNo" index="index" open="("  separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOrderByStoreNoAndSku"  resultType="net.summerfarm.model.domain.OrderItem">
        select o.order_no orderNo, oi.amount
        from orders o
                 left join order_item oi on o.order_no = oi.order_no
                 inner join (select con.*
                             from (select * from xianmudb.contact where store_no = #{storeNo} order by is_default desc) con
                             group by con.m_id) c on c.m_id = o.m_id
        where o.status in (2, 3)
          and o.type = 1
          and oi.sku = #{sku}
          <if test="areaNo != null">
              o.area_no = #{areaNo}
          </if>
    </select>
    <select id="selectPeriodMid" resultType="java.lang.Long">
        select m_id from orders where
        status in(2,3,6) and m_size != '大客户' and area_no !=3027
         and DATE(order_time) between #{sTime} and #{eTime}
        group by m_id having count(1)>0
    </select>
    <select id="selectPeriodGmv" resultType="java.math.BigDecimal">
        select ifnull(sum(totalGmv),0)
        from (
                 select sum(oi.amount * oi.price) + o.delivery_fee as totalGmv
                 from orders o
                          left join order_item oi on o.order_no = oi.order_no
                 where oi.status in (2, 3, 6) and o.status != 12
                   and order_time between #{startTime}
                     and #{endTime}
                   and m_id = #{mId}
                 group by oi.order_no
             ) a
    </select>
    <select id="selectOrderByMerchant" resultMap="ClosingOrderMap" parameterType="net.summerfarm.model.vo.MerchantVO">
        SELECT o.order_id orderId, o.order_no orderNo, m.mname userName, m.m_id, dp.delivery_time deliveryTime,o.type orderType,
        oi.id id,oi.pd_name,if(o.type=1,dp.quantity,oi.amount) amount,oi.sku,
        m.area_no areaNo,oi.order_no oi_order_no
        FROM ( SELECT * FROM  delivery_plan WHERE  delivery_time >= #{deliveryTime} AND status in (3,6)  ) dp
        INNER JOIN orders o  on dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        where  dp.deliverytype = 0 AND m.area_no= #{areaNo} and m.province = #{province} and m.city = #{city} and m.area = #{area}
        order by userName desc
    </select>
    <update id="updateOrderAreaNo">
        update orders set area_no = #{areaNo}
        where
        <if test="orderNoList != null and orderNoList.size > 0">
            order_no IN
            <foreach collection="orderNoList" item="orderNo" index="index" open="("  separator="," close=")">
                #{orderNo}
            </foreach>
        </if>
    </update>

    <select id="selectClosingToTimingOrder" resultType="java.lang.String" parameterType="net.summerfarm.model.vo.MerchantVO">
        select o.order_no from orders o
        left join merchant m on o.m_id = m.m_id
        where m.area_no= #{areaNo} and m.province = #{province} and m.city = #{city} and m.area = #{area}
         and o.type = 1
         and status in(2,3)
    </select>
    <select id="selectLatestOrderTime" resultType="java.time.LocalDateTime">
        SELECT last_order_time FROM `merchant` WHERE m_id = #{mId}
    </select>

    <select id="selectOrderTimeByMId" resultType="java.util.Date">
        SELECT order_time FROM orders WHERE m_id = #{mId} AND `status` in (2,3,6)
        <if test="startTime != null">
            AND order_time <![CDATA[ >= ]]> #{startTime}
        </if>
        ORDER BY order_time DESC
    </select>
    <select id="selectTotalAmount" resultType="java.math.BigDecimal">
        select sum(total_price) from orders o LEFT JOIN payment p on o.order_no = p.order_no
        where p.status = 1
        <if test="accountId != null">
            and p.company_account_id=#{accountId}
        </if>
        <if test="payTypes !=null and !payTypes.isEmpty()">
            and p.pay_type in
            <foreach collection="payTypes" item="payType" open="(" close=")" separator=",">
                #{payType}
            </foreach>
        </if>
        and p.end_time >= #{startTime} and p.end_time <![CDATA[<=]]> #{endTime};
        ;
    </select>

    <select id="selectDeliveryFee" resultType="java.math.BigDecimal">
        select delivery_fee
        from orders
        where order_no = #{orderNo}
    </select>

    <select id="selectOrdersByMId" resultType="net.summerfarm.model.vo.OrderVO">
        SELECT distinct o.order_no orderNo,o.total_price totalPrice,o.delivery_fee deliveryFee,o.out_times_fee outTimesFee
        FROM orders o
                 LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
                 LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time
        WHERE
           o.m_id = #{mId} AND o.status in (2,3,6,8)
           and ((dep.finish_time <![CDATA[<]]> #{endTime} and dep.finish_time <![CDATA[>=]]> #{startTime} )
           or (dep.finish_time is null and  dp.delivery_time <![CDATA[<]]> #{endTime} and dp.delivery_time <![CDATA[>=]]> #{startTime}))
           and ((o.direct = 1) or (o.direct = 2 and o.type = 3))
    </select>

    <select id="selectPlaceAnOrder" resultType="net.summerfarm.model.vo.OrderVO">
        SELECT o.m_id mId,o.order_no orderNo,o.total_price totalPrice,o.delivery_fee deliveryFee,o.out_times_fee outTimesFee
        FROM orders o
                 LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
                 LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time
                 LEFT JOIN finance_settlement fs ON fs.admin_id = o.admin_id
        WHERE o.status in (2,3,6,8)  and o.`type` = 3 and (o.direct is null or (o.direct = 2 and o.m_size = '大客户' and (fs.status <![CDATA[<>]]> 0 or fs.status is null)))
           and ((dep.finish_time <![CDATA[<]]> #{endTime} and dep.finish_time <![CDATA[>=]]> #{startTime})
           or (dep.finish_time is null and dp.delivery_time <![CDATA[<]]> #{endTime} and dp.delivery_time <![CDATA[>=]]> #{startTime}))
    </select>


    <select id="selectWithItem" resultMap="VOMap">
        select o.order_no,o.delivery_fee,sum(oi.price * oi.amount) as itemSum,count(oi.id) itemCount,o.m_id
        from orders o
        left join order_item oi on oi.order_no = o.order_no
        where o.order_no in
            <foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
                #{orderNo}
            </foreach>
        group by o.order_no
    </select>

    <select id="selectItem" resultType="net.summerfarm.model.vo.OrderItemVO">
        select id,sku,price * amount as itemAmount,order_no
        from order_item where order_no = #{orderNo}
    </select>

    <select id="selectMidByOrderNo" resultType="java.lang.Long">
        select m_id mId
        from orders
        where order_no = #{orderNo}
    </select>

    <select id="selectOrderTime" resultType="java.time.LocalDateTime">
        select order_time orderTime
        from orders
        where order_no = #{orderNo}
    </select>

    <select id="selectAddress" parameterType="string" resultType="net.summerfarm.model.vo.OrderItemVO">
        SELECT CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,o.order_time orderTime,dp.delivery_time deliveryTime,dep.finish_time finishTime,o.m_id mId
        FROM orders o
        LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
        left join contact c on c.contact_id = dp.contact_id
        LEFT JOIN delivery_path dep ON dp.contact_id=dep.contact_id AND dp.delivery_time=dep.delivery_time
        WHERE o.order_no = #{orderNo}
        limit 1
    </select>

    <select id="selectSaleLockQuantity" resultType="java.lang.Integer">
        select ifnull(sum(if(dp.quantity > 0, dp.quantity, oi.amount)),0) as amount
        from orders o
             left join order_item oi on o.order_no = oi.order_no
             left join delivery_plan dp on o.order_no = dp.order_no
             left join warehouse_inventory_mapping wim on oi.sku = wim.sku and dp.order_store_no = wim.store_no
        where dp.delivery_time > #{startDate} and
              oi.sku = #{sku} and
              wim.warehouse_no = #{warehouseNo}
          and oi.status in (1, 3, 6)
          and dp.status in (1, 3, 6);
    </select>

    <select id="selectSaleLockQuantityByStoreNo" resultType="java.lang.Integer">
        select ifnull(sum(if(dp.quantity > 0, dp.quantity, oi.amount)),0) as amount
        from orders o
             left join order_item oi on o.order_no = oi.order_no
             left join delivery_plan dp on o.order_no = dp.order_no
             left join warehouse_inventory_mapping wim on oi.sku = wim.sku and dp.order_store_no = wim.store_no
        where dp.delivery_time > #{startDate} and
              oi.sku = #{sku} and
              wim.store_no= #{storeNo}
          and oi.status in (1, 3, 6)
          and dp.status in (1, 3, 6);
    </select>

    <select id="selectSaleOrders" resultType="net.summerfarm.model.vo.OrderItemVO">
        select
        oi.id,
        ifnull(if(dp.quantity > 0, dp.quantity, oi.amount),0) as amount,
        (case m.size when '单店' then 0 when '小连锁' then 1 when '大连锁' then 2
        when '大客户' then 3  else 0 end) as customerType ,
        o.order_no orderNo,
        o.type,
        m.grade,
        o.order_time orderTime,
        m.m_id mId,
        dp.delivery_time deliveryTime
        from orders o
             left join order_item oi on o.order_no = oi.order_no
             left join delivery_plan dp on o.order_no = dp.order_no
             left join warehouse_inventory_mapping wim on oi.sku = wim.sku and dp.order_store_no = wim.store_no
             left join merchant m on m.m_id =o.m_id
        where dp.delivery_time > #{startDate} and
              oi.sku = #{sku} and
              wim.warehouse_no = #{warehouseNo}
          and oi.status in (1, 3, 6)
          and dp.status in (1, 3, 6)
    </select>

    <select id="selectSaleOrdersByStoreNo" resultType="net.summerfarm.model.vo.OrderItemVO">
        select
        oi.id,
        ifnull(if(dp.quantity > 0, dp.quantity, oi.amount),0) as amount,
        o.order_no orderNo,
        o.type,
        m.grade,
        o.order_time orderTime,
        m.m_id mId,
        m.mname ,
        m.phone ,
        dp.delivery_time deliveryTime,
        oi.actual_total_price actualTotalPrice,
        oi.sku
        from orders o
             left join order_item oi on o.order_no = oi.order_no
             left join delivery_plan dp on o.order_no = dp.order_no
             left join warehouse_inventory_mapping wim on oi.sku = wim.sku and dp.order_store_no = wim.store_no
             left join merchant m on m.m_id =o.m_id
        where dp.delivery_time > #{startDate} and
              oi.sku = #{sku} and
              wim.store_no = #{storeNo}
          and oi.status in (1, 3, 6)
          and dp.status in (1, 3, 6);
    </select>

    <select id="selectSkuNumByMid" resultType="integer">
        select count(distinct oi.sku) skuCount
        from  orders o
        INNER JOIN order_item oi on oi.order_no = o.order_no
        where m_id = #{mId} AND o.status in (2, 3, 6)
        and  o.order_time BETWEEN #{startTime} AND #{endTime}
        and sku != 'DF001TD0001'
    </select>

    <select id="selectOrderReminder" resultType="net.summerfarm.model.vo.OrderItemVO">
        SELECT oi.`status`,oi.amount,c.type,o.m_id mId,oi.pd_name pdName FROM orders o
            INNER JOIN order_item oi ON o.order_no = oi.order_no
            INNER JOIN category c ON oi.category_id = c.id
        WHERE oi.order_no = #{orderNo} AND oi.amount <![CDATA[ >= ]]> 50 AND o.`status` IN (1,2)
    </select>

    <select id="selectTotalQuantity" resultType="java.lang.Long">
        select IFNULL(sum(oi.amount),0) from orders o
        join order_item oi on o.order_no = oi.order_no
        where o.area_no = #{areaNo} and o.order_time BETWEEN #{startTime} AND #{endTime} and oi.sku = #{sku}
        and o.status in (2, 3, 6)
    </select>

    <update id="updateOrdersFinancial" parameterType="string">
        UPDATE orders
        SET invoice_status = 0
        WHERE order_no = #{orderNo}
    </update>
    <update id="updateOrderStatus">
        UPDATE orders
        SET
        status = #{status}

        WHERE type != 1 and order_no in
        <foreach collection="orderNos" separator="," open="(" close=")" item="orderNo">
            #{orderNo}
        </foreach>
    </update>

    <select id="getOrderInfoByOrderNos" resultType="net.summerfarm.model.vo.OrderInfoVo">
        SELECT
            o.order_no as orderNo,
            o.type ,
            o.order_time as orderTime,
            wlc.close_time as closeTime,
            dpa.path_status as pathStatus,
            dpa.id as deliveryPathId,
            dp.contact_id as contactId,
            dp.delivery_time as deliveryTime,
            o.area_no as areaNo,
            dp.order_store_no as storeNo,
            dp.id as deliveryPlanId
        FROM
            orders o
            LEFT JOIN delivery_plan dp ON o.order_no = dp.order_no
            LEFT JOIN delivery_path dpa ON dp.delivery_time = dpa.delivery_time
            AND dp.contact_id = dpa.contact_id
            LEFT JOIN warehouse_logistics_center wlc ON dp.order_store_no = wlc.store_no
        WHERE 1=1
        <if test="orderNos != null and orderNos.size != 0">
            and o.order_no IN
            <foreach collection="orderNos" separator="," open="(" close=")" item="orderNo">
                #{orderNo}
            </foreach>
        </if>
        <if test="noHeartDeliveryTime != null">
            and dp.delivery_time = #{noHeartDeliveryTime}
        </if>
        <if test="deliveryPlanId != null">
            and dp.id = #{deliveryPlanId}
        </if>
        and dp.intercept_flag = 0
        and dp.deliverytype = 0
        and (dpa.brand_type is null
        or dpa.brand_type= 0)
    </select>

    <select id="selectTimingOrderInfos" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.TimingOrderVO">
        SELECT o.order_no orderNo,o.m_id mId,o.order_time orderTime,o.type,o.`status`,m.mname merchantName,m.mcontact mContact,m.phone,oi.sku,oi.amount,p.pd_no pdNo, oi.pd_name pdName
        FROM order_item oi
          INNER JOIN orders o ON oi.order_no = o.order_no
          INNER JOIN merchant m ON o.m_id = m.m_id
          INNER JOIN inventory i ON oi.sku = i.sku
          INNER JOIN products p ON i.pd_id = p.pd_id
        WHERE o.order_no IN
        <foreach collection="orderNos" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectSassOtherOrder" parameterType="string" resultType="string">
        SELECT
            tdp.order_no
        FROM
            ( SELECT tdp.delivery_time, tdp.contact_id FROM tms_delivery_plan tdp WHERE tdp.order_no = #{orderNo} and type = 1) t
                LEFT JOIN tms_delivery_plan tdp ON t.contact_id = tdp.contact_id
                AND tdp.delivery_time = t.delivery_time
        WHERE
            tdp.`status` = 1
    </select>

    <select id="queryGmv" resultType="java.math.BigDecimal">
        select COALESCE(
        (select(case
        when pir.`id` is not null
        then SUM((pi.`prepay_price` / pi.`prepay_amount`) * pir.`amount`)
        when pir.`id` is null then SUM(oi.`original_price` * oi.`amount`)
        else 0 end) as gmv
        from orders o FORCE INDEX (orders_time_index)
        left join `order_item` oi on o.`order_no` = oi.`order_no`
        and oi.`status` not in (1, 10, 11)
        left join `area` ar on o.`area_no` = ar.`area_no`
        left join `prepay_inventory_record` pir on o.`order_no` = pir.`order_no`
        and oi.`sku` = pir.`sku`
        and pir.`valid` = 1
        and oi.`suit_id` = 0
        left join `prepay_inventory` pi on pir.`prepay_inventory_id` = pi.`id`
        <where>
            o.`status` not in (1, 10, 11)
            and o.`order_sale_type` = 0
            and ar.`parent_no` != 12
            <if test="startTime != null">
                AND o.order_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND o.order_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="areaNos != null">
                AND o.area_no in
                <foreach collection="areaNos.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataPermission != null and dataPermission.size!=0">
                AND o.area_no IN
                <foreach collection="dataPermission" item="item" open="(" close=")"  separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        )
        , 0) as gmv
    </select>

    <select id="queryPaymentGmv" resultType="java.math.BigDecimal">
        select coalesce(
        (select (case
        when pir.`id` is not null
        then SUM((pi.`prepay_price` / pi.`prepay_amount`) * pir.`amount`)
        when pir.`id` is null then SUM(oi.`price` * oi.`amount`)
        else 0 end) as gmv
        from orders o FORCE INDEX (orders_time_index)
        left join `order_item` oi on o.`order_no` = oi.`order_no` and oi.`status` not in (1, 8, 10, 11)
        left join `area` ar on o.`area_no` = ar.`area_no`
        left join `prepay_inventory_record` pir
        on o.`order_no` = pir.`order_no` and oi.`sku` = pir.`sku` and pir.`valid` = 1 and
        oi.`suit_id` = 0
        left join `prepay_inventory` pi on pir.`prepay_inventory_id` = pi.`id`
        <where>
            o.`status` in (2, 3, 6)
            and ar.`parent_no` != 12
            and o.order_sale_type = 0
            <if test="startTime != null">
                AND o.order_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND o.order_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="areaNos != null">
                AND o.area_no in
                <foreach collection="areaNos.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataPermission != null and dataPermission.size!=0">
                AND o.area_no IN
                <foreach collection="dataPermission" item="item" open="(" close=")"  separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        )
        , 0) as gmv;
    </select>

    <select id="selectOrderData" resultType="net.summerfarm.model.vo.OrderDataVO">
        SELECT order_no orderNo,order_time orderTime,total_price totalPrice,type
        FROM orders
        WHERE status in (2, 3, 6)
        AND m_id = #{mId}
        AND order_time BETWEEN #{startTime} AND #{endTime}
        order by order_id DESC
    </select>

    <select id="selectCouponDeliveryFeeByOrderNo" resultType="java.math.BigDecimal">
        SELECT IFNULL(c.money,0) money  FROM `order_delivery_record`  odr
        left join merchant_coupon mc
        on odr.`delivery_coupon_id`=mc.id
        left join `coupon` c
        on mc.`coupon_id` =c.id
        where odr.`order_no` =#{orderNo}
    </select>
    <select id="selectOrderByOrderNo" resultType="net.summerfarm.model.vo.OrderVO">
        select
        o.m_id mId,o.account_id accountId,
    o.order_no orderNo,  o.order_time orderTime, o.status,
    o.remark, o.type,o.total_price totalPrice,
    o.m_size mSize, o.total_price totalPrice ,
    o.out_stock outStock,
    o.order_sale_type orderSaleType,
    o.receivable_status receivableStatus,
    o.invoice_status invoiceStatus,
    o.financial_invoice_id financialInvoiceId,o.origin_price,o.type,o.delivery_fee deliveryFee
    from orders o
    where o.order_no = #{orderNo} and o.status in (2,3,6)
    </select>

    <select id="selectIsFruitByItemId" resultType="net.summerfarm.model.domain.FruitSales">
        select oi.id orderItemId, oi.sku sku, oi.amount * (-1) sales
        from order_item oi
                 left join orders o on o.order_no=oi.order_no
                 left join inventory i on oi.sku = i.sku
                 left join products p on i.pd_id = p.pd_id
                 inner join category c on p.category_id = c.id
        where c.type = 4 and oi.id = #{orderItemId}
    </select>
    <select id="selectMoneyByMId" resultType="java.lang.String">
        select IFNULL(sum(total_price),0)
        from orders
        where m_id = #{MId} and order_time <![CDATA[>=]]> #{beforeTime} and order_time <![CDATA[<]]> #{now}
        and status in (2,3,6,8)
    </select>
    <select id="selectOrderStatusLimit2" resultType="net.summerfarm.model.vo.OrderVO">
       SELECT order_no orderNo  FROM orders
        WHERE status in (2, 3, 6)
        AND m_id = #{mId}
        limit 2
    </select>

    <select id="queryByOrderNoList" resultType="net.summerfarm.model.domain.Orders">
        SELECT order_id orderId,order_no orderNo,type, status,order_time orderTime, selling_entity_name sellingEntityName
        FROM orders
        where order_no in
        <foreach collection="orderNoList" item="item" open="(" close=")"  separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByOrderNo" resultType="net.summerfarm.model.domain.Orders" parameterType="java.lang.String">
        SELECT area_no areaNo, type, m_id mId, m_size mSize,order_time orderTime
        FROM orders
        where order_no = #{orderNo}
    </select>
    <select id="selectOrdersByOrderNos" resultType="net.summerfarm.model.DTO.finance.FinanceOrderDTO">
        select os.`order_no` orderNo,ifnull(fior.create_time,os.order_time) invoiceTime,os.`total_price` billAmount,os.invoice_status invoiceStatus
        from orders os
        left join financial_invoice_orderno_relation fior on os.order_no=fior.order_no
        where os.order_no in
        <foreach collection="orderNos" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        group by os.`order_no`
    </select>
    <select id="selectDeliveryInfo" resultType="net.summerfarm.model.DTO.BillOrderDeliveryInfoDTO">
        SELECT o.order_no outerOrderId,c.`contact_id` outerContactId,dp.delivery_time expectBeginTime,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,
               o.m_id mId,m.mname mname,o.order_time orderTime,m.direct,o.status status
        FROM orders o
                 LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
                 left join contact c on c.contact_id = dp.contact_id
                left join  merchant m on m.m_id=o.m_id
        WHERE o.order_no in
        <foreach collection="orderNos" separator="," open="(" close=")" item="orderNo">
            #{orderNo}
        </foreach>
        group by o.order_no
    </select>
    <select id="selectExportOrder" resultType="net.summerfarm.model.vo.finance.AdminOrderExportVo">
        SELECT m.`mname`                                                                                                      mname
             , IF(order_pay_type = 1 or order_pay_type = 3, '账期', IF(order_pay_type = 2 or order_pay_type = 4, '现结', '')) settlementMethod
             , m.m_id                                                                                                         mId
             , os.`order_no`                                                                                                  orderNo
             , os.`order_time`                                                                                                orderTime
             , os.`status`                                                                                                    orderStatus
             , oi.`pd_name`                                                                                                   pdName
             , oi.`sku`
             , oi.`weight`
             , oi.id                                                                                                          orderItemId
             , oi.rebate_type                                                                                                 rebateType
             , rebate_number                                                                                                  rebateNumber
             , IF(i.`type` = 0, "自营", "代仓")                                                                               property
             , oi.`amount`                                                                                                    quantity
             , oi.original_price                                                                                              copeUnitPrice
             , oi.`price`                                                                                                     actualUnitPrice
             , oi.price * oi.amount                                                                                           actualPaidPrice
             , `delivery_fee`                                                                                                 deliveryFee
             , os.`remark`                                                                                                    orderRemark
        from orders os
                 LEFT JOIN `order_item` oi on oi.`order_no` = os.`order_no`
                 LEFT JOIN `merchant` m on os.`m_id` = m.`m_id`
                 LEFT JOIN `inventory` i on oi.`sku` = i.`sku`
        where os.`order_no` = #{orderNo}
          and oi.`status` !=8
    </select>
    <select id="selectMajorOrderNos" resultType="java.lang.String">
        SELECT o.order_no
        FROM orders o
                 INNER JOIN merchant m ON o.m_id=m.m_id
                 LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
        WHERE m.admin_id=#{majorAdminId} AND o.`status` in (2,3,6) AND o.m_size in('大客户','批发客户','普通客户') AND o.type !=10
        <if test="startTime != null">
            AND o.order_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND o.order_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="timeDimension !=null and timeDimension == 1">
            <if test="billStartTime != null">
                AND o.order_time >= #{billStartTime}
            </if>
            <if test="billEndTime != null">
                AND o.order_time <![CDATA[<=]]> #{billEndTime}
            </if>
        </if>
        <if test="timeDimension !=null and timeDimension == 2">
            <if test="billStartTime != null">
                AND dp.delivery_time >= #{billStartTime}
            </if>
            <if test="billEndTime != null">
                AND dp.delivery_time <![CDATA[<=]]> #{billEndTime}
            </if>
        </if>
        <if test="status != null ">
            AND o.status = #{status}
        </if>

        <if test="mId != null">
            AND o.m_id = #{mId}
        </if>
        <if test= "settlementMethod != null">
            AND o.direct = #{settlementMethod}
        </if>
        ORDER BY o.order_id DESC
    </select>
    <select id="countRebateOrderByOrderNos" resultType="java.lang.Integer">
        select count(*)
        from order_item
        where rebate_type is not null and rebate_number is not null and order_no in
        <foreach collection="orderNos" separator="," open="(" close=")" item="orderNo">
            #{orderNo}
        </foreach>
    </select>
    <select id="countMajorOrderNos" resultType="java.lang.Integer">
        SELECT count(*)
        FROM orders o
        INNER JOIN merchant m ON o.m_id=m.m_id
        LEFT JOIN delivery_plan dp ON o.order_no=dp.order_no
        WHERE m.admin_id=#{majorAdminId} AND o.`status` in (2,3,6) AND o.m_size in('大客户','批发客户','普通客户') AND o.type !=10
        <if test="startTime != null">
            AND o.order_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND o.order_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="timeDimension !=null and timeDimension == 1">
            <if test="billStartTime != null">
                AND o.order_time >= #{billStartTime}
            </if>
            <if test="billEndTime != null">
                AND o.order_time <![CDATA[<=]]> #{billEndTime}
            </if>
        </if>
        <if test="timeDimension !=null and timeDimension == 2">
            <if test="billStartTime != null">
                AND dp.delivery_time >= #{billStartTime}
            </if>
            <if test="billEndTime != null">
                AND dp.delivery_time <![CDATA[<=]]> #{billEndTime}
            </if>
        </if>
        <if test="status != null ">
            AND o.status = #{status}
        </if>

        <if test="mId != null">
            AND o.m_id = #{mId}
        </if>
        <if test= "settlementMethod != null">
            AND o.direct = #{settlementMethod}
        </if>
    </select>

    <select id="selectByOrderNoForMaster" resultType="net.summerfarm.model.domain.Orders" parameterType="java.lang.String">
        /*FORCE_MASTER*/
        SELECT area_no areaNo, type, m_id mId
        FROM orders
        where order_no = #{orderNo}
    </select>

    <select id="selectTimingOrderInfosNew" resultType="net.summerfarm.model.vo.TimingOrderVO">
        SELECT o.order_no orderNo,o.m_id mId,o.order_time orderTime,o.type,o.`status`,m.mname merchantName,m.mcontact mContact,m.phone,oi.sku,oi.amount, oi.pd_name pdName
        FROM order_item oi
        INNER JOIN orders o ON oi.order_no = o.order_no
        LEFT JOIN merchant m ON o.m_id = m.m_id
        WHERE o.type = 1 and o.order_no IN
        <foreach collection="orderNos" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTimingRefundOrder" resultType="java.time.LocalDate">
        select refund_time
        from timing_order_refund_time
        where order_no = #{orderNo}
    </select>

    <select id="selectTmsOuterClosingOrder" resultMap="ClosingOrderMap">
        SELECT
            tdo.id orderId,
            tdo.type deliveryType,
            TRUE showPrice,
            tdo.outer_order_id orderNo,
            tdse.`name` contact,
            tdo.outer_client_name userName,
            tdi.outer_item_name AS pd_name,
            tdo.outer_client_id AS m_id,
            tdo.expect_begin_time deliveryTime,
            tdse.phone phone,
            ifnull( tdse.area, '' ) area,
            CONCAT( tdse.provice, tdse.city, ifnull( tdse.area, '' ), tdse.address ) deliveryAddress,
            tdse.id endSiteId,
            tdi.quantity amount,
            tdi.unit,
            tdi.specification weight,
            tdi.weight weight_num,
            tdi.outer_item_id sku,
            tdi.volume,
            tdi.temperature storage_location,
            tdo.outer_contact_id AS contact_id,
            tdb.path_code path,
            IF
                ( tdb.path_id > 0, tdsite.sequence, NULL ) sort,
            tdo.outer_brand_name name_remakes,
            2 AS brandType,
            tdo.outer_tenant_id tenant_id,
            tdo.outer_client_id store_id,
            tdi.delivery_type orderItemDeliveryType,
            tdi.id,
            tdsite.outer_brand_name outerBrandName,
            tdsite.send_remark sendRemark,
            tdi.outer_item_price price,
            tdi.outer_item_price salePrice,
            tdi.pack_type
        FROM
            tms_dist_order tdo
                LEFT JOIN tms_dist_item tdi ON tdo.id = tdi.dist_order_id
                LEFT JOIN tms_dist_site tdse ON tdse.id = tdo.end_site_id
                LEFT JOIN tms_dist_site tdsb ON tdsb.id = tdo.begin_site_id
                LEFT JOIN tms_delivery_order tdeo ON tdeo.dist_order_id = tdo.id
                LEFT JOIN tms_delivery_batch tdb ON tdb.id = tdeo.batch_id
                LEFT JOIN tms_delivery_site tdsite ON tdsite.delivery_batch_id = tdeo.batch_id
                AND tdo.end_site_id = tdsite.site_id
        WHERE
            tdsb.out_business_no = #{storeNo}
          AND tdo.expect_begin_time = #{deliveryTime}
          AND tdo.state NOT IN ( 18, 19 )
          AND tdo.source = 220
    </select>


    <update id="updateOrderSizeForAuditSuccess">
        UPDATE orders
        set m_size = '单店'
        WHERE m_id= #{mid} and admin_id = #{adminId} and m_size = '大客户'
    </update>

    <select id="querySellingEntityByNos" resultType="java.lang.String">
        SELECT
            distinct o.selling_entity_name
        FROM
            orders o
        WHERE
            o.order_no IN
            <foreach collection="orderNos" item="orderNo" separator="," open="(" close=")">
                #{orderNo}
            </foreach>
    </select>
</mapper>
