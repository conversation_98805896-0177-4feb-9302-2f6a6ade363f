<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.InventoryRecordMapper">

    <insert id="insert" parameterType="net.summerfarm.model.domain.InventoryRecord">
        INSERT INTO inventory_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sku != null">
                sku,
            </if>
            <if test="changeField != null">
                change_field,
            </if>
            <if test="oldValue != null">
                old_value,
            </if>
            <if test="newValue != null">
                new_value,
            </if>
            <if test="recorder != null">
                recorder,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sku != null">
                #{sku} ,
            </if>
            <if test="changeField != null">
                #{changeField} ,
            </if>
            <if test="oldValue != null">
                #{oldValue} ,
            </if>
            <if test="newValue != null">
                #{newValue} ,
            </if>
            <if test="recorder != null">
                #{recorder} ,
            </if>
            <if test="addtime != null">
                #{addtime} ,
            </if>
        </trim>
    </insert>

    <select id="select" parameterType="net.summerfarm.model.vo.InventoryRecordVO"
            resultType="net.summerfarm.model.vo.InventoryRecordVO">
        SELECT ir.id, ir.sku, ir.change_field changeField, ir.old_value oldValue, ir.new_value newValue, ir.recorder, ir.addtime, i.weight, p.pd_name pdName
        FROM inventory_record ir
        LEFT JOIN inventory i ON ir.sku = i.sku
        LEFT JOIN products p ON i.pd_id = p.pd_id
        <where>
            <if test="sku != null">
                AND ir.sku = #{sku}
            </if>
            <if test="changeField != null">
                AND ir.change_field = #{changeField}
            </if>
            <if test="startTime != null">
                AND ir.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND ir.addtime <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        ORDER BY ir.addtime DESC
    </select>

</mapper>