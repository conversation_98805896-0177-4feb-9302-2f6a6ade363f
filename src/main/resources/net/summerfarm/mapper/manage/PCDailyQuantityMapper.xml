<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PCDailyQuantityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PCDailyQuantity">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="quantity_all" jdbcType="INTEGER" property="quantityAll" />
    <result column="quantity_threshold" jdbcType="INTEGER" property="quantityThreshold" />
    <result column="sale_time_length" jdbcType="REAL" property="saleTimeLength" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="max_threshold" jdbcType="INTEGER" property="maxThreshold"/>
  </resultMap>
  <insert id="insert" parameterType="net.summerfarm.model.domain.PCDailyQuantity">
    insert into pc_daily_quantity (id, store_no, sku, 
      date, quantity_all, quantity_threshold, 
      sale_time_length, create_time, max_threshold)
    values (#{id,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{date,jdbcType=DATE}, #{quantityAll,jdbcType=INTEGER}, #{quantityThreshold,jdbcType=INTEGER}, 
      #{saleTimeLength,jdbcType=REAL}, #{createTime,jdbcType=TIMESTAMP}, #{maxThreshold,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.PCDailyQuantity">
    insert into pc_daily_quantity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="quantityAll != null">
        quantity_all,
      </if>
      <if test="quantityThreshold != null">
        quantity_threshold,
      </if>
      <if test="saleTimeLength != null">
        sale_time_length,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="maxThreshold != null">
        max_threshold,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="quantityAll != null">
        #{quantityAll,jdbcType=INTEGER},
      </if>
      <if test="quantityThreshold != null">
        #{quantityThreshold,jdbcType=INTEGER},
      </if>
      <if test="saleTimeLength != null">
        #{saleTimeLength,jdbcType=REAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="maxThreshold != null">
        #{maxThreshold},
      </if>
    </trim>
  </insert>
  <delete id="delete">
    delete from pc_daily_quantity where store_no = #{storeNo} and `date` &gt;= #{startDate} and `date` &lt;= #{endDate}
  </delete>
  <select id="selectList" parameterType="net.summerfarm.model.vo.PCOrderQuery" resultMap="BaseResultMap">
    select id,
       store_no,
       sku,
       `date`,
       quantity_all,
       quantity_threshold,
       sale_time_length,
       create_time,
       max_threshold
    from pc_daily_quantity
    where store_no = #{storeNo} and sku = #{sku} and `date` &gt;= #{startDate} and `date` &lt;= #{endDate}
    order by `date`
  </select>
  <select id="selectAmountPerHour" resultType="int">
    select ifnull(ceil(sum(quantity_all) / sum(sale_time_length)), 0)
    from pc_daily_quantity
    where store_no = #{storeNo}
      and sku = #{sku}
      and date >= #{startDate}
      and date &lt; #{endDate}
  </select>
</mapper>