<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchasesConfigMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchasesConfig">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER" />
        <result column="area_manage_id" property="areaManageId" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="safe_level" property="safeLevel" jdbcType="INTEGER" />
        <result column="lead_time" property="leadTime" jdbcType="INTEGER" />
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="stock_rate" property="stockRate" jdbcType="DECIMAL" />
        <result column="cc_admin_id" property="ccAdminId" jdbcType="VARCHAR" />
        <result column="addtime" property="addtime"/>
        <result column="updatetime" property="updateTime" />
        <result column="cycle_time" property="cycleTime"/>
        <result column="calc_quantity" property="calcQuantity"/>
    </resultMap>

    <sql id="BaseColumn">
        id,type,area_no,area_manage_id,sku,status,safe_level,lead_time,supplier_id,stock_rate,cc_admin_id,addtime,updatetime,cycle_time,calc_quantity
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.PurchasesConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO purchases_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">
                type,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="areaManageId != null">
                area_manage_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="safeLevel != null">
                safe_level,
            </if>
            <if test="leadTime != null">
                lead_time,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="stockRate != null">
                stock_rate,
            </if>
            <if test="ccAdminId != null">
                cc_admin_id,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
            <if test="updateTime != null">
                updatetime,
            </if>
            <if test="cycleTime != null">
                cycle_time,
            </if>
            <if test="calcQuantity != null">
                calc_quantity,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">
                #{type},
            </if>
            <if test="areaNo != null">
                #{areaNo} ,
            </if>
            <if test="areaManageId != null">
                #{areaManageId} ,
            </if>
            <if test="sku != null">
                #{sku} ,
            </if>
            <if test="status != null">
                #{status} ,
            </if>
            <if test="safeLevel != null">
                #{safeLevel} ,
            </if>
            <if test="leadTime != null">
                #{leadTime} ,
            </if>
            <if test="supplierId != null">
                #{supplierId} ,
            </if>
            <if test="stockRate != null">
                #{stockRate} ,
            </if>
            <if test="ccAdminId != null">
                #{ccAdminId},
            </if>
            <if test="addtime != null">
                #{addtime} ,
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="cycleTime != null">
                #{cycleTime},
            </if>
            <if test="calcQuantity != null">
                #{calcQuantity},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.PurchasesConfig">
        INSERT INTO purchases_config(type,area_no,area_manage_id,sku,status,safe_level,lead_time,supplier_id,stock_rate,cc_admin_id,addtime,updatetime,cycle_time,calc_quantity)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.type},#{item.areaNo},#{item.areaManageId},#{item.sku},#{item.status},#{item.safeLevel},
            #{item.leadTime},#{item.supplierId},#{item.stockRate},#{item.ccAdminId},#{item.addtime},#{item.updateTime},#{item.cycleTime},#{item.calcQuantity})
        </foreach>
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM purchases_config
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer">
        DELETE
        FROM purchases_config
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <delete id="deleteBatch">
        DELETE
        FROM purchases_config
        WHERE id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="selectVO" parameterType="net.summerfarm.model.vo.PurchasesConfigVO"
            resultType="net.summerfarm.model.vo.PurchasesConfigVO">
        SELECT pc.id,pc.type,pc.area_no areaNo,pc.area_manage_id areaManageId,pc.sku,pc.`status`,pc.safe_level safeLevel,pc.lead_time leadTime,pc.cycle_time cycleTime,pc.calc_quantity calcQuantity,
          pc.supplier_id supplierId,pc.stock_rate stockRate,pc.cc_admin_id ccAdminId,pc.addtime,pc.updatetime updateTime,p.pd_name pdName,i.weight,gc.category, ad.name_remakes nameRemakes
        FROM purchases_config pc
        LEFT JOIN inventory i ON pc.sku=i.sku
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        left join category pc2 on c.parent_id = pc2.id
        left join category gc on pc2.parent_id = gc.id
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        <where>
            <if test="type != null">
                AND pc.type = #{type}
            </if>
            <if test="areaNo != null">
                AND pc.area_no = #{areaNo}
            </if>
            <if test="areaManageId != null">
                AND pc.area_manage_id = #{areaManageId}
            </if>
            <if test="sku != null">
                AND pc.sku = #{sku}
            </if>
            <if test="pdName != null">
                AND p.pd_name LIKE concat('%',#{pdName},'%')
            </if>
            <if test="categoryId != null">
                AND c.id = #{categoryId}
            </if>
            <if test="stockRate != null">
                AND pc.stock_rate = #{stockRate}
            </if>
            <if test="parentCategoryId != null">
                AND c.parent_id = #{parentCategoryId}
            </if>
            <if test="grandCategoryId != null">
                AND pc2.parent_id = #{grandCategoryId}
            </if>
            order by pc.addTime DESC
        </where>
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.PurchasesConfig">
        UPDATE purchases_config
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="safeLevel != null">
                safe_level = #{safeLevel},
            </if>
            <if test="leadTime != null">
                lead_time = #{leadTime},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="stockRate != null">
                stock_rate = #{stockRate},
            </if>
            <if test="ccAdminId != null">
                cc_admin_id = #{ccAdminId},
            </if>
            <if test="updateTime != null">
                updatetime = #{updateTime},
            </if>
            <if test="cycleTime != null">
                cycle_time = #{cycleTime},
            </if>
            <if test="calcQuantity != null">
                calc_quantity = #{calcQuantity}
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectVOList" parameterType="net.summerfarm.model.vo.PurchasesConfigVO" resultType="net.summerfarm.model.vo.PurchasesConfigVO">
        SELECT  pc.id,pc.type,pc.area_no areaNo,pc.area_manage_id areaManageId,pc.sku,pc.`status`,pc.safe_level safeLevel,pc.lead_time leadTime,pc.cycle_time cycle_time,pc.calc_quantity calcQuantity,
	      pc.supplier_id supplierId,pc.stock_rate stockRate,pc.addtime,pc.updatetime updateTime,p.pd_name pdName,i.weight,SUM(ar.quantity) quantity,
	      SUM(ar.safe_quantity) safeQuantity,SUM(ar.road_quantity) roadQuantity,
          ad.name_remakes nameRemakes,
        SUM(ar.lock_quantity) lockQuantity
        FROM purchases_config pc
        inner JOIN inventory i ON pc.sku=i.sku
        inner JOIN products p ON i.pd_id=p.pd_id
        inner JOIN area_store ar ON pc.sku=ar.sku
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        <where>
            <if test="type != null">
                AND pc.type = #{type}
            </if>
            <if test="areaNo != null">
                AND pc.area_no = #{areaNo}
            </if>
            <if test="areaManageId != null">
                AND pc.area_manage_id = #{areaManageId}
            </if>
            <if test="sku != null">
                AND pc.sku = #{sku}
            </if>
            <if test="pdName != null">
                AND p.pd_name LIKE concat('%',#{pdName},'%')
            </if>
            <if test="categoryId != null">
                AND p.category_id = #{categoryId}
            </if>
            <if test="status != null">
                AND pc.status = #{status}
            </if>
            <if test="areaNos != null and areaNos.size!= 0">
                AND ar.area_no IN
                <foreach collection="areaNos" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="onSale != null">
                <choose>
                    <when test="onSale">
                        AND pc.sku IN
                        (
                          SELECT DISTINCT ak.sku
                          FROM area_sku ak
                          left join warehouse_inventory_mapping wim on wim.sku = ak.sku
                          left join fence f on f.area_no = ak.area_no and wim.store_no = f.store_no
                          WHERE ak.on_sale = 1 and f.status = 0
                          <if test="areaNos != null and areaNos.size!= 0">
                              AND wim.warehouse_no IN
                              <foreach collection="areaNos" item="item" separator="," open="(" close=")">
                                  #{item}
                              </foreach>
                          </if>
                        )
                    </when>
                    <otherwise>
                        AND pc.sku NOT IN
                        (
                        SELECT DISTINCT ak.sku
                        FROM area_sku ak
                        left join warehouse_inventory_mapping wim on wim.sku = ak.sku
                        left join fence f on f.area_no = ak.area_no and wim.store_no = f.store_no
                        WHERE ak.on_sale = 1 and f.status = 0
                        <if test="areaNos != null and areaNos.size!= 0">
                            AND wim.warehouse_no IN
                            <foreach collection="areaNos" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                        </if>
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY pc.id
        ORDER BY pc.status DESC
    </select>

    <select id="selectOne" parameterType="net.summerfarm.model.domain.PurchasesConfig" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM purchases_config
        <where>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
            <if test="areaManageId != null">
                AND area_manage_id = #{areaManageId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
        </where>
      limit 1
    </select>

    <select id="select" parameterType="net.summerfarm.model.domain.PurchasesConfig" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM purchases_config
        <where>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
            <if test="areaManageId != null">
                AND area_manage_id = #{areaManageId}
            </if>
            <if test="supplierId != null">
                AND supplier_id = #{supplierId}
            </if>
        </where>
    </select>

    <select id="selectBySomeKey" resultType="net.summerfarm.model.domain.PurchasesConfig">
        SELECT pc.id,pc.type,pc.area_no areaNo,pc.area_manage_id areaManageId,pc.sku,pc.status,pc.safe_level safeLevel,pc.lead_time leadTime,pc.cycle_time cycleTime,pc.supplier_id supplierId,pc.calc_quantity calcQuantity,
        pc.stock_rate stockRate,pc.cc_admin_id ccAdminId,pc.addtime,pc.updatetime
        FROM purchases_config pc
        LEFT JOIN area_store ar ON pc.sku=ar.sku
        WHERE pc.type = #{type}
        AND ar.area_no = #{areaNo}
        <if test="areaManageId != null">
            AND pc.area_manage_id = #{areaManageId}
        </if>
    </select>

</mapper>