<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PriceAdjustmentMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PriceAdjustment" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="pd_name" property="pdName" jdbcType="VARCHAR" />
    <result column="original_cost_price" property="originalCostPrice" jdbcType="DECIMAL" />
    <result column="cost_price" property="costPrice" jdbcType="DECIMAL" />
    <result column="original_market_price" property="originalMarketPrice" jdbcType="DECIMAL" />
    <result column="market_price" property="marketPrice" jdbcType="DECIMAL" />
    <result column="up_time" property="upTime" jdbcType="TIMESTAMP" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="area_price_adjustment" property="areaPriceAdjustment" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_admin_name" property="createAdminName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, sku, pd_name, original_cost_price, cost_price, original_market_price, market_price, 
    up_time, reason, status, update_time, area_no, area_price_adjustment, create_admin_name
  </sql>

  <update id="update2outOfTime" parameterType="net.summerfarm.model.vo.PriceAdjustVO">
    UPDATE price_adjustment
    SET status = 6
    WHERE up_time <![CDATA[<=]]> #{upTimeEnd}
    AND status = #{priceStatus}
  </update>

  <select id="selectInFlow" resultType="java.lang.Integer">
     select count(1) from price_adjustment
     WHERE sku = #{sku} and area_no=#{areaNo}
     AND status in (1,2,3)
  </select>

  <select id="selectPriceAdjustVORecord" resultType="net.summerfarm.model.vo.PriceAdjustVO" parameterType="net.summerfarm.model.vo.PriceAdjustVO">
    select t.id, t.sku, t.pd_name pdName, t.cost_price costPrice, t.market_price marketPrice, t.status priceStatus, t.original_cost_price originalCostPrice,
    t.original_market_price originalMarketPrice, t.up_time upTime, t.reason, t.area_price_adjustment areaPriceAdjustment,
    t.area_no areaNo,a.name_remakes nameRemakes,i.ext_type extType, t.create_admin_name createAdminName
    FROM price_adjustment t
    LEFT JOIN inventory i on i.sku = t.sku
    LEFT JOIN admin a on a.admin_id = i.admin_id
    WHERE t.status in (4,5,6,7)
      <if test="priceStatus != null">
        AND t.status = #{priceStatus}
      </if>
      <if test="pdName != null">
        AND t.pd_name like concat('%',#{pdName},'%')
      </if>
      <if test="sku != null" >
        AND t.sku = #{sku}
      </if>
      <if test="areaNo != null" >
        AND t.area_no = #{areaNo}
      </if>
      <if test="upTimeStart != null">
        AND t.up_time >= #{upTimeStart}
      </if>
      <if test="upTimeEnd != null">
        AND t.up_time <![CDATA[<=]]> #{upTimeEnd}
      </if>
      <if test="upTime != null">
        AND t.up_time = #{upTime}
      </if>
    order by t.update_time desc
</select>


  <select id="selectPriceAdjustVO" resultType="net.summerfarm.model.vo.PriceAdjustVO" parameterType="net.summerfarm.model.vo.PriceAdjustVO">
    select t.id, t.sku, t.pd_name pdName, t.cost_price costPrice, t.market_price marketPrice, t.status priceStatus, t.original_cost_price originalCostPrice,
    t.original_market_price originalMarketPrice, t.up_time upTime, t.reason, t.area_price_adjustment areaPriceAdjustment,
    t.area_no areaNo,t.add_time addTime ,t.create_admin_name createAdminName
    FROM price_adjustment t
    <where>
      <if test="priceStatus != null">
        AND t.status = #{priceStatus}
      </if>
      <if test="pdName != null">
        AND t.pd_name = concat('%',#{pdName},'%')
      </if>
      <if test="sku != null" >
        AND t.sku = #{sku}
      </if>
      <if test="areaNo != null" >
        AND t.area_no = #{areaNo}
      </if>
      <if test="upTimeStart != null">
        AND t.up_time >= #{upTimeStart}
      </if>
      <if test="upTimeEnd != null">
        AND t.up_time <![CDATA[<=]]> #{upTimeEnd}
      </if>
      <if test="upTime != null">
        AND t.up_time = #{upTime}
      </if>
    </where>
  </select>

  <select id="selectCostAndMarketPrice" resultType="net.summerfarm.model.vo.CostAndMarketPriceVO">
    select cost_price costPrice, original_cost_price originalCostPrice, market_price marketPrice, original_market_price originalMarketPrice, add_time addTime from price_adjustment
    WHERE sku = #{sku}
    AND add_time >= #{startTime}
    AND add_time <![CDATA[<]]> #{endTime}

  </select>

  <select id="selectLastCostAndMarketPrice" resultType="net.summerfarm.model.vo.CostAndMarketPriceVO">
    select cost_price costPrice, original_cost_price originalCostPrice, market_price marketPrice, original_market_price originalMarketPrice, add_time addTime
    from price_adjustment
    WHERE sku = #{sku}
    AND add_time = (SELECT max(add_time)
    from price_adjustment
    WHERE sku = #{sku}
    AND add_time <![CDATA[<]]> #{startTime} )
  </select>

  <select id="selectNearlyCostAndMarketPrice" resultType="net.summerfarm.model.vo.CostAndMarketPriceVO">
    select cost_price costPrice, original_cost_price originalCostPrice, market_price marketPrice, original_market_price originalMarketPrice, add_time addTime
    from price_adjustment
    WHERE sku = #{sku}
    AND add_time = (SELECT min(add_time)
    from price_adjustment
    WHERE sku = #{sku}
    AND add_time >= #{endTime} )
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from price_adjustment
    where id = #{id,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="net.summerfarm.model.domain.PriceAdjustment" useGeneratedKeys="true" keyProperty="id">
    insert into price_adjustment (sku, pd_name,
      original_cost_price, cost_price, original_market_price, 
      market_price, up_time, reason, area_price_adjustment,
      status, update_time, add_time,area_no ,create_admin_name)
    values ( #{sku,jdbcType=VARCHAR}, #{pdName,jdbcType=VARCHAR},
      #{originalCostPrice,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL}, #{originalMarketPrice,jdbcType=DECIMAL}, 
      #{marketPrice,jdbcType=DECIMAL}, #{upTime,jdbcType=TIMESTAMP}, #{reason,jdbcType=VARCHAR}, #{areaPriceAdjustment},
      #{status,jdbcType=TINYINT}, now(), #{addTime},#{areaNo},#{createAdminName})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PriceAdjustment" >
    update price_adjustment
    <set >
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null" >
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="originalCostPrice != null" >
        original_cost_price = #{originalCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null" >
        cost_price = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalMarketPrice != null" >
        original_market_price = #{originalMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="marketPrice != null" >
        market_price = #{marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="upTime != null" >
        up_time = #{upTime},
      </if>
      <if test="reason != null" >
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="areaPriceAdjustment != null" >
        area_price_adjustment = #{areaPriceAdjustment,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PriceAdjustment" >
    update price_adjustment
    set sku = #{sku,jdbcType=VARCHAR},
      pd_name = #{pdName,jdbcType=VARCHAR},
      original_cost_price = #{originalCostPrice,jdbcType=DECIMAL},
      cost_price = #{costPrice,jdbcType=DECIMAL},
      original_market_price = #{originalMarketPrice,jdbcType=DECIMAL},
      market_price = #{marketPrice,jdbcType=DECIMAL},
      up_time = #{upTime,jdbcType=TIMESTAMP},
      reason = #{reason,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectNearlyPriceAdjust" resultType="net.summerfarm.model.domain.PriceAdjustment">
    select cost_price costPrice, original_cost_price originalCostPrice, market_price marketPrice, original_market_price originalMarketPrice, add_time addTime
    from price_adjustment
    WHERE sku = #{sku}
    AND area_no = #{storeNo}
    AND status = 4
    AND up_time = (SELECT min(up_time)
    from price_adjustment
    WHERE sku = #{sku}
    AND area_no = #{storeNo}
    AND up_time >= #{startTime}
    AND status = 4 )
  </select>
  <update id="update2cancel">
    update price_adjustment set status = 5 where status = 3 and area_no = #{storeNo} and sku = #{sku}
  </update>
  <select id="selectByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from price_adjustment where status = #{status}
  </select>


  <select id="selectPriceAdjustVO2" parameterType="net.summerfarm.model.vo.PriceAdjustVO"
          resultType="net.summerfarm.model.vo.PriceAdjustVO">
    select i.sku,
           p.pd_name                                      pdName,
           i.weight,
           ar.cost_price                                  costPrice,
           ar.market_price                                marketPrice,
           ak.price                                       price,
           ar.price_status                                priceStatus,
           a.large_area_no                                areaNo,
           a.area_no                                      cityNo,
           if(ifnull(ak.ladder_price, '[]') = '[]', 0, 1) ladderStatus,
           irc.create_admin_name                          createAdminName,
           ad.name_remakes                                nameRemakes,
           ak.on_sale                                     onSale,
           f.store_no                                     storeNo,
           i.ext_type                                     extType,
           i.sub_type                                     subType
    FROM area_sku ak
    LEFT JOIN area a on ak.area_no = a.area_no
    LEFT JOIN inventory i on i.sku = ak.sku
    Left Join (
    select * from fence where status = 0
    group by area_no
    ) f on f.area_no = a.area_no
    Left Join warehouse_inventory_mapping wim on wim.store_no = f.store_no and ak.sku = wim.sku
    left join area_store ar on ar.sku = wim.sku and ar.area_no = wim.warehouse_no
    LEFT JOIN products p on i.pd_id = p.pd_id
    LEFT JOIN interest_rate_config irc on a.area_no = irc.area_no and i.sku = irc.sku
    LEFT JOIN admin ad on ad.admin_id = i.admin_id
    left join category c on p.category_id = c.id
    left join category pc on c.parent_id = pc.id
    WHERE i.outdated = 0 and f.store_no is not null
    <if test="pdName != null">
      AND p.pd_name LIKE concat('%',#{pdName},'%')
    </if>
    <if test="sku != null">
      AND ar.sku = #{sku}
    </if>
    <if test="onSale != null">
      AND ak.on_sale = #{onSale}
    </if>
    <if test="areaNo != null">
      AND a.large_area_no = #{areaNo}
    </if>
    <if test="storeNo != null">
      AND f.store_no = #{storeNo}
    </if>
    <if test="cityNo != null">
      ANd a.area_no = #{cityNo}
    </if>
    <if test="categoryId != null">
      AND p.category_id = #{categoryId}
    </if>
    <if test="parentCategoryId != null">
      AND c.parent_id = #{parentCategoryId}
    </if>
    <if test="grandCategoryId != null">
      AND pc.parent_id = #{grandCategoryId}
    </if>
    <if test="categoryTypes != null">
      and c.type IN
      <foreach collection="categoryTypes" item="type" open="(" separator="," close=")">
        #{type}
      </foreach>
    </if>
    <if test="type != null">
      and i.type = #{type}
    </if>
    <if test="extType != null">
      and i.ext_type = #{extType}
    </if>
    <if test="subType != null">
      and i.sub_type = #{subType}
    </if>
    order by i.sku,cityNo
  </select>

  <select id="selectPriceAdjustVO3" parameterType="net.summerfarm.model.vo.PriceAdjustVO"
          resultType="net.summerfarm.model.vo.PriceAdjustVO">
    select i.sku,
    p.pd_name                                      pdName,
    i.weight,
    ar.cost_price                                  costPrice,
    ar.market_price                                marketPrice,
    ak.price                                       price,
    ar.price_status                                priceStatus,
    a.large_area_no                                areaNo,
    a.area_no                                      cityNo,
    if(ifnull(ak.ladder_price, '[]') = '[]', 0, 1) ladderStatus,
    irc.create_admin_name                          createAdminName,
    ad.name_remakes                                nameRemakes,
    ak.on_sale                                     onSale,
    f.store_no                                     storeNo,
    i.ext_type                                     extType,
    i.sub_type                                     subType,
    ak.m_type                                      mType
    FROM area_sku ak
    LEFT JOIN area a on ak.area_no = a.area_no
    LEFT JOIN inventory i on i.sku = ak.sku
    Left Join (
    select * from fence where status = 0
    group by area_no
    ) f on f.area_no = a.area_no
    Left Join warehouse_inventory_mapping wim on wim.store_no = f.store_no and ak.sku = wim.sku
    left join area_store ar on ar.sku = wim.sku and ar.area_no = wim.warehouse_no
    LEFT JOIN products p on i.pd_id = p.pd_id
    LEFT JOIN interest_rate_config irc on a.area_no = irc.area_no and i.sku = irc.sku
    LEFT JOIN admin ad on ad.admin_id = i.admin_id
    left join category c on p.category_id = c.id
    left join category pc on c.parent_id = pc.id
    WHERE i.outdated = 0 and f.store_no is not null and a.status = 1
    <if test="pdName != null">
      AND p.pd_name LIKE concat('%',#{pdName},'%')
    </if>
    <if test="sku != null">
      AND ar.sku = #{sku}
    </if>
    <if test="onSale != null">
      AND ak.on_sale = #{onSale}
    </if>
    <if test="areaNo != null">
      AND a.large_area_no = #{areaNo}
    </if>
    <if test="storeNo != null">
      AND f.store_no = #{storeNo}
    </if>
    <if test="cityNo != null">
      ANd a.area_no = #{cityNo}
    </if>
    <if test="categoryId != null">
      AND p.category_id = #{categoryId}
    </if>
    <if test="parentCategoryId != null">
      AND c.parent_id = #{parentCategoryId}
    </if>
    <if test="grandCategoryId != null">
      AND pc.parent_id = #{grandCategoryId}
    </if>
    <if test="categoryTypes != null">
      and c.type IN
      <foreach collection="categoryTypes" item="type" open="(" separator="," close=")">
        #{type}
      </foreach>
    </if>
    <if test="type != null">
      and i.type = #{type}
    </if>
    <if test="extType != null">
      and i.ext_type = #{extType}
    </if>
    <if test="subType != null">
      and i.sub_type = #{subType}
    </if>
    <if test="mType != null">
      and ak.m_type = #{mType}
    </if>
    order by i.sku,cityNo
  </select>

</mapper>