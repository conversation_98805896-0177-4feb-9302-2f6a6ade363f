<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SplitAreaDetailMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SplitAreaDetail">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="split_area_id" property="splitAreaId"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
    </resultMap>
    <sql id="base">
       id,`add_time` ,`update_time` ,`status` ,`split_area_id`,`province` ,`city`,`area`
    </sql>

    <insert id="saveSplitAreaDetail" parameterType="net.summerfarm.model.domain.SplitAreaDetail">
        insert split_area_detail ( `add_time` ,`update_time` ,`status` ,`split_area_id`,`province` ,`city`,`area`)
        value (now(),now(),#{status},#{splitAreaId},#{province},#{city},#{area})
    </insert>

    <update id="updateSplitAreaDetail" parameterType="net.summerfarm.model.domain.SplitAreaDetail">
        update split_area_detail
        <set>
            update_time = nuow(),
            <if test="status != null">
                status = #{status},
            </if>
            <if test="splitAreaId != null">
                split_area_id = #{areaNo},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="area != null">
                area = #{area},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveBathSplitAreaDetail" parameterType="net.summerfarm.model.domain.SplitAreaDetail">
        insert split_area_detail ( `add_time` ,`update_time` ,`status` ,`split_area_id`,`province` ,`city`,`area`)
        values
        <foreach collection="list" separator="," item="item">
            (now(),now(),#{item.status},#{item.splitAreaId},#{item.province},#{item.city},#{item.area})
        </foreach>
    </insert>

    <select id="selectDetailBySplitId" resultType="net.summerfarm.model.domain.SplitAreaDetail">
        select <include refid="base"/>
        from split_area_detail
        where split_area_id = #{splitAreaId} and status = 0
    </select>

    <update id="deleteDetailList">
        update split_area_detail
        set  status = 2
        where id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

</mapper>